name: Build and Deploy Multi-Architecture to Docker Hub

on:
  push:
    branches:
      - main
      - master
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Extract Docker Hub credentials
        run: |
          echo "DOCKERHUB_USERNAME=${{ vars.DOCKERHUB_USERNAME }}" >> $GITHUB_ENV
          echo "DOCKERHUB_TOKEN=${{ vars.DOCKERHUB_TOKEN }}" >> $GITHUB_ENV
      
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ env.DOCKERHUB_USERNAME }}
          password: ${{ env.DOCKERHUB_TOKEN }}
      
      - name: Create .env file from GitHub Variables
        env:
          VARS_CONTEXT: ${{ to<PERSON><PERSON>(vars) }}
        run: |
          echo "=== Creating .env file from GitHub Variables ==="
          echo "$VARS_CONTEXT" | jq -r 'keys[] as $k | "\($k)=\(.[$k])"' > .env
        shell: sh

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          platforms: linux/amd64,linux/arm64
          tags: ${{ env.DOCKERHUB_USERNAME }}/n8workshop:thinluong-web.latest,${{ env.DOCKERHUB_USERNAME }}/n8workshop:thinluong-web.${{ github.sha }}
          cache-from: type=registry,ref=${{ env.DOCKERHUB_USERNAME }}/n8workshop:thinluong-web.latest
          cache-to: type=inline
          build-args: |
            BUILDPLATFORM=linux/amd64