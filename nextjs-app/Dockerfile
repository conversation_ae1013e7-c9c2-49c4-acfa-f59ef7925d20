FROM --platform=$BUILDPLATFORM node:20-alpine AS base

WORKDIR /app

FROM base AS deps

RUN apk add --no-cache libc6-compat

COPY .env ./
COPY package.json package-lock.json* ./
RUN npm ci

FROM base AS builder
WORKDIR /app

RUN apk add --no-cache libc6-compat

COPY package.json package-lock.json* ./
COPY .env ./
RUN npm pkg set scripts.prepare=":"

COPY . .

ENV NEXT_TELEMETRY_DISABLED=1

ENV PRISMA_CLI_BINARY_TARGETS="linux-musl-openssl-3.0.x,linux-musl-arm64-openssl-3.0.x"
RUN npm ci && npm run prisma:generate

# Thêm cấu hình cho hình ảnh SVG
ENV NEXT_PUBLIC_DANGEROUSLY_ALLOW_SVG=true
ENV NEXT_PUBLIC_CONTENT_SECURITY_POLICY="default-src 'self'; img-src *;"

RUN npm run build

FROM --platform=$TARGETPLATFORM node:20-alpine AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
EXPOSE ${PORT}

RUN addgroup --system --gid 1001 nextjs && \
    adduser --system --uid 1001 nextjs

RUN apk update && apk add --no-cache bash

COPY .env ./
COPY prisma ./
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

RUN chown -R nextjs:nextjs /app

USER nextjs

CMD ["node", "server.js"]