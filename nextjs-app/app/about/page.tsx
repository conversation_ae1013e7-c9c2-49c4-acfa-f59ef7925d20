'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { Award, Users, Globe, Heart, Truck, Shield } from 'lucide-react';

export default function AboutPage() {
  const stats = [
    { number: '10+', label: 'Years Experience' },
    { number: '50K+', label: 'Happy Customers' },
    { number: '100+', label: 'Countries Served' },
    { number: '1000+', label: 'Products Sold' }
  ];

  const values = [
    {
      icon: <Award className="w-8 h-8" />,
      title: 'Quality First',
      description: 'We never compromise on quality. Every piece is carefully crafted with premium materials and attention to detail.'
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: 'Customer Focused',
      description: 'Our customers are at the heart of everything we do. We strive to exceed expectations with every interaction.'
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: 'Global Reach',
      description: 'From our headquarters to customers worldwide, we bring fashion to every corner of the globe.'
    },
    {
      icon: <Heart className="w-8 h-8" />,
      title: 'Sustainable Fashion',
      description: 'We believe in responsible fashion that respects both people and the planet.'
    }
  ];

  const features = [
    {
      icon: <Truck className="w-6 h-6" />,
      title: 'Free Worldwide Shipping',
      description: 'On orders over $100'
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: '30-Day Returns',
      description: 'Hassle-free returns'
    },
    {
      icon: <Award className="w-6 h-6" />,
      title: 'Premium Quality',
      description: 'Guaranteed satisfaction'
    }
  ];

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-gray-900 to-gray-700 text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              Về Nhà Thiết Kế Thời Trang
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed">
              Tạo nên vẻ đẹp vượt thời gian và phong cách hiện đại cho những người đam mê thời trang
            </p>
          </motion.div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-6">Câu Chuyện Của Chúng Tôi</h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  Được thành lập vào năm 2014, Nhà Thiết Kế Thời Trang bắt đầu như một cửa hàng nhỏ với sứ mệnh đơn giản:
                  mang đến thời trang chất lượng cao và phong cách cho mọi người. Từ một dự án đam mê, chúng tôi đã phát triển
                  thành một thương hiệu toàn cầu được tin tưởng bởi những người yêu thời trang trên khắp thế giới.
                </p>
                <p>
                  Hành trình của chúng tôi được thúc đẩy bởi cam kết không ngừng về tay nghề thủ công, sự đổi mới
                  và sự hài lòng của khách hàng. Chúng tôi tin rằng thời trang không chỉ là quần áo –
                  đó là một hình thức thể hiện bản thân giúp mọi người cảm thấy tự tin và xinh đẹp.
                </p>
                <p>
                  Ngày nay, chúng tôi tiếp tục đẩy mạnh ranh giới trong thiết kế trong khi vẫn trung thành với các giá trị cốt lõi
                  về chất lượng, tính bền vững và tính bao trùm. Mỗi sản phẩm trong bộ sưu tập của chúng tôi kể một
                  câu chuyện về sự cống hiến, sáng tạo và theo đuổi sự hoàn hảo.
                </p>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="relative"
            >
              <Image
                src="/images/collections/fashion-designer-featured-collection-1.jpg"
                alt="Our Story"
                width={600}
                height={400}
                className="rounded-lg shadow-lg object-cover"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Values</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The principles that guide everything we do
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center p-6 rounded-lg hover:shadow-lg transition-shadow duration-300"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4 text-gray-700">
                  {value.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{value.title}</h3>
                <p className="text-gray-600 leading-relaxed">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The creative minds behind our collections
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { name: 'Sarah Johnson', role: 'Creative Director', image: '/images/collections/fashion-designer-featured-collection-2.jpg' },
              { name: 'Michael Chen', role: 'Head of Design', image: '/images/collections/fashion-designer-featured-collection-3.jpg' },
              { name: 'Emma Williams', role: 'Sustainability Lead', image: '/images/collections/fashion-designer-featured-collection-4.jpg' }
            ].map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="relative mb-4">
                  <Image
                    src={member.image}
                    alt={member.name}
                    width={300}
                    height={300}
                    className="rounded-full mx-auto object-cover w-48 h-48"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-1">{member.name}</h3>
                <p className="text-gray-600">{member.role}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center w-12 h-12 bg-white text-gray-900 rounded-full mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-300">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </main>
  );
}
