'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Save,
  Upload,
  X,
  AlertCircle,
  Check,
  Loader2
} from 'lucide-react';
import AdminLayout from '../../../../../src/components/admin/AdminLayout';

interface Category {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  image: string | null;
  parentId: string | null;
  level: number;
  sortOrder: number;
  isActive: boolean;
  parent?: {
    id: string;
    name: string;
  } | null;
  _count: {
    products: number;
  };
}

interface FormData {
  name: string;
  slug: string;
  description: string;
  image: string;
  parentId: string;
  sortOrder: number;
  isActive: boolean;
}

interface FormErrors {
  name?: string;
  slug?: string;
  description?: string;
  image?: string;
  parentId?: string;
  sortOrder?: string;
}

export default function EditCategoryPage() {
  const router = useRouter();
  const params = useParams();
  const categoryId = params.id as string;

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [categories, setCategories] = useState<Category[]>([]);
  const [currentCategory, setCurrentCategory] = useState<Category | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [slugChecking, setSlugChecking] = useState(false);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);

  const [formData, setFormData] = useState<FormData>({
    name: '',
    slug: '',
    description: '',
    image: '',
    parentId: '',
    sortOrder: 0,
    isActive: true
  });

  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    if (categoryId) {
      fetchCategory();
      fetchCategories();
    }
  }, [categoryId]);

  // Check slug availability when slug changes (but not for initial load)
  useEffect(() => {
    if (formData.slug && formData.slug.length > 2 && currentCategory && formData.slug !== currentCategory.slug) {
      checkSlugAvailability(formData.slug);
    } else {
      setSlugAvailable(null);
    }
  }, [formData.slug, currentCategory]);

  const fetchCategory = async () => {
    try {
      const response = await fetch(`/api/admin/categories/${categoryId}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          const category = result.data;
          setCurrentCategory(category);
          setFormData({
            name: category.name,
            slug: category.slug,
            description: category.description || '',
            image: category.image || '',
            parentId: category.parentId || '',
            sortOrder: category.sortOrder,
            isActive: category.isActive
          });
          if (category.image) {
            setImagePreview(category.image);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching category:', error);
    } finally {
      setInitialLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories?limit=100', {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Filter out current category and its children to prevent circular references
          const filteredCategories = (result.data.data || []).filter((cat: Category) => 
            cat.id !== categoryId && !isChildCategory(cat.id, categoryId, result.data.data)
          );
          setCategories(filteredCategories);
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  // Helper function to check if a category is a child of another
  const isChildCategory = (categoryId: string, parentId: string, allCategories: Category[]): boolean => {
    const category = allCategories.find(cat => cat.id === categoryId);
    if (!category || !category.parentId) return false;
    if (category.parentId === parentId) return true;
    return isChildCategory(category.parentId, parentId, allCategories);
  };

  const generateSlug = (name: string): string => {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/đ/g, 'd')
      .replace(/Đ/g, 'D')
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim()
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
  };

  const checkSlugAvailability = async (slug: string) => {
    setSlugChecking(true);
    try {
      const response = await fetch(`/api/admin/categories/check-slug?slug=${encodeURIComponent(slug)}&excludeId=${categoryId}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        setSlugAvailable(result.available);
      }
    } catch (error) {
      console.error('Error checking slug:', error);
      setSlugAvailable(null);
    } finally {
      setSlugChecking(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));

    // Auto-generate slug from name if slug hasn't been manually edited
    if (name === 'name' && currentCategory && formData.slug === currentCategory.slug) {
      const newSlug = generateSlug(value);
      setFormData(prev => ({ ...prev, slug: newSlug }));
    }

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setErrors(prev => ({ ...prev, image: 'Vui lòng chọn file hình ảnh hợp lệ' }));
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, image: 'Kích thước file không được vượt quá 5MB' }));
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setImagePreview(result);
      setFormData(prev => ({ ...prev, image: result }));
      setErrors(prev => ({ ...prev, image: undefined }));
    };
    reader.readAsDataURL(file);
  };

  const removeImage = () => {
    setImagePreview('');
    setFormData(prev => ({ ...prev, image: '' }));
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên danh mục là bắt buộc';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Slug là bắt buộc';
    } else if (slugAvailable === false) {
      newErrors.slug = 'Slug đã tồn tại, vui lòng chọn slug khác';
    }

    if (formData.sortOrder < 0) {
      newErrors.sortOrder = 'Thứ tự sắp xếp phải là số không âm';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/admin/categories/${categoryId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          name: formData.name.trim(),
          slug: formData.slug.trim(),
          description: formData.description.trim() || null,
          image: formData.image || null,
          parentId: formData.parentId || null,
          sortOrder: formData.sortOrder,
          isActive: formData.isActive
        }),
      });

      const result = await response.json();

      if (result.success) {
        router.push('/admin/danh-muc?success=updated');
      } else {
        setErrors({ name: result.message || 'Có lỗi xảy ra khi cập nhật danh mục' });
      }
    } catch (error) {
      console.error('Error updating category:', error);
      setErrors({ name: 'Có lỗi xảy ra khi cập nhật danh mục' });
    } finally {
      setLoading(false);
    }
  };

  const renderParentCategories = (cats: Category[], level = 0): JSX.Element[] => {
    return cats
      .filter(cat => cat.level === level)
      .map(cat => [
        <option key={cat.id} value={cat.id}>
          {'—'.repeat(level)} {cat.name}
        </option>,
        ...renderParentCategories(cats.filter(c => c.parentId === cat.id), level + 1)
      ])
      .flat();
  };

  if (initialLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!currentCategory) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">Không tìm thấy danh mục</h2>
          <p className="mt-2 text-gray-600">Danh mục bạn đang tìm không tồn tại.</p>
          <button
            onClick={() => router.push('/admin/danh-muc')}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            Quay lại danh sách
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => router.back()}
              className="inline-flex items-center p-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              <ArrowLeft className="w-4 h-4" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Chỉnh Sửa Danh Mục</h1>
              <p className="mt-1 text-sm text-gray-500">
                Cập nhật thông tin danh mục "{currentCategory.name}"
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Thông Tin Cơ Bản</h2>
              </div>

              <div className="px-6 py-6 space-y-6">
                {/* Category Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Tên Danh Mục <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.name ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Nhập tên danh mục..."
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.name}
                    </p>
                  )}
                </div>

                {/* Slug */}
                <div>
                  <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-2">
                    Slug <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="slug"
                      name="slug"
                      value={formData.slug}
                      onChange={handleInputChange}
                      className={`block w-full border rounded-md px-3 py-2 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.slug ? 'border-red-300' :
                        slugAvailable === true ? 'border-green-300' :
                        slugAvailable === false ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="slug-danh-muc"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      {slugChecking ? (
                        <Loader2 className="w-4 h-4 text-gray-400 animate-spin" />
                      ) : slugAvailable === true ? (
                        <Check className="w-4 h-4 text-green-500" />
                      ) : slugAvailable === false ? (
                        <X className="w-4 h-4 text-red-500" />
                      ) : null}
                    </div>
                  </div>
                  {errors.slug && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.slug}
                    </p>
                  )}
                  {slugAvailable === true && (
                    <p className="mt-1 text-sm text-green-600 flex items-center">
                      <Check className="w-4 h-4 mr-1" />
                      Slug có thể sử dụng
                    </p>
                  )}
                </div>

                {/* Description */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                    Mô Tả
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={4}
                    value={formData.description}
                    onChange={handleInputChange}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Mô tả về danh mục này..."
                  />
                </div>
              </div>
            </div>

            {/* Image Upload */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Hình Ảnh Danh Mục</h2>
              </div>

              <div className="px-6 py-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hình Ảnh
                  </label>

                  {imagePreview ? (
                    <div className="relative inline-block">
                      <img
                        src={imagePreview}
                        alt="Preview"
                        className="w-32 h-32 object-cover rounded-lg border border-gray-300"
                      />
                      <button
                        type="button"
                        onClick={removeImage}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="mt-2">
                        <label htmlFor="image-upload" className="cursor-pointer">
                          <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                            Tải lên hình ảnh
                          </span>
                          <input
                            id="image-upload"
                            type="file"
                            accept="image/*"
                            onChange={handleImageUpload}
                            className="sr-only"
                          />
                        </label>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        PNG, JPG, GIF tối đa 5MB
                      </p>
                    </div>
                  )}

                  {errors.image && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.image}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Category Settings */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Cài Đặt Danh Mục</h2>
              </div>

              <div className="px-6 py-6 space-y-6">
                {/* Parent Category */}
                <div>
                  <label htmlFor="parentId" className="block text-sm font-medium text-gray-700 mb-2">
                    Danh Mục Cha
                  </label>
                  <select
                    id="parentId"
                    name="parentId"
                    value={formData.parentId}
                    onChange={handleInputChange}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">-- Không có danh mục cha --</option>
                    {renderParentCategories(categories)}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    Chọn danh mục cha để tạo cấu trúc phân cấp
                  </p>
                </div>

                {/* Sort Order */}
                <div>
                  <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 mb-2">
                    Thứ Tự Sắp Xếp
                  </label>
                  <input
                    type="number"
                    id="sortOrder"
                    name="sortOrder"
                    min="0"
                    value={formData.sortOrder}
                    onChange={handleInputChange}
                    className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.sortOrder ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="0"
                  />
                  {errors.sortOrder && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.sortOrder}
                    </p>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    Số thứ tự để sắp xếp danh mục (0 = đầu tiên)
                  </p>
                </div>

                {/* Active Status */}
                <div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isActive"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                      Kích hoạt danh mục
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Danh mục sẽ hiển thị trên website khi được kích hoạt
                  </p>
                </div>

                {/* Category Stats */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Thống Kê</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Số sản phẩm:</span>
                      <span className="ml-2 font-medium">{currentCategory._count.products}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Cấp độ:</span>
                      <span className="ml-2 font-medium">{currentCategory.level}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Hủy
              </button>
              <button
                type="submit"
                disabled={loading || slugChecking || slugAvailable === false}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Đang cập nhật...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Cập Nhật Danh Mục
                  </>
                )}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AdminLayout>
  );
}
