/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  ArrowLeft,
  Package,
  User,
  CreditCard,
  Calendar,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Save,
  RefreshCw,
} from 'lucide-react';
import AdminLayout from '../../../../src/components/admin/AdminLayout';
import { ToastProvider, useToast } from '../../../../src/components/admin/ToastContainer';
import OrderStatusFlow from '../../../../src/components/admin/OrderStatusFlow';
import OrderActions from '../../../../src/components/admin/OrderActions';

interface OrderItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  product: {
    id: string;
    name: string;
    images: string[];
    price: number;
    slug: string;
  };
}

interface Order {
  id: string;
  orderNumber: string;
  status: string;
  paymentStatus: string;
  paymentMethod: string;
  subtotal: number;
  shippingFee: number;
  tax: number;
  discount: number;
  total: number;
  currency: string;
  shippingAddress: any;
  trackingNumber?: string;
  estimatedDelivery?: string;
  deliveredAt?: string;
  adminNotes?: string;
  cancelReason?: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  orderItems: OrderItem[];
}

function OrderDetailPageContent() {
  const router = useRouter();
  const params = useParams();
  const orderId = params.id as string;
  const { showSuccess, showError } = useToast();

  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  // Form states
  const [status, setStatus] = useState('');
  const [trackingNumber, setTrackingNumber] = useState('');
  const [adminNotes, setAdminNotes] = useState('');
  const [cancelReason, setCancelReason] = useState('');
  const [estimatedDelivery, setEstimatedDelivery] = useState('');

  const fetchOrder = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/orders/${orderId}`);
      const data = await response.json();

      if (response.ok && data.success && data.data) {
        const orderData = data.data;
        setOrder(orderData);
        setStatus(orderData.status);
        setTrackingNumber(orderData.trackingNumber || '');
        setAdminNotes(orderData.adminNotes || '');
        setCancelReason(orderData.cancelReason || '');
        setEstimatedDelivery(
          orderData.estimatedDelivery 
            ? new Date(orderData.estimatedDelivery).toISOString().split('T')[0]
            : ''
        );
      } else {
        console.error('Failed to fetch order:', data);
        if (response.status === 404) {
          showError('Không tìm thấy đơn hàng', 'Đơn hàng có thể đã bị xóa hoặc không tồn tại');
        } else {
          showError('Lỗi tải dữ liệu', data.error || 'Có lỗi xảy ra khi tải thông tin đơn hàng');
        }
      }
    } catch (error) {
      console.error('Error fetching order:', error);
      showError('Lỗi hệ thống', 'Có lỗi xảy ra khi tải thông tin đơn hàng');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (orderId) {
      fetchOrder();
    }
  }, [orderId]);

  const handleUpdateOrder = async () => {
    try {
      setUpdating(true);
      
      const updateData = {
        status,
        trackingNumber: trackingNumber || null,
        adminNotes: adminNotes || null,
        cancelReason: status === 'CANCELLED' ? cancelReason : null,
        estimatedDelivery: estimatedDelivery || null,
      };

      const response = await fetch(`/api/admin/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();

      if (response.ok && data.success && data.data) {
        setOrder(data.data);
        showSuccess('Cập nhật thành công', data.message || 'Đã cập nhật đơn hàng thành công');
      } else {
        showError('Lỗi cập nhật', data.error || 'Có lỗi xảy ra khi cập nhật đơn hàng');
      }
    } catch (error) {
      console.error('Error updating order:', error);
      showError('Lỗi hệ thống', 'Có lỗi xảy ra khi cập nhật đơn hàng');
    } finally {
      setUpdating(false);
    }
  };



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'CONFIRMED':
        return 'bg-blue-100 text-blue-800';
      case 'PROCESSING':
        return 'bg-purple-100 text-purple-800';
      case 'SHIPPED':
        return 'bg-indigo-100 text-indigo-800';
      case 'DELIVERED':
        return 'bg-green-100 text-green-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Clock className="w-4 h-4" />;
      case 'CONFIRMED':
        return <CheckCircle className="w-4 h-4" />;
      case 'PROCESSING':
        return <Package className="w-4 h-4" />;
      case 'SHIPPED':
        return <Truck className="w-4 h-4" />;
      case 'DELIVERED':
        return <CheckCircle className="w-4 h-4" />;
      case 'CANCELLED':
        return <XCircle className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="flex items-center space-x-2">
            <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
            <span className="text-gray-600">Đang tải thông tin đơn hàng...</span>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!order) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center max-w-md mx-auto">
            <AlertCircle className="w-16 h-16 mx-auto mb-4 text-red-500" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Không tìm thấy đơn hàng</h2>
            <p className="text-gray-600 mb-6">
              Đơn hàng có thể đã bị xóa hoặc không tồn tại trong hệ thống.
            </p>
            <button
              onClick={() => router.push('/admin/don-hang')}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Quay lại danh sách đơn hàng
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!order) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Không tìm thấy đơn hàng
            </h2>
            <p className="text-gray-600 mb-4">
              Đơn hàng có thể đã bị xóa hoặc không tồn tại.
            </p>
            <button
              onClick={() => router.push('/admin/don-hang')}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Quay lại danh sách
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push('/admin/don-hang')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Quay lại
            </button>
            
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Chi tiết đơn hàng #{order.orderNumber}
              </h1>
              <p className="text-sm text-gray-500">
                Tạo lúc {formatDate(order.createdAt)}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <OrderStatusFlow currentStatus={order.status} />
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
              {getStatusIcon(order.status)}
              <span className="ml-2">
                {order.status === 'PENDING' && 'Chờ xử lý'}
                {order.status === 'CONFIRMED' && 'Đã xác nhận'}
                {order.status === 'PROCESSING' && 'Đang xử lý'}
                {order.status === 'SHIPPED' && 'Đã gửi hàng'}
                {order.status === 'DELIVERED' && 'Đã giao hàng'}
                {order.status === 'CANCELLED' && 'Đã hủy'}
              </span>
            </span>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <User className="w-5 h-5 text-blue-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Thông tin khách hàng</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tên khách hàng
                  </label>
                  <p className="text-sm text-gray-900">
                    {order.user.name || (order.shippingAddress ? (() => {
                      try {
                        return JSON.parse(order.shippingAddress).fullName;
                      } catch (e) {
                        return 'Chưa cập nhật';
                      }
                    })() : 'Chưa cập nhật')}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <p className="text-sm text-gray-900">
                    {order.user.email || (order.shippingAddress ? (() => {
                      try {
                        return JSON.parse(order.shippingAddress).email;
                      } catch (e) {
                        return 'Chưa cập nhật';
                      }
                    })() : 'Chưa cập nhật')}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Số điện thoại
                  </label>
                  <p className="text-sm text-gray-900">
                    {order.user.phone || (order.shippingAddress ? (() => {
                      try {
                        return JSON.parse(order.shippingAddress).phone;
                      } catch (e) {
                        return 'Chưa cập nhật';
                      }
                    })() : 'Chưa cập nhật')}
                  </p>
                </div>
              </div>

              {/* Shipping Address in Customer Info */}
              {order.shippingAddress && (() => {
                try {
                  const shippingAddr = JSON.parse(order.shippingAddress);
                  return (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Địa chỉ giao hàng
                      </label>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <p className="text-sm text-gray-900 font-medium mb-1">
                          {shippingAddr.address}
                        </p>
                        <p className="text-sm text-gray-600">
                          {shippingAddr.ward}, {shippingAddr.district}, {shippingAddr.province}
                        </p>
                      </div>
                    </div>
                  );
                } catch (e) {
                  return (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Địa chỉ giao hàng
                      </label>
                      <p className="text-sm text-gray-500 italic">
                        Thông tin địa chỉ không hợp lệ
                      </p>
                    </div>
                  );
                }
              })()}
            </div>



            {/* Order Items */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <Package className="w-5 h-5 text-purple-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Sản phẩm đặt hàng</h3>
              </div>

              <div className="space-y-4">
                {order.orderItems.map((item) => (
                  <div key={item.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                    <div className="flex-shrink-0">
                      {item.product.images && item.product.images.length > 0 ? (
                        <img
                          src={item.product.images[0]}
                          alt={item.product.name}
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                      ) : (
                        <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                          <Package className="w-6 h-6 text-gray-400" />
                        </div>
                      )}
                    </div>

                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{item.product.name}</h4>

                      {/* Product Category */}
                      {(item.product as any).category && (
                        <p className="text-xs text-gray-400 mb-1">
                          {(item.product as any).category.name}
                        </p>
                      )}

                      {/* Price Information */}
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">Đơn giá:</span>
                          <span className="text-sm font-medium text-gray-900">
                            {formatCurrency(item.unitPrice)}
                          </span>
                          {item.unitPrice !== item.product.price && (
                            <span className="text-xs text-gray-400 line-through">
                              {formatCurrency(item.product.price)}
                            </span>
                          )}
                        </div>

                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">Số lượng:</span>
                          <span className="text-sm text-gray-700">{item.quantity}</span>
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-gray-900">
                          {formatCurrency(item.totalPrice)}
                        </p>
                        <p className="text-xs text-gray-500">
                          {item.quantity} × {formatCurrency(item.unitPrice)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Payment Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <CreditCard className="w-5 h-5 text-orange-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Thông tin thanh toán</h3>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Tạm tính:</span>
                  <span className="text-sm text-gray-900">{formatCurrency(order.subtotal)}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Phí vận chuyển:</span>
                  <span className="text-sm text-gray-900">{formatCurrency(order.shippingFee)}</span>
                </div>

                {order.tax > 0 && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Thuế:</span>
                    <span className="text-sm text-gray-900">{formatCurrency(order.tax)}</span>
                  </div>
                )}

                {order.discount > 0 && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Giảm giá:</span>
                    <span className="text-sm text-red-600">-{formatCurrency(order.discount)}</span>
                  </div>
                )}

                <div className="border-t pt-3">
                  <div className="flex justify-between">
                    <span className="text-base font-semibold text-gray-900">Tổng cộng:</span>
                    <span className="text-base font-semibold text-gray-900">{formatCurrency(order.total)}</span>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Phương thức thanh toán:</span>
                    <span className="text-sm text-gray-900">
                      {order.paymentMethod === 'COD' && 'Thanh toán khi nhận hàng'}
                      {order.paymentMethod === 'BANK_TRANSFER' && 'Chuyển khoản ngân hàng'}
                      {order.paymentMethod === 'CREDIT_CARD' && 'Thẻ tín dụng'}
                      {order.paymentMethod === 'E_WALLET' && 'Ví điện tử'}
                    </span>
                  </div>

                  <div className="flex justify-between mt-2">
                    <span className="text-sm text-gray-600">Trạng thái thanh toán:</span>
                    <span className={`text-sm font-medium ${
                      order.paymentStatus === 'PAID' ? 'text-green-600' :
                      order.paymentStatus === 'PENDING' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {order.paymentStatus === 'PAID' && 'Đã thanh toán'}
                      {order.paymentStatus === 'PENDING' && 'Chờ thanh toán'}
                      {order.paymentStatus === 'FAILED' && 'Thanh toán thất bại'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Order Management */}
          <div className="space-y-6">
            {/* Order Status Management */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quản lý đơn hàng</h3>

              <div className="space-y-4">
                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Trạng thái đơn hàng
                  </label>
                  <select
                    value={status}
                    onChange={(e) => setStatus(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="PENDING">Chờ xử lý</option>
                    <option value="CONFIRMED">Đã xác nhận</option>
                    <option value="PROCESSING">Đang xử lý</option>
                    <option value="SHIPPED">Đã gửi hàng</option>
                    <option value="DELIVERED">Đã giao hàng</option>
                    <option value="CANCELLED">Đã hủy</option>
                  </select>
                </div>

                {/* Tracking Number */}
                {/* <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mã vận đơn
                  </label>
                  <input
                    type="text"
                    value={trackingNumber}
                    onChange={(e) => setTrackingNumber(e.target.value)}
                    placeholder="Nhập mã vận đơn"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div> */}

                {/* Estimated Delivery */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ngày giao hàng dự kiến
                  </label>
                  <input
                    type="date"
                    value={estimatedDelivery}
                    onChange={(e) => setEstimatedDelivery(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Admin Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ghi chú admin
                  </label>
                  <textarea
                    value={adminNotes}
                    onChange={(e) => setAdminNotes(e.target.value)}
                    placeholder="Ghi chú nội bộ cho đơn hàng này..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Cancel Reason (only show if status is CANCELLED) */}
                {status === 'CANCELLED' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Lý do hủy đơn
                    </label>
                    <textarea
                      value={cancelReason}
                      onChange={(e) => setCancelReason(e.target.value)}
                      placeholder="Nhập lý do hủy đơn hàng..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button
                    onClick={handleUpdateOrder}
                    disabled={updating}
                    className="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {updating ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Đang cập nhật...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Cập nhật đơn hàng
                      </>
                    )}
                  </button>

                  {/* Order Actions (Cancel/Delete) */}
                  <OrderActions
                    orderId={order.id}
                    orderNumber={order.orderNumber}
                    currentStatus={order.status}
                    onOrderUpdated={fetchOrder}
                  />
                </div>
              </div>
            </div>

            {/* Order Timeline */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <Calendar className="w-5 h-5 text-indigo-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Lịch sử đơn hàng</h3>
              </div>

              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                  <div>
                    <p className="text-gray-900 font-medium">Đơn hàng được tạo</p>
                    <p className="text-gray-500">{formatDate(order.createdAt)}</p>
                  </div>
                </div>

                {order.deliveredAt && (
                  <div className="flex items-center text-sm">
                    <div className="flex-shrink-0 w-2 h-2 bg-green-600 rounded-full mr-3"></div>
                    <div>
                      <p className="text-gray-900 font-medium">Đã giao hàng</p>
                      <p className="text-gray-500">{formatDate(order.deliveredAt)}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center text-sm">
                  <div className="flex-shrink-0 w-2 h-2 bg-gray-400 rounded-full mr-3"></div>
                  <div>
                    <p className="text-gray-900 font-medium">Cập nhật lần cuối</p>
                    <p className="text-gray-500">{formatDate(order.updatedAt)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </AdminLayout>
  );
}

export default function OrderDetailPage() {
  return (
    <ToastProvider>
      <OrderDetailPageContent />
    </ToastProvider>
  );
}
