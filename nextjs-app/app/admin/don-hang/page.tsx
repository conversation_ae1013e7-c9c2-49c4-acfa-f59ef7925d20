'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  Search,
  Filter,
  Download,
  Package,
  Clock,
  CheckCircle,
  Truck,
  XCircle,
  DollarSign,
  User,
  RefreshCw,
  ArrowRight,
} from 'lucide-react';
import AdminLayout from '../../../src/components/admin/AdminLayout';

interface StatusCount {
  status: string;
  _count: {
    status: number;
  };
}

interface Summary {
  totalRevenue: number;
  statusCounts?: StatusCount[];
}

interface Order {
  id: string;
  orderNumber: string;
  status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';
  paymentStatus: string;
  paymentMethod: string;
  total: number;
  subtotal: number;
  shippingFee: number;
  shippingAddress: string | object;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string | null;
    email: string;
    phone: string | null;
  };
  orderItems: Array<{
    id: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    product: {
      id: string;
      name: string;
      slug: string;
      images: string | string[];
      price: number;
    };
  }>;
}

type OrderStatus = 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';

interface ShippingAddress {
  name: string;
  phone: string;
  address: string;
  email?: string;
}

const statusConfig = {
  PENDING: {
    label: 'Chờ xử lý',
    color: 'bg-yellow-100 text-yellow-800',
    icon: <Clock className="w-4 h-4" />,
  },
  CONFIRMED: {
    label: 'Đã xác nhận',
    color: 'bg-blue-100 text-blue-800',
    icon: <CheckCircle className="w-4 h-4" />,
  },
  PROCESSING: {
    label: 'Đang xử lý',
    color: 'bg-indigo-100 text-indigo-800',
    icon: <Package className="w-4 h-4" />,
  },
  SHIPPED: {
    label: 'Đã gửi',
    color: 'bg-purple-100 text-purple-800',
    icon: <Truck className="w-4 h-4" />,
  },
  DELIVERED: {
    label: 'Đã giao',
    color: 'bg-green-100 text-green-800',
    icon: <CheckCircle className="w-4 h-4" />,
  },
  CANCELLED: {
    label: 'Đã hủy',
    color: 'bg-red-100 text-red-800',
    icon: <XCircle className="w-4 h-4" />,
  },
};

export default function AdminOrdersPage() {
  const router = useRouter();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [summary, setSummary] = useState<Summary | null>(null);

  useEffect(() => {
    fetchOrders();
  }, [currentPage, searchTerm, selectedStatus, sortBy, sortOrder, dateFrom, dateTo]);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        search: searchTerm,
        status: selectedStatus,
        sortBy,
        sortOrder,
      });

      if (dateFrom) params.append('dateFrom', dateFrom);
      if (dateTo) params.append('dateTo', dateTo);

      const response = await fetch(`/api/admin/orders?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.error('Unauthorized access - redirecting to login');
          // Redirect to admin login or show login modal
          window.location.href = '/admin/login';
          return;
        } else if (response.status === 403) {
          console.error('Forbidden - insufficient permissions');
          alert('Bạn không có quyền truy cập trang này');
          return;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setOrders(result.data.data);
        setTotalPages(result.data.pagination.totalPages);
        setTotal(result.data.pagination.total);
        setSummary(result.data.summary);
      } else {
        console.error('API returned error:', result.error);
        alert(result.error || 'Có lỗi xảy ra khi tải đơn hàng');
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      alert('Không thể kết nối đến server. Vui lòng thử lại sau.');
    } finally {
      setLoading(false);
    }
  };

  const viewOrderDetails = (orderId: string) => {
    router.push(`/admin/don-hang/${orderId}`);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const getProductImage = (images: string | string[]) => {
    try {
      const imageArray = Array.isArray(images)
        ? images
        : (images ? JSON.parse(images) : []);
      return imageArray.length > 0 ? imageArray[0] : '/images/placeholder.svg';
    } catch {
      return '/images/placeholder.svg';
    }
  };

  const getShippingAddress = (address: string | object): string => {
    try {
      if (typeof address === 'string') {
        const parsed = JSON.parse(address) as ShippingAddress;
        return `${parsed.name} - ${parsed.phone}\n${parsed.address}`;
      }
      const addr = address as ShippingAddress;
      return `${addr.name} - ${addr.phone}\n${addr.address}`;
    } catch {
      return String(address);
    }
  };



  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-8">
          <div>
            <div className="text-2xl font-bold text-gray-900">Quản Lý Đơn Hàng</div>
            <p className="mt-1 text-sm text-gray-500">
              Theo dõi và quản lý tất cả đơn hàng của khách hàng ({total} đơn hàng)
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            <button 
              onClick={fetchOrders}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Làm mới
            </button>
           
          </div>
        </div>

        {/* Summary Cards */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center">
                <DollarSign className="w-8 h-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Tổng doanh thu</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPrice(summary.totalRevenue)}
                  </p>
                </div>
              </div>
            </div>
            
            {summary.statusCounts?.map((statusData: StatusCount) => (
              <div key={statusData.status} className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex items-center">
                  {statusConfig[statusData.status as keyof typeof statusConfig]?.icon}
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">
                      {statusConfig[statusData.status as keyof typeof statusConfig]?.label}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {statusData._count.status}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Filters */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Tìm kiếm đơn hàng..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="flex gap-4 items-center">
                <select
                  className="block w-40 px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                >
                  <option value="">Trạng thái</option>
                  {Object.entries(statusConfig).map(([status, config]) => (
                    <option key={status} value={status}>
                      {config.label}
                    </option>
                  ))}
                </select>
                
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Bộ lọc
                </button>
              </div>
            </div>
            
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 pt-4 border-t border-gray-200"
              >
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Từ ngày
                    </label>
                    <input
                      type="date"
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      value={dateFrom}
                      onChange={(e) => setDateFrom(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Đến ngày
                    </label>
                    <input
                      type="date"
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      value={dateTo}
                      onChange={(e) => setDateTo(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Sắp xếp theo
                    </label>
                    <select
                      className="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                    >
                      <option value="createdAt">Ngày tạo</option>
                      <option value="total">Tổng tiền</option>
                      <option value="status">Trạng thái</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Thứ tự
                    </label>
                    <select
                      className="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      value={sortOrder}
                      onChange={(e) => setSortOrder(e.target.value)}
                    >
                      <option value="desc">Giảm dần</option>
                      <option value="asc">Tăng dần</option>
                    </select>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </div>

        {/* Orders Table */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {orders.length === 0 ? (
              <div className="text-center py-12">
                <Package className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Không có đơn hàng</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Chưa có đơn hàng nào phù hợp với bộ lọc của bạn.
                </p>
              </div>
            ) : (
              orders.map((order, index) => (
                <motion.li
                  key={order.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.05 }}
                  className="px-6 py-4 hover:bg-gray-50 cursor-pointer"
                  onClick={() => viewOrderDetails(order.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <User className="h-8 w-8 text-gray-400" />
                      </div>
                      <div className="ml-4">
                        <div className="flex items-center">
                          <p className="text-sm font-medium text-gray-900">
                            #{order.orderNumber}
                          </p>
                          <span className={`ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            statusConfig[order.status]?.color
                          }`}>
                            {statusConfig[order.status]?.icon}
                            <span className="ml-1">{statusConfig[order.status]?.label}</span>
                          </span>
                        </div>
                        <div className="mt-1 flex items-center text-sm text-gray-500">
                          <span>{order.user.name || order.user.email}</span>
                          <span className="mx-2">•</span>
                          <span>{formatDate(order.createdAt)}</span>
                          <span className="mx-2">•</span>
                          <span className="font-medium text-gray-900">
                            {formatPrice(order.total)}
                          </span>
                        </div>
                        <div className="mt-1 text-xs text-gray-500">
                          {order.orderItems.length} sản phẩm • {order.paymentMethod}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className="text-sm text-gray-500 flex items-center">
                        <span>Nhấp để xem chi tiết</span>
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </div>
                    </div>
                  </div>
                  
                  {/* Order Items Preview */}
                  <div className="mt-4">
                    <div className="flex space-x-2 overflow-x-auto">
                      {order.orderItems.slice(0, 3).map((item) => (
                        <div key={item.id} className="flex-shrink-0 flex items-center space-x-2 bg-gray-50 rounded-lg p-2">
                          <img
                            className="w-8 h-8 rounded object-cover"
                            src={getProductImage(item.product.images)}
                            alt={item.product.name}
                          />
                          <div>
                            <p className="text-xs font-medium text-gray-900 truncate max-w-24">
                              {item.productName}
                            </p>
                            <p className="text-xs text-gray-500">
                              {item.quantity}x {formatPrice(item.unitPrice)}
                            </p>
                          </div>
                        </div>
                      ))}
                      {order.orderItems.length > 3 && (
                        <div className="flex-shrink-0 flex items-center justify-center w-16 h-12 bg-gray-100 rounded-lg">
                          <span className="text-xs text-gray-500">
                            +{order.orderItems.length - 3}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.li>
              ))
            )}
          </ul>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Trước
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Sau
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Hiển thị trang <span className="font-medium">{currentPage}</span> của{' '}
                  <span className="font-medium">{totalPages}</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        page === currentPage
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          </div>
        )}


      </div>
    </AdminLayout>
  );
}
