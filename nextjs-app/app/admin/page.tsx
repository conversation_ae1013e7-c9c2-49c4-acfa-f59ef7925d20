'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Package,
  FolderOpen,
  ShoppingCart,
  Users,
  TrendingUp,
  TrendingDown,
  Eye,
} from 'lucide-react';
import AdminLayout from '../../src/components/admin/AdminLayout';
import DashboardSidebar from '../../src/components/admin/DashboardSidebar';

interface RecentOrder {
  id: string;
  orderNumber: string;
  total: number;
  status: string;
  customerName: string;
  customerEmail: string;
  createdAt: string;
}

interface TopProduct {
  id: string;
  name: string;
  price: number;
  images: string[];
  category: { name: string };
  totalSold: number;
  orderCount: number;
}

interface DashboardStats {
  totalProducts: number;
  totalCategories: number;
  totalOrders: number;
  totalCustomers: number;
  totalRevenue: number;
  lowStockProducts: number;
  monthlyOrders: number;
  monthlyRevenue: number;
  orderChange: number;
  revenueChange: number;
  recentOrders: RecentOrder[];
  topProducts: TopProduct[];
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/admin/dashboard/stats', {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setStats(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'PROCESSING': return 'bg-blue-100 text-blue-800';
      case 'SHIPPED': return 'bg-purple-100 text-purple-800';
      case 'DELIVERED': return 'bg-green-100 text-green-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING': return 'Chờ xử lý';
      case 'PROCESSING': return 'Đang xử lý';
      case 'SHIPPED': return 'Đã gửi';
      case 'DELIVERED': return 'Đã giao';
      case 'CANCELLED': return 'Đã hủy';
      default: return status;
    }
  };

  const statCards = [
    {
      name: 'Tổng Sản Phẩm',
      value: stats?.totalProducts || 0,
      icon: <Package className="w-6 h-6" />,
      color: 'bg-blue-500',
      change: `${stats?.totalProducts || 0}`,
      changeType: 'neutral',
    },
    {
      name: 'Danh Mục',
      value: stats?.totalCategories || 0,
      icon: <FolderOpen className="w-6 h-6" />,
      color: 'bg-green-500',
      change: `${stats?.totalCategories || 0}`,
      changeType: 'neutral',
    },
    {
      name: 'Đơn Hàng',
      value: stats?.totalOrders || 0,
      icon: <ShoppingCart className="w-6 h-6" />,
      color: 'bg-yellow-500',
      change: stats?.orderChange ? `${stats.orderChange > 0 ? '+' : ''}${stats.orderChange}%` : '0%',
      changeType: (stats?.orderChange || 0) >= 0 ? 'increase' : 'decrease',
    },
    {
      name: 'Khách Hàng',
      value: stats?.totalCustomers || 0,
      icon: <Users className="w-6 h-6" />,
      color: 'bg-purple-500',
      change: `${stats?.totalCustomers || 0}`,
      changeType: 'neutral',
    },
  ];

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col xl:flex-row gap-4 lg:gap-6">
          {/* Main Content */}
          <div className="flex-1 space-y-4 lg:space-y-8">
          {/* Header - Mobile Optimized */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex flex-col space-y-2">
              <div className="text-xl font-bold text-gray-900">
                Tổng Quan
              </div>
              <p className="text-sm text-gray-600">
                Bảng điều khiển quản trị
              </p>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-xs text-gray-500 space-y-2 sm:space-y-0">
                <span>Cập nhật: {new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}</span>
                {stats && (
                  <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-1 sm:space-y-0">
                    <span className="text-xs">Doanh thu tháng: {formatCurrency(stats.monthlyRevenue || 0)}</span>
                    {stats.lowStockProducts > 0 && (
                      <span className="text-red-600 font-medium text-xs">
                        ⚠️ {stats.lowStockProducts} SP sắp hết
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Stats Cards - Mobile Optimized */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-6">
            {statCards.map((card, index) => (
              <motion.div
                key={card.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-100"
              >
                <div className="p-3 lg:p-4">
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center justify-between">
                      <div className={`${card.color} p-2 rounded-lg text-white`}>
                        <div className="w-4 h-4">
                          {card.icon}
                        </div>
                      </div>
                      <div className={`flex items-center text-xs font-medium px-2 py-1 rounded-full ${
                        card.changeType === 'increase'
                          ? 'text-green-700 bg-green-100'
                          : card.changeType === 'decrease'
                          ? 'text-red-700 bg-red-100'
                          : 'text-gray-700 bg-gray-100'
                      }`}>
                        {card.changeType === 'increase' ? (
                          <TrendingUp className="w-3 h-3 mr-1" />
                        ) : card.changeType === 'decrease' ? (
                          <TrendingDown className="w-3 h-3 mr-1" />
                        ) : null}
                        {card.change}
                      </div>
                    </div>
                    <div>
                      <div className="text-lg lg:text-2xl font-bold text-gray-900">
                        {card.value.toLocaleString()}
                      </div>
                      <div className="text-xs lg:text-sm font-medium text-gray-500 truncate">
                        {card.name}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Revenue and Stock Alert Cards */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Monthly Revenue Card */}
              <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">Doanh thu tháng này</p>
                    <p className="text-2xl font-bold">{formatCurrency(stats.monthlyRevenue || 0)}</p>
                    <p className="text-green-100 text-xs mt-1">
                      {stats.revenueChange >= 0 ? '+' : ''}{stats.revenueChange}% so với tháng trước
                    </p>
                  </div>
                  <div className="text-green-200">
                    <TrendingUp className="w-8 h-8" />
                  </div>
                </div>
              </div>

              {/* Low Stock Alert Card */}
              <div className={`rounded-lg p-4 text-white ${
                stats.lowStockProducts > 0
                  ? 'bg-gradient-to-r from-red-500 to-red-600'
                  : 'bg-gradient-to-r from-blue-500 to-blue-600'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm ${stats.lowStockProducts > 0 ? 'text-red-100' : 'text-blue-100'}`}>
                      Tồn kho
                    </p>
                    <p className="text-2xl font-bold">
                      {stats.lowStockProducts > 0 ? stats.lowStockProducts : '✓'}
                    </p>
                    <p className={`text-xs mt-1 ${stats.lowStockProducts > 0 ? 'text-red-100' : 'text-blue-100'}`}>
                      {stats.lowStockProducts > 0 ? 'Sản phẩm sắp hết hàng' : 'Tồn kho ổn định'}
                    </p>
                  </div>
                  <div className={stats.lowStockProducts > 0 ? 'text-red-200' : 'text-blue-200'}>
                    <Package className="w-8 h-8" />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Charts and Tables - Mobile Optimized */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Recent Orders */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="bg-white shadow-sm rounded-lg border border-gray-100"
            >
              <div className="px-4 py-3 border-b border-gray-100">
                <h3 className="text-base font-semibold text-gray-900">Đơn Hàng Gần Đây</h3>
                <p className="text-xs text-gray-500 mt-1">5 đơn hàng mới nhất</p>
              </div>
              <div className="p-4">
                <div className="space-y-3">
                  {stats?.recentOrders && stats.recentOrders.length > 0 ? (
                    stats.recentOrders.map((order) => (
                      <div key={order.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <ShoppingCart className="w-4 h-4 text-gray-500" />
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">
                              {order.orderNumber}
                            </p>
                            <p className="text-xs text-gray-500">{order.customerName}</p>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                              {getStatusText(order.status)}
                            </span>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">
                            {formatCurrency(order.total)}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(order.createdAt).toLocaleDateString('vi-VN')}
                          </p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <ShoppingCart className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-sm text-gray-500">Chưa có đơn hàng nào</p>
                    </div>
                  )}
                </div>
                <div className="mt-6">
                  <a
                    href="/admin/don-hang"
                    className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 px-4 py-2 rounded-lg transition-colors"
                  >
                    Xem tất cả đơn hàng →
                  </a>
                </div>
              </div>
            </motion.div>

            {/* Top Products */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="bg-white shadow-sm rounded-lg border border-gray-100"
            >
              <div className="px-4 py-3 border-b border-gray-100">
                <h3 className="text-base font-semibold text-gray-900">Sản Phẩm Bán Chạy</h3>
                <p className="text-xs text-gray-500 mt-1">Top 5 sản phẩm</p>
              </div>
              <div className="p-4">
                <div className="space-y-3">
                  {stats?.topProducts && stats.topProducts.length > 0 ? (
                    stats.topProducts.map((product) => (
                      <div key={product.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                            {product.images && product.images.length > 0 ? (
                              <img
                                src={product.images[0]}
                                alt={product.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <Package className="w-4 h-4 text-gray-500" />
                            )}
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900 truncate max-w-[120px]">
                              {product.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {product.totalSold} đã bán
                            </p>
                            <p className="text-xs text-gray-400">
                              {product.category?.name}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">
                            {formatCurrency(product.price)}
                          </p>
                          <div className="flex items-center text-xs text-green-600">
                            <TrendingUp className="w-3 h-3 mr-1" />
                            {product.orderCount} đơn
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <Package className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-sm text-gray-500">Chưa có dữ liệu bán hàng</p>
                    </div>
                  )}
                </div>
                <div className="mt-6">
                  <a
                    href="/admin/san-pham"
                    className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 px-4 py-2 rounded-lg transition-colors"
                  >
                    Xem tất cả sản phẩm →
                  </a>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Quick Actions - Mobile Optimized */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="bg-white shadow-sm rounded-lg border border-gray-100"
          >
            <div className="px-4 py-3 border-b border-gray-100">
              <h3 className="text-base font-semibold text-gray-900">Thao Tác Nhanh</h3>
              <p className="text-xs text-gray-500 mt-1">Các chức năng thường dùng</p>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                <a
                  href="/admin/san-pham/them-moi"
                  className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                >
                  <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center text-white mb-2">
                    <Package className="w-5 h-5" />
                  </div>
                  <p className="text-sm font-medium text-gray-900 text-center">Thêm SP</p>
                </a>
                <a
                  href="/admin/danh-muc/them-moi"
                  className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors"
                >
                  <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center text-white mb-2">
                    <FolderOpen className="w-5 h-5" />
                  </div>
                  <p className="text-sm font-medium text-gray-900 text-center">Danh Mục</p>
                </a>
                <a
                  href="/admin/don-hang"
                  className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
                >
                  <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center text-white mb-2">
                    <Eye className="w-5 h-5" />
                  </div>
                  <p className="text-sm font-medium text-gray-900 text-center">Đơn Hàng</p>
                </a>
              </div>
            </div>
          </motion.div>
        </div>

          {/* Dashboard Sidebar - Only show on desktop */}
          <div className="hidden xl:block w-72 xl:w-80">
            <DashboardSidebar />
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
