'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Save,
  Upload,
  X,
  AlertCircle,
  Check,
  Loader2,
  Plus,
  Trash2,
  Image as ImageIcon
} from 'lucide-react';
import AdminLayout from '../../../../../src/components/admin/AdminLayout';
import { useAdminToast } from '../../../../../src/contexts/AdminToastContext';

interface Category {
  id: string;
  name: string;
  slug: string;
  level: number;
  parentId: string | null;
  children?: Category[];
}

interface ProductVariant {
  id: string;
  name: string;
  value: string;
  price?: number;
  stock?: number;
}

interface Product {
  id: string;
  name: string;
  slug: string;
  shortDescription: string | null;
  description: string | null;
  images: string[];
  categoryId: string;
  brand: string | null;
  sku: string;
  price: number;
  originalPrice: number | null;
  stock: number;
  variants: ProductVariant[];
  tags: string[];
  featured: boolean;
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT';
  seoTitle: string | null;
  seoDescription: string | null;
  seoKeywords: string | null;
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  _count: {
    reviews: number;
    wishlistItems: number;
    cartItems: number;
  };
}

interface FormData {
  name: string;
  slug: string;
  shortDescription: string;
  description: string;
  images: string[];
  categoryId: string;
  brand: string;
  sku: string;
  price: number;
  originalPrice: number;
  stock: number;
  variants: ProductVariant[];
  tags: string;
  featured: boolean;
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT';
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
}

interface FormErrors {
  [key: string]: string;
}

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [categories, setCategories] = useState<Category[]>([]);
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [slugChecking, setSlugChecking] = useState(false);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const [skuChecking, setSkuChecking] = useState(false);
  const [skuAvailable, setSkuAvailable] = useState<boolean | null>(null);

  const [formData, setFormData] = useState<FormData>({
    name: '',
    slug: '',
    shortDescription: '',
    description: '',
    images: [],
    categoryId: '',
    brand: '',
    sku: '',
    price: 0,
    originalPrice: 0,
    stock: 0,
    variants: [],
    tags: '',
    featured: false,
    status: 'ACTIVE',
    seoTitle: '',
    seoDescription: '',
    seoKeywords: ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const { showToast } = useAdminToast();

  useEffect(() => {
    if (productId) {
      fetchProduct();
      fetchCategories();
    }
  }, [productId]);

  // Check slug availability when slug changes (but not for initial load)
  useEffect(() => {
    if (formData.slug && formData.slug.length > 2 && currentProduct && formData.slug !== currentProduct.slug) {
      checkSlugAvailability(formData.slug);
    } else {
      setSlugAvailable(null);
    }
  }, [formData.slug, currentProduct]);

  // Check SKU availability when SKU changes (but not for initial load)
  useEffect(() => {
    if (formData.sku && formData.sku.length > 2 && currentProduct && formData.sku !== currentProduct.sku) {
      checkSKUAvailability(formData.sku);
    } else {
      setSkuAvailable(null);
    }
  }, [formData.sku, currentProduct]);

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/admin/products/${productId}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          const product = result.data;
          setCurrentProduct(product);
          setFormData({
            name: product.name,
            slug: product.slug,
            shortDescription: product.shortDescription || '',
            description: product.description || '',
            images: product.images || [],
            categoryId: product.categoryId,
            brand: product.brand || '',
            sku: product.sku,
            price: product.price,
            originalPrice: product.originalPrice || 0,
            stock: product.stock,
            variants: product.variants || [],
            tags: Array.isArray(product.tags) ? product.tags.join(', ') : '',
            featured: product.featured,
            status: product.status,
            seoTitle: product.seoTitle || '',
            seoDescription: product.seoDescription || '',
            seoKeywords: product.seoKeywords || ''
          });
          if (product.images && product.images.length > 0) {
            setImagePreviews(product.images);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching product:', error);
    } finally {
      setInitialLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories?limit=100', {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCategories(result.data.data || []);
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const generateSlug = (name: string): string => {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/đ/g, 'd')
      .replace(/Đ/g, 'D')
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim()
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
  };

  const checkSlugAvailability = async (slug: string) => {
    setSlugChecking(true);
    try {
      const response = await fetch(`/api/admin/products/check-slug?slug=${encodeURIComponent(slug)}&excludeId=${productId}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        setSlugAvailable(result.available);
      }
    } catch (error) {
      console.error('Error checking slug:', error);
      setSlugAvailable(null);
    } finally {
      setSlugChecking(false);
    }
  };

  const checkSKUAvailability = async (sku: string) => {
    setSkuChecking(true);
    try {
      const response = await fetch(`/api/admin/products/check-sku?sku=${encodeURIComponent(sku)}&excludeId=${productId}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        setSkuAvailable(result.available);
      }
    } catch (error) {
      console.error('Error checking SKU:', error);
      setSkuAvailable(null);
    } finally {
      setSkuChecking(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    let processedValue: any = value;
    
    // Handle numeric fields that are now text inputs
    if (name === 'price' || name === 'originalPrice' || name === 'stock') {
      // Remove non-numeric characters except decimal point
      const numericValue = value.replace(/[^0-9.]/g, '');
      processedValue = numericValue === '' ? 0 : parseFloat(numericValue) || 0;
    } else if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Auto-generate slug from name if slug hasn't been manually edited
    if (name === 'name' && currentProduct && formData.slug === currentProduct.slug) {
      const newSlug = generateSlug(value);
      setFormData(prev => ({ ...prev, slug: newSlug }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên sản phẩm là bắt buộc';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Slug là bắt buộc';
    } else if (slugAvailable === false) {
      newErrors.slug = 'Slug đã tồn tại, vui lòng chọn slug khác';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU là bắt buộc';
    } else if (skuAvailable === false) {
      newErrors.sku = 'SKU đã tồn tại, vui lòng chọn SKU khác';
    }

    if (!formData.categoryId) {
      newErrors.categoryId = 'Danh mục là bắt buộc';
    }

    if (formData.price <= 0) {
      newErrors.price = 'Giá phải lớn hơn 0';
    }

    if (formData.originalPrice && formData.originalPrice > 0 && formData.originalPrice <= formData.price) {
      newErrors.originalPrice = 'Giá gốc phải lớn hơn giá bán';
    }

    if (formData.stock < 0) {
      newErrors.stock = 'Số lượng tồn kho không được âm';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/admin/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        if (result.success) {
          showToast('Cập nhật sản phẩm thành công', 'success', 'edit');
          router.push('/admin/san-pham?success=updated');
        } else {
          showToast(result.message || 'Lỗi khi cập nhật sản phẩm', 'error');
        }
      } else {
        showToast(result.message || result.error || `Lỗi server: ${response.status}`, 'error');
      }
    } catch (error) {
      console.error('Error updating product:', error);
      showToast('Lỗi kết nối khi cập nhật sản phẩm', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      // Check file size and type
      const maxSize = 5 * 1024 * 1024; // 5MB
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      
      const validFiles = Array.from(files).filter(file => {
        if (file.size > maxSize) {
          alert(`File ${file.name} quá lớn. Kích thước tối đa là 5MB.`);
          return false;
        }
        if (!allowedTypes.includes(file.type)) {
          alert(`File ${file.name} không đúng định dạng. Chỉ chấp nhận JPG, PNG, WEBP.`);
          return false;
        }
        return true;
      });

      if (validFiles.length > 0) {
        // For now, we'll create object URLs for preview
        // In production, you would upload to a cloud storage service
        const newImages = validFiles.map(file => URL.createObjectURL(file));
        setFormData(prev => ({
          ...prev,
          images: [...prev.images, ...newImages]
        }));
      }
    }
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  if (initialLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!currentProduct) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">Không tìm thấy sản phẩm</h2>
          <p className="mt-2 text-gray-600">Sản phẩm bạn đang tìm không tồn tại.</p>
          <button
            onClick={() => router.push('/admin/san-pham')}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            Quay lại danh sách
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => router.back()}
              className="inline-flex items-center p-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              <ArrowLeft className="w-4 h-4" />
            </button>
            <div>
              <div className="text-2xl font-bold text-gray-900">Chỉnh Sửa Sản Phẩm</div>
              <p className="mt-1 text-sm text-gray-500">
                Cập nhật thông tin sản phẩm "{currentProduct.name}"
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Basic Information */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Thông Tin Cơ Bản</h2>
              </div>

              <div className="px-6 py-6 space-y-6">
                {/* Product Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Tên Sản Phẩm <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.name ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Nhập tên sản phẩm..."
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.name}
                    </p>
                  )}
                </div>

                {/* Slug */}
                <div>
                  <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-2">
                    Slug <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="slug"
                      name="slug"
                      value={formData.slug}
                      onChange={handleInputChange}
                      className={`block w-full border rounded-md px-3 py-2 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.slug ? 'border-red-300' :
                        slugAvailable === true ? 'border-green-300' :
                        slugAvailable === false ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="slug-san-pham"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      {slugChecking ? (
                        <Loader2 className="w-4 h-4 text-gray-400 animate-spin" />
                      ) : slugAvailable === true ? (
                        <Check className="w-4 h-4 text-green-500" />
                      ) : slugAvailable === false ? (
                        <X className="w-4 h-4 text-red-500" />
                      ) : null}
                    </div>
                  </div>
                  {errors.slug && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.slug}
                    </p>
                  )}
                  {slugAvailable === true && (
                    <p className="mt-1 text-sm text-green-600 flex items-center">
                      <Check className="w-4 h-4 mr-1" />
                      Slug có thể sử dụng
                    </p>
                  )}
                </div>

                {/* Short Description */}
                <div>
                  <label htmlFor="shortDescription" className="block text-sm font-medium text-gray-700 mb-2">
                    Mô Tả Ngắn
                  </label>
                  <textarea
                    id="shortDescription"
                    name="shortDescription"
                    rows={3}
                    value={formData.shortDescription}
                    onChange={handleInputChange}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Mô tả ngắn về sản phẩm..."
                  />
                </div>

                {/* Description */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                    Mô Tả Chi Tiết
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={6}
                    value={formData.description}
                    onChange={handleInputChange}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Mô tả chi tiết về sản phẩm..."
                  />
                </div>
              </div>
            </div>

            {/* Product Statistics */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Thống Kê Sản Phẩm</h2>
              </div>

              <div className="px-6 py-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-blue-600">{currentProduct._count.reviews}</div>
                    <div className="text-sm text-gray-600">Đánh giá</div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-green-600">{currentProduct._count.wishlistItems}</div>
                    <div className="text-sm text-gray-600">Yêu thích</div>
                  </div>
                  <div className="bg-orange-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-orange-600">{currentProduct._count.cartItems}</div>
                    <div className="text-sm text-gray-600">Trong giỏ hàng</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Images */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Hình Ảnh Sản Phẩm</h2>
              </div>

              <div className="px-6 py-6">
                {/* Current Images */}
                {formData.images.length > 0 && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Hình ảnh hiện tại
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {formData.images.map((image, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={image}
                            alt={`Product ${index + 1}`}
                            className="w-full h-32 object-cover rounded-lg border border-gray-200"
                          />
                          <button
                            type="button"
                            onClick={() => removeImage(index)}
                            className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Upload New Images */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Thêm hình ảnh mới
                  </label>
                  <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                    <div className="space-y-1 text-center">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label
                          htmlFor="images"
                          className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                        >
                          <span>Tải lên hình ảnh</span>
                          <input
                            id="images"
                            name="images"
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleImageUpload}
                            className="sr-only"
                          />
                        </label>
                        <p className="pl-1">hoặc kéo thả</p>
                      </div>
                      <p className="text-xs text-gray-500">PNG, JPG, GIF tối đa 10MB</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Category and Classification */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Phân Loại</h2>
              </div>

              <div className="px-6 py-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Category */}
                  <div>
                    <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 mb-2">
                      Danh Mục <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="categoryId"
                      name="categoryId"
                      value={formData.categoryId}
                      onChange={handleInputChange}
                      className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.categoryId ? 'border-red-300' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Chọn danh mục</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                    {errors.categoryId && (
                      <p className="mt-1 text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {errors.categoryId}
                      </p>
                    )}
                  </div>

                  {/* Brand */}
                  <div>
                    <label htmlFor="brand" className="block text-sm font-medium text-gray-700 mb-2">
                      Thương Hiệu
                    </label>
                    <input
                      type="text"
                      id="brand"
                      name="brand"
                      value={formData.brand}
                      onChange={handleInputChange}
                      className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Nhập thương hiệu..."
                    />
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
                    Tags
                  </label>
                  <input
                    type="text"
                    id="tags"
                    name="tags"
                    value={formData.tags}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, tags: e.target.value }));
                    }}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Nhập tags, phân cách bằng dấu phẩy..."
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Phân cách các tag bằng dấu phẩy (ví dụ: thời trang, nữ, váy)
                  </p>
                </div>
              </div>
            </div>

            {/* Pricing and Inventory */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Giá & Tồn Kho</h2>
              </div>

              <div className="px-6 py-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Price */}
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
                      Giá Bán <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        id="price"
                        name="price"
                        value={formData.price}
                        onChange={handleInputChange}
                        className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.price ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="0"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">VNĐ</span>
                      </div>
                    </div>
                    {errors.price && (
                      <p className="mt-1 text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {errors.price}
                      </p>
                    )}
                  </div>

                  {/* Original Price */}
                  <div>
                    <label htmlFor="originalPrice" className="block text-sm font-medium text-gray-700 mb-2">
                      Giá Gốc
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        id="originalPrice"
                        name="originalPrice"
                        value={formData.originalPrice || ''}
                        onChange={handleInputChange}
                        className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.originalPrice ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="0"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">VNĐ</span>
                      </div>
                    </div>
                    {errors.originalPrice && (
                      <p className="mt-1 text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {errors.originalPrice}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Stock */}
                  <div>
                    <label htmlFor="stock" className="block text-sm font-medium text-gray-700 mb-2">
                      Số Lượng Tồn Kho <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="stock"
                      name="stock"
                      value={formData.stock}
                      onChange={handleInputChange}
                      className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.stock ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="0"
                    />
                    {errors.stock && (
                      <p className="mt-1 text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {errors.stock}
                      </p>
                    )}
                  </div>

                  {/* SKU */}
                  <div>
                    <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-2">
                      SKU <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        id="sku"
                        name="sku"
                        value={formData.sku}
                        onChange={handleInputChange}
                        className={`block w-full border rounded-md px-3 py-2 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.sku ? 'border-red-300' :
                          skuAvailable === true ? 'border-green-300' :
                          skuAvailable === false ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="SKU-001"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        {skuChecking ? (
                          <Loader2 className="w-4 h-4 text-gray-400 animate-spin" />
                        ) : skuAvailable === true ? (
                          <Check className="w-4 h-4 text-green-500" />
                        ) : skuAvailable === false ? (
                          <X className="w-4 h-4 text-red-500" />
                        ) : null}
                      </div>
                    </div>
                    {errors.sku && (
                      <p className="mt-1 text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {errors.sku}
                      </p>
                    )}
                    {skuAvailable === true && (
                      <p className="mt-1 text-sm text-green-600 flex items-center">
                        <Check className="w-4 h-4 mr-1" />
                        SKU có thể sử dụng
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

         

            {/* Status and Settings */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Trạng Thái & Cài Đặt</h2>
              </div>

              <div className="px-6 py-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Status */}
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                      Trạng Thái
                    </label>
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="ACTIVE">Đang hoạt động</option>
                      <option value="INACTIVE">Không hoạt động</option>
                      <option value="DRAFT">Bản nháp</option>
                    </select>
                  </div>

                  {/* Featured */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Sản phẩm nổi bật
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        name="featured"
                        checked={formData.featured}
                        onChange={handleInputChange}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        Hiển thị sản phẩm này trong danh sách nổi bật
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4 pt-6">
              <button
                type="button"
                onClick={() => router.back()}
                className="inline-flex items-center px-6 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Hủy
              </button>
              <button
                type="submit"
                disabled={loading}
                className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Đang cập nhật...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Cập nhật sản phẩm
                  </>
                )}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AdminLayout>
  );
}
