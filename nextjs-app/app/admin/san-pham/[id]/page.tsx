'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Package,
  Star,
  Heart,
  ShoppingCart,
  Eye,
  Calendar,
  Tag,
  DollarSign,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Clock,
  Globe,
  Image as ImageIcon,
  Copy,
  ExternalLink
} from 'lucide-react';
import AdminLayout from '../../../../src/components/admin/AdminLayout';
import { useAdminToast } from '../../../../src/contexts/AdminToastContext';

interface Product {
  id: string;
  originalId?: string; // Store original ID from JSON for SEO
  name: string;
  slug: string;
  shortDescription: string | null;
  description: string | null;
  price: number;
  originalPrice: number | null;
  stock: number;
  sku: string;
  brand: string | null;
  images: string[];
  colors: string[]; // Product colors from imported data
  sizes: string[]; // Product sizes from imported data
  tags: string[];
  featured: boolean;
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT' | 'OUT_OF_STOCK';
  seoTitle: string | null;
  seoDescription: string | null;
  seoKeywords: string[];
  createdAt: string;
  updatedAt: string;

  // Multiple categories support
  categories: {
    id: string;
    name: string;
    slug: string;
  }[];
  category?: { // For backward compatibility
    id: string;
    name: string;
    slug: string;
  };

  // Enhanced reviews with imported data
  reviews: {
    id: string;
    rating: number;
    comment?: string;
    author?: string;
    reviewTime?: string;
    images: string[];
    isApproved: boolean;
    createdAt: string;
    user?: {
      name: string | null;
      email: string;
    } | null;
  }[];

  _count: {
    reviews: number;
    wishlistItems: number;
    cartItems: number;
  };
}

export default function AdminProductDetailPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;
  const { showToast } = useAdminToast();

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (productId) {
      fetchProduct();
    }
  }, [productId]);

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/admin/products/${productId}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setProduct(result.data);
        } else {
          console.error('Failed to fetch product:', result.message);
        }
      } else {
        console.error('Failed to fetch product:', response.status);
      }
    } catch (error) {
      console.error('Error fetching product:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProduct = async () => {
    if (!product) {
      return;
    }

    setDeleting(true);

    try {
      const response = await fetch(`/api/admin/products/${product.id}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      let result;
      try {
        result = await response.json();
      } catch (parseError) {
        throw new Error('Invalid response from server');
      }

      if (response.ok && result.success) {
        // Show success message
        showToast(`Đã xóa sản phẩm "${product.name}" thành công!`, 'success', 'trash');

        // Navigate back to products list with success message
        setTimeout(() => {
          router.push('/admin/san-pham?deleted=true&productName=' + encodeURIComponent(product.name));
        }, 1000);

      } else {
        // Handle specific error cases
        let errorMessage = 'Không thể xóa sản phẩm';

        if (result.message) {
          errorMessage = result.message;
        } else if (result.error) {
          errorMessage = result.error;
        } else if (response.status === 401) {
          errorMessage = 'Bạn không có quyền xóa sản phẩm này';
        } else if (response.status === 404) {
          errorMessage = 'Sản phẩm không tồn tại';
        } else if (response.status === 409) {
          errorMessage = 'Không thể xóa sản phẩm vì có đơn hàng liên quan';
        }

        showToast(errorMessage, 'error');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Lỗi kết nối khi xóa sản phẩm';
      showToast(errorMessage + '. Vui lòng thử lại.', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800';
      case 'DRAFT':
        return 'bg-yellow-100 text-yellow-800';
      case 'OUT_OF_STOCK':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'Đang hoạt động';
      case 'INACTIVE':
        return 'Không hoạt động';
      case 'DRAFT':
        return 'Bản nháp';
      case 'OUT_OF_STOCK':
        return 'Hết hàng';
      default:
        return status;
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showToast('Đã sao chép vào clipboard', 'success', 'check', 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      showToast('Không thể sao chép. Vui lòng thử lại.', 'error');
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!product) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-gray-400" />
          <div className="text-2xl font-bold text-gray-900 mt-4">Không tìm thấy sản phẩm</div>
          <p className="mt-2 text-gray-600">Sản phẩm bạn đang tìm không tồn tại hoặc đã bị xóa.</p>
          <button
            onClick={() => router.push('/admin/san-pham')}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Quay lại danh sách
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => router.back()}
              className="inline-flex items-center p-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              <ArrowLeft className="w-4 h-4" />
            </button>
            <div className="flex-1">
              <div className="text-2xl font-bold text-gray-900">{product.name}</div>
              <p className="mt-1 text-sm text-gray-500">
                ID: {product.id} • SKU: {product.sku}
              </p>
            </div>
            <div className="flex space-x-3">
              <a
                href={`/san-pham/${product.slug}`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Xem trên website
              </a>
              <a
                href={`/admin/san-pham/${product.id}/sua`}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
              >
                <Edit className="w-4 h-4 mr-2" />
                Chỉnh sửa
              </a>
              <button
                onClick={handleDeleteProduct}
                disabled={deleting}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                {deleting ? 'Đang xóa...' : 'Xóa'}
              </button>
            </div>
          </div>

          {/* Status and Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="flex items-center">
                <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
                  {getStatusText(product.status)}
                </div>
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="flex items-center">
                <Star className="w-5 h-5 text-yellow-400 mr-2" />
                <span className="text-sm font-medium text-gray-900">{product._count.reviews} đánh giá</span>
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="flex items-center">
                <Heart className="w-5 h-5 text-red-400 mr-2" />
                <span className="text-sm font-medium text-gray-900">{product._count.wishlistItems} yêu thích</span>
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="flex items-center">
                <ShoppingCart className="w-5 h-5 text-blue-400 mr-2" />
                <span className="text-sm font-medium text-gray-900">{product._count.cartItems} trong giỏ</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Product Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Product Images */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Hình ảnh sản phẩm</h2>
              {product.images && product.images.length > 0 ? (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {product.images.map((image, index) => (
                    <div key={index} className="relative aspect-square">
                      <img
                        src={image}
                        alt={`${product.name} - ${index + 1}`}
                        className="w-full h-full object-cover rounded-lg border border-gray-200"
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex items-center justify-center h-32 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <div className="text-center">
                    <ImageIcon className="mx-auto h-8 w-8 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-500">Chưa có hình ảnh</p>
                  </div>
                </div>
              )}
            </div>

            {/* Product Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Thông tin sản phẩm</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Tên sản phẩm</label>
                  <p className="mt-1 text-sm text-gray-900">{product.name}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Slug</label>
                  <div className="mt-1 flex items-center space-x-2">
                    <p className="text-sm text-gray-900 font-mono">{product.slug}</p>
                    <button
                      onClick={() => copyToClipboard(product.slug)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {product.shortDescription && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Mô tả ngắn</label>
                    <p className="mt-1 text-sm text-gray-900">{product.shortDescription}</p>
                  </div>
                )}

                {product.description && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Mô tả chi tiết</label>
                    <div className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{product.description}</div>
                  </div>
                )}

                {/* Original ID for reference */}
                {product.originalId && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">ID gốc (từ JSON)</label>
                    <div className="mt-1 flex items-center space-x-2">
                      <p className="text-sm text-gray-900 font-mono break-all">{product.originalId}</p>
                      <button
                        onClick={() => copyToClipboard(product.originalId!)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )}

                {/* Multiple Categories */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Danh mục ({product.categories?.length || 0})
                  </label>
                  {product.categories && product.categories.length > 0 ? (
                    <div className="mt-1 flex flex-wrap gap-2">
                      {product.categories.map((category) => (
                        <span
                          key={category.id}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                        >
                          {category.name}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <p className="mt-1 text-sm text-gray-500">Chưa có danh mục</p>
                  )}
                </div>

                {/* Colors and Sizes */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Màu sắc ({product.colors?.length || 0})
                    </label>
                    {product.colors && product.colors.length > 0 ? (
                      <div className="mt-1 flex flex-wrap gap-2">
                        {product.colors.map((color, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                          >
                            {color}
                          </span>
                        ))}
                      </div>
                    ) : (
                      <p className="mt-1 text-sm text-gray-500">Chưa có màu sắc</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Kích thước ({product.sizes?.length || 0})
                    </label>
                    {product.sizes && product.sizes.length > 0 ? (
                      <div className="mt-1 flex flex-wrap gap-2">
                        {product.sizes.map((size, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                          >
                            {size}
                          </span>
                        ))}
                      </div>
                    ) : (
                      <p className="mt-1 text-sm text-gray-500">Chưa có kích thước</p>
                    )}
                  </div>
                </div>

                {product.brand && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Thương hiệu</label>
                    <p className="mt-1 text-sm text-gray-900">{product.brand}</p>
                  </div>
                )}

                {product.tags && product.tags.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Tags</label>
                    <div className="mt-1 flex flex-wrap gap-2">
                      {product.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* SEO Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Thông tin SEO</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">SEO Title</label>
                  <p className="mt-1 text-sm text-gray-900">{product.seoTitle || 'Chưa thiết lập'}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">SEO Description</label>
                  <p className="mt-1 text-sm text-gray-900">{product.seoDescription || 'Chưa thiết lập'}</p>
                </div>

                {product.seoKeywords && product.seoKeywords.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">SEO Keywords</label>
                    <div className="mt-1 flex flex-wrap gap-2">
                      {product.seoKeywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Imported Reviews Section */}
            {product.reviews && product.reviews.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Đánh giá đã import ({product.reviews.length})
                </h2>
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {product.reviews.slice(0, 10).map((review) => (
                    <div key={review.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm font-medium text-gray-900">
                            {review.author || review.user?.name || 'Ẩn danh'}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {review.reviewTime || formatDate(review.createdAt)}
                        </div>
                      </div>

                      {review.comment && (
                        <p className="text-sm text-gray-700 mb-2 line-clamp-3">
                          {review.comment}
                        </p>
                      )}

                      {review.images && review.images.length > 0 && (
                        <div className="flex space-x-2 mt-2">
                          {review.images.slice(0, 3).map((image, index) => (
                            <img
                              key={index}
                              src={image}
                              alt={`Review image ${index + 1}`}
                              className="w-12 h-12 object-cover rounded border border-gray-200"
                            />
                          ))}
                          {review.images.length > 3 && (
                            <div className="w-12 h-12 bg-gray-100 rounded border border-gray-200 flex items-center justify-center">
                              <span className="text-xs text-gray-500">+{review.images.length - 3}</span>
                            </div>
                          )}
                        </div>
                      )}

                      <div className="flex items-center justify-between mt-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          review.isApproved
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {review.isApproved ? 'Đã duyệt' : 'Chờ duyệt'}
                        </span>
                      </div>
                    </div>
                  ))}

                  {product.reviews.length > 10 && (
                    <div className="text-center pt-4">
                      <p className="text-sm text-gray-500">
                        Hiển thị 10/{product.reviews.length} đánh giá
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Pricing & Inventory */}
          <div className="space-y-6">
            {/* Pricing */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Giá & Khuyến mãi</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Giá bán</label>
                  <p className="mt-1 text-lg font-semibold text-gray-900">{formatPrice(product.price)}</p>
                </div>

                {product.originalPrice && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Giá gốc</label>
                    <p className="mt-1 text-sm text-gray-500 line-through">{formatPrice(product.originalPrice)}</p>
                    <p className="text-sm text-green-600">
                      Giảm {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                    </p>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  {product.featured && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      <Star className="w-3 h-3 mr-1" />
                      Nổi bật
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Inventory */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Tồn kho</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Số lượng tồn kho</label>
                  <div className="mt-1 flex items-center space-x-2">
                    <span className={`text-lg font-semibold ${
                      product.stock > 10 ? 'text-green-600' :
                      product.stock > 0 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {product.stock}
                    </span>
                    <span className="text-sm text-gray-500">sản phẩm</span>
                  </div>
                  {product.stock <= 5 && product.stock > 0 && (
                    <p className="mt-1 text-sm text-yellow-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      Sắp hết hàng
                    </p>
                  )}
                  {product.stock === 0 && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      Hết hàng
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">SKU</label>
                  <div className="mt-1 flex items-center space-x-2">
                    <p className="text-sm text-gray-900 font-mono">{product.sku}</p>
                    <button
                      onClick={() => copyToClipboard(product.sku)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Timestamps */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Thông tin thời gian</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Ngày tạo</label>
                  <p className="mt-1 text-sm text-gray-900">{formatDate(product.createdAt)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Cập nhật lần cuối</label>
                  <p className="mt-1 text-sm text-gray-900">{formatDate(product.updatedAt)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </AdminLayout>
  );
}
