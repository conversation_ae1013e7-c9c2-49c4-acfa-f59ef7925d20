'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Save,
  Upload,
  X,
  AlertCircle,
  Loader2,
  Plus,
  Package,
  DollarSign,
  Image as ImageIcon,
} from 'lucide-react';
import AdminLayout from '../../../../../src/components/admin/AdminLayout';
import { useAdminToast } from '../../../../../src/contexts/AdminToastContext';
import { validateProductForm } from '../../../../../src/utils/categoryUtils';
import MultiCategorySelect from '../../../../../src/components/admin/MultiCategorySelect';
import ArrayFieldManager from '../../../../../src/components/admin/ArrayFieldManager';

interface Category {
  id: string;
  name: string;
  slug: string;
  level: number;
  parentId: string | null;
  isActive: boolean;
  sortOrder: number;
}

interface ProductSize {
  id: string;
  name: string;
  price?: number;
  stock?: number;
}

interface ProductVariant {
  id: string;
  name: string;
  price?: number;
  stock?: number;
  attributes?: {
    size?: string;
  };
}

interface CategoryRelation {
  id: string;
}

interface Product {
  id: string;
  originalId?: string; // Store original ID from JSON for SEO
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  price: number;
  originalPrice: number;
  stock: number;
  sku: string;
  categoryId: string; // For backward compatibility
  categoryIds?: string[]; // New multiple categories
  categories?: Category[]; // Category objects
  category?: CategoryRelation; // For backward compatibility
  variants?: ProductVariant[]; // Product variants
  images: string[];
  colors: string[]; // Product colors from imported data
  sizes: ProductSize[]; // Keep as ProductSize objects for compatibility
  sizeStrings?: string[]; // Simple size strings from imported data
  tags: string[];
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT';
  featured: boolean;
  weight: number;
  brand: string;
}

export default function SuaSanPhamPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;
  const { showToast } = useAdminToast();

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const [newImage, setNewImage] = useState('');
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    if (productId) {
      fetchProduct();
    }
  }, [productId]);

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/admin/products/${productId}`, {
        credentials: 'include',
      });
      const result = await response.json();
      
      if (result.success) {
        const productData = result?.data;

        // Handle categories - convert from many-to-many format
        if (productData?.categories && Array.isArray(productData.categories)) {
          productData.categoryIds = productData.categories.map((cat: CategoryRelation) => cat.id);
        } else if (productData?.category) {
          // Backward compatibility
          productData.categoryIds = [productData.category.id];
          productData.categories = [productData.category];
        } else {
          productData.categoryIds = [];
          productData.categories = [];
        }

        // Ensure colors and sizes arrays exist
        if (!productData.colors) productData.colors = [];
        if (!productData.sizes) productData.sizes = [];

        // Convert variants to sizes format if needed
        if (productData?.variants && productData?.variants.length > 0) {
          const sizes = productData.variants.map((variant: ProductVariant) => ({
            id: variant.id,
            name: variant.attributes?.size || variant.name,
            price: variant.price || productData.price,
            stock: variant.stock || 0
          }));
          productData.sizes = sizes;
        } else if (productData.sizeStrings && Array.isArray(productData.sizeStrings)) {
          // Convert simple size strings to ProductSize objects
          productData.sizes = productData.sizeStrings.map((sizeName: string, index: number) => ({
            id: `size-${index}`,
            name: sizeName,
            price: productData.price,
            stock: 0
          }));
        } else if (!productData.sizes || !Array.isArray(productData.sizes)) {
          productData.sizes = [];
        }

        setProduct(productData);
      } else {
        showToast('Không tìm thấy sản phẩm', 'error');
        router.push('/admin/san-pham');
      }
    } catch (error) {
      console.error('Error fetching product:', error);
      showToast('Lỗi khi tải sản phẩm', 'error');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    if (!product) return false;

    // Convert product to formData format for validation
    const formData = {
      name: product.name,
      slug: product.slug,
      sku: product.sku,
      categoryId: product.categoryId,
      price: product.price,
      stock: product.stock,
      status: product.status,
      description: product.description
    };

    // Use the shared validation function (skip slug/sku availability check for edit mode)
    const newErrors = validateProductForm(formData, null, null);
    
    // Remove any size/color related validation errors since they're optional for edit
    // and user should be able to save with existing data
    delete newErrors.sizes;
    delete newErrors.colors;
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!product || !validateForm()) {
      showToast('Vui lòng kiểm tra lại thông tin', 'error');
      return;
    }

    setSaving(true);

    try {
      // Create a clean request body without non-serializable fields
      // Allow existing sizes and colors to be submitted as-is (no additional validation required)
      const requestBody = {
        name: product.name,
        slug: product.slug,
        description: product.description,
        shortDescription: product.shortDescription,
        price: product.price,
        originalPrice: product.originalPrice,
        stock: product.stock,
        sku: product.sku,
        categoryId: product.categoryId, // For backward compatibility
        categoryIds: product.categoryIds || [], // New multiple categories
        colors: product.colors || [], // Submit existing colors as-is
        sizes: product.sizes || [], // Submit existing sizes as-is
        images: product.images,
        tags: product.tags,
        seoTitle: product.seoTitle,
        seoDescription: product.seoDescription,
        seoKeywords: product.seoKeywords,
        status: product.status,
        featured: product.featured,
        weight: product.weight,
        brand: product.brand,
        isOnSale: product.originalPrice > product.price,
      };

      const response = await fetch(`/api/admin/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        showToast('Cập nhật sản phẩm thành công!', 'success', 'edit');
        router.push('/admin/san-pham');
      } else {
        showToast(result.message || 'Lỗi khi cập nhật sản phẩm', 'error');
      }
    } catch (error) {
      console.error('Error updating product:', error);
      showToast('Lỗi kết nối khi cập nhật sản phẩm', 'error');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof Product, value: string | number | boolean | string[] | ProductSize[]) => {
    if (!product) return;
    
    setProduct(prev => prev ? { ...prev, [field]: value } : null);
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addImage = () => {
    if (!product || !newImage.trim() || product.images.includes(newImage.trim())) return;
    
    setProduct(prev => prev ? {
      ...prev,
      images: [...prev.images, newImage.trim()]
    } : null);
    setNewImage('');
  };

  const removeImage = (index: number) => {
    if (!product) return;

    setProduct(prev => prev ? {
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    } : null);
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || !product) return;

    const fileArray = Array.from(files);
    const validFiles: File[] = [];

    // Validate files
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml'
    ];

    for (const file of fileArray) {
      if (allowedTypes.includes(file.type)) {
        if (file.size <= 5 * 1024 * 1024) { // 5MB limit
          validFiles.push(file);
        } else {
          showToast(`File ${file.name} quá lớn (tối đa 5MB)`, 'error');
        }
      } else {
        showToast(`File ${file.name} không được hỗ trợ. Chỉ chấp nhận: JPEG, PNG, GIF, WebP, SVG`, 'error');
      }
    }

    if (validFiles.length === 0) return;

    setUploading(true);

    try {
      const uploadPromises = validFiles.map(async (file) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('folder', 'products');

        const response = await fetch('/api/admin/upload', {
          method: 'POST',
          credentials: 'include',
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Upload failed for ${file.name}`);
        }

        const result = await response.json();
        if (result.success) {
          return result.data.url;
        } else {
          throw new Error(result.message || `Upload failed for ${file.name}`);
        }
      });

      const uploadedUrls = await Promise.all(uploadPromises);

      // Add uploaded images to product
      setProduct(prev => prev ? {
        ...prev,
        images: [...prev.images, ...uploadedUrls]
      } : null);

      showToast(`Đã tải lên ${uploadedUrls.length} hình ảnh thành công!`, 'success');
    } catch (error) {
      console.error('Upload error:', error);
      showToast(
        error instanceof Error ? error.message : 'Lỗi khi tải lên hình ảnh',
        'error'
      );
    } finally {
      setUploading(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-orange-600" />
        </div>
      </AdminLayout>
    );
  }

  if (!product) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Không tìm thấy sản phẩm</h2>
          <p className="text-gray-600 mb-4">Sản phẩm có thể đã bị xóa hoặc không tồn tại.</p>
          <button
            onClick={() => router.push('/admin/san-pham')}
            className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
          >
            Quay lại danh sách
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Quay lại
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Sửa Sản Phẩm</h1>
              <p className="text-gray-600">Cập nhật thông tin sản phẩm: {product.name}</p>
            </div>
          </div>
          
          <button
            type="submit"
            form="product-form"
            disabled={saving}
            className="flex items-center px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 transition-colors"
          >
            <Save className="w-4 h-4 mr-2" />
            {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
          </button>
        </div>

        {/* Form */}
        <form id="product-form" onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <Package className="w-5 h-5 text-orange-600 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">Thông Tin Cơ Bản</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Original ID Field */}
                {product.originalId && (
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ID gốc (từ JSON) <span className="text-blue-600">*</span>
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={product.originalId}
                        onChange={(e) => handleInputChange('originalId', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-blue-50"
                        placeholder="ID từ dữ liệu JSON gốc"
                        readOnly
                      />
                      <button
                        type="button"
                        onClick={() => {
                          navigator.clipboard.writeText(product.originalId || '');
                          showToast('Đã copy ID gốc', 'success');
                        }}
                        className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                        title="Copy ID gốc"
                      >
                        Copy
                      </button>
                    </div>
                    <p className="mt-1 text-xs text-blue-600">
                      ID này được preserve từ dữ liệu JSON để maintain SEO URLs
                    </p>
                  </div>
                )}

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tên sản phẩm <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={product.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                      errors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Nhập tên sản phẩm"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.name}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Slug <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={product.slug}
                    onChange={(e) => handleInputChange('slug', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                      errors.slug ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="san-pham-slug"
                  />
                  {errors.slug && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.slug}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SKU <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={product.sku}
                    onChange={(e) => handleInputChange('sku', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                      errors.sku ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="SP-001"
                  />
                  {errors.sku && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.sku}
                    </p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mô tả ngắn
                  </label>
                  <input
                    type="text"
                    value={product.shortDescription || ''}
                    onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Mô tả ngắn gọn về sản phẩm"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mô tả chi tiết  
                  </label>
                  <textarea
                    value={product.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={4}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                      errors.description ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Mô tả chi tiết về sản phẩm"
                  />
                  {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                </div>
              </div>
            </div>

            {/* Pricing & Inventory */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <DollarSign className="w-5 h-5 text-orange-600 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">Giá & Kho Hàng</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Giá bán <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    value={product.price}
                    onChange={(e) => handleInputChange('price', Number(e.target.value))}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                      errors.price ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="0"
                    min="0"
                  />
                  {errors.price && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.price}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Giá gốc
                  </label>
                  <input
                    type="number"
                    value={product.originalPrice || 0}
                    onChange={(e) => handleInputChange('originalPrice', Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    placeholder="0"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Số lượng tồn kho <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    value={product.stock}
                    onChange={(e) => handleInputChange('stock', Number(e.target.value))}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                      errors.stock ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="0"
                    min="0"
                  />
                  {errors.stock && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.stock}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cân nặng (gram)
                  </label>
                  <input
                    type="number"
                    value={product.weight || 0}
                    onChange={(e) => handleInputChange('weight', Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    placeholder="0"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Thương hiệu
                  </label>
                  <input
                    type="text"
                    value={product.brand || ''}
                    onChange={(e) => handleInputChange('brand', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Tên thương hiệu"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status & Category */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Trạng Thái & Danh Mục</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Trạng thái <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={product.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                      errors.status ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">-- Chọn trạng thái --</option>
                    <option value="DRAFT">Bản nháp</option>
                    <option value="ACTIVE">Hoạt động</option>
                    <option value="INACTIVE">Tạm dừng</option>
                  </select>
                  {errors.status && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.status}
                    </p>
                  )}
                </div>

                {/* Multi-Category Selection */}
                <MultiCategorySelect
                  selectedCategoryIds={product.categoryIds || []}
                  onSelectionChange={(categoryIds) => {
                    handleInputChange('categoryIds', categoryIds);
                    // Update backward compatibility field
                    if (categoryIds.length > 0) {
                      handleInputChange('categoryId', categoryIds[0]);
                    } else {
                      handleInputChange('categoryId', '');
                    }
                  }}
                  error={errors.categoryId || errors.categoryIds}
                  required={true}
                />

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="featured"
                    checked={product.featured}
                    onChange={(e) => handleInputChange('featured', e.target.checked)}
                    className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                  />
                  <label htmlFor="featured" className="ml-2 block text-sm text-gray-900">
                    Sản phẩm nổi bật
                  </label>
                </div>
              </div>
            </div>

            {/* Images */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <ImageIcon className="w-5 h-5 text-orange-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Hình Ảnh</h3>
              </div>

              <div className="space-y-4">
                {/* Image Upload Area */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-2">
                    <label htmlFor="image-upload-edit" className="cursor-pointer">
                      <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                        {uploading ? 'Đang tải lên...' : 'Tải lên hình ảnh'}
                      </span>
                      <input
                        id="image-upload-edit"
                        type="file"
                        accept="image/jpeg,image/jpg,image/png,image/gif,image/webp,image/svg+xml"
                        multiple
                        onChange={handleImageUpload}
                        disabled={uploading}
                        className="sr-only"
                      />
                    </label>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    JPEG, PNG, GIF, WebP, SVG tối đa 5MB mỗi file
                  </p>
                  {uploading && (
                    <div className="mt-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mx-auto"></div>
                    </div>
                  )}
                </div>

                {/* URL Input */}
                <div className="border-t border-gray-200 pt-4">
                  <p className="text-sm text-gray-600 mb-2">Hoặc thêm từ URL:</p>
                  <div className="flex space-x-2">
                    <input
                      type="url"
                      value={newImage}
                      onChange={(e) => setNewImage(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      placeholder="https://example.com/image.jpg"
                    />
                    <button
                      type="button"
                      onClick={addImage}
                      className="px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Image List */}
                <div className="space-y-2">
                  {product.images.map((image, index) => (
                    <div key={index} className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg border">
                      <img src={image} alt="" className="w-12 h-12 object-cover rounded" />
                      <span className="flex-1 text-sm text-gray-600 truncate">{image}</span>
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="text-red-600 hover:text-red-800 p-1"
                        title="Xóa hình ảnh"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                  {product.images.length === 0 && (
                    <p className="text-sm text-gray-500 text-center py-4">
                      Chưa có hình ảnh nào. Hãy tải lên hoặc thêm từ URL.
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Colors and Sizes Management */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <Package className="w-5 h-5 text-orange-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Màu Sắc & Kích Thước</h3>
              </div>

              <div className="space-y-6">
                {/* Colors Management */}
                <ArrayFieldManager
                  label="Màu sắc"
                  values={product.colors || []}
                  onChange={(colors) => handleInputChange('colors', colors)}
                  placeholder="Nhập màu sắc (VD: Đỏ, Xanh, Vàng...)"
                  maxItems={15}
                  colorScheme="purple"
                />

                {/* Sizes Management */}
                <ArrayFieldManager
                  label="Kích thước"
                  values={product.sizes?.map(s => typeof s === 'string' ? s : s.name) || []}
                  onChange={(sizeNames) => {
                    // Convert size names back to ProductSize objects
                    const newSizes = sizeNames.map((name, index) => ({
                      id: `size-${index}-${Date.now()}`,
                      name,
                      price: product.price,
                      stock: 0
                    }));
                    handleInputChange('sizes', newSizes);
                  }}
                  placeholder="Nhập kích thước (VD: S, M, L, XL...)"
                  maxItems={20}
                  colorScheme="green"
                />
              </div>
            </div>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
}

