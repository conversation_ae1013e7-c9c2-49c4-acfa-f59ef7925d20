'use client';

import React, { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Package,
  Eye,
  Filter,
  Download,
  Upload,
  Star,
  Heart,
  ShoppingCart,
} from 'lucide-react';
import AdminLayout from '../../../src/components/admin/AdminLayout';
import { useAdminToast } from '../../../src/contexts/AdminToastContext';

interface Product {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  price: number;
  originalPrice: number | null;
  isOnSale: boolean;
  stock: number;
  images: string;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
  category?: {
    id: string;
    name: string;
  };
  _count?: {
    reviews: number;
    wishlistItems: number;
    cartItems: number;
    orderItems: number;
  };
}

interface Category {
  id: string;
  name: string;
}

// Component to handle URL params with Suspense
function URLParamsHandler({ showToast }: { showToast: (message: string, type: 'success' | 'error' | 'info' | 'warning', icon?: 'check' | 'edit' | 'delete' | 'add' | 'info') => void }) {
  const searchParams = useSearchParams();

  useEffect(() => {
    const deleted = searchParams.get('deleted');
    const productName = searchParams.get('productName');

    if (deleted === 'true' && productName) {
      showToast(`Đã xóa sản phẩm "${decodeURIComponent(productName)}" thành công`, 'success', 'delete');

      // Clean up URL params
      const url = new URL(window.location.href);
      url.searchParams.delete('deleted');
      url.searchParams.delete('productName');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams, showToast]);

  return null;
}

function AdminProductsPageContent() {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const [showFilters, setShowFilters] = useState(false);
  const { showToast } = useAdminToast();

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchProducts();
  }, [currentPage, searchTerm, selectedCategory, sortBy, sortOrder]);



  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories?limit=100', {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCategories(result.data.data);
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchProducts = async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        search: searchTerm,
        category: selectedCategory,
        sortBy,
        sortOrder,
      });

      const response = await fetch(`/api/admin/products?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setProducts(result.data.data);
          setTotalPages(result.data.pagination.totalPages);
        }
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProduct = async (product: Product) => {
    // Prevent multiple simultaneous deletions
    if (deleting) {
      return;
    }

    setDeleting(true);

    try {
      // Check if we're in the browser environment
      if (typeof window === 'undefined') {
        throw new Error('Delete function called on server side');
      }

      const deleteUrl = `/api/admin/products/${product.id}`;

      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Check if response is actually JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text();
        throw new Error(`Server returned ${response.status}: ${textResponse}`);
      }

      let result;
      try {
        result = await response.json();
      } catch (parseError) {
        throw new Error('Invalid JSON response from server');
      }

      if (response.ok && result.success) {
        // Show success toast with detailed message
        let successMessage = `Đã xóa sản phẩm "${product.name}" thành công`;
        if (result.deletedDependencies && result.deletedDependencies.length > 0) {
          successMessage += ` (bao gồm ${result.deletedDependencies.join(', ')} liên quan)`;
        }

        showToast(successMessage, 'success', 'trash');

        // Refresh product list
        await fetchProducts();

        // Reset to first page if current page becomes empty
        if (products.length === 1 && currentPage > 1) {
          setCurrentPage(1);
        }
      } else {
        // Handle specific error cases
        let errorMessage = 'Không thể xóa sản phẩm';

        if (result && result.message) {
          errorMessage = result.message;
        } else if (result && result.error) {
          errorMessage = result.error;
        } else if (response.status === 401) {
          errorMessage = 'Bạn không có quyền xóa sản phẩm này';
        } else if (response.status === 404) {
          errorMessage = 'Sản phẩm không tồn tại';
        } else if (response.status === 409) {
          errorMessage = 'Không thể xóa sản phẩm vì có đơn hàng liên quan';
        } else if (response.status === 500) {
          errorMessage = 'Lỗi server nội bộ';
        }

        showToast(errorMessage, 'error');
      }
    } catch (error) {
      let errorMessage = 'Lỗi kết nối khi xóa sản phẩm';

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Yêu cầu xóa sản phẩm bị timeout';
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng';
        } else {
          errorMessage = error.message;
        }
      }

      showToast(errorMessage + '. Vui lòng thử lại.', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const getProductImages = (images: string | string[]) => {
    try {
      const imageArray = Array.isArray(images)
        ? images
        : (images ? JSON.parse(images) : []);
      return imageArray.length > 0 ? imageArray[0] : '/images/placeholder.jpg';
    } catch {
      return '/images/placeholder.jpg';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Suspense fallback={null}>
        <URLParamsHandler showToast={showToast} />
      </Suspense>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="sm:flex sm:items-center sm:justify-between">
            <div>
              <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Quản Lý Sản Phẩm
              </div>
              <p className="mt-2 text-gray-600">
                Quản lý toàn bộ sản phẩm trong cửa hàng
              </p>
              <div className="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                <span>Tổng: {products.length} sản phẩm</span>
                <span>•</span>
                <span>Trang {currentPage}/{totalPages}</span>
              </div>
            </div>
            <div className="mt-6 sm:mt-0 flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
              {/* <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                <Download className="w-4 h-4 mr-2" />
                Xuất Excel
              </button>
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                <Upload className="w-4 h-4 mr-2" />
                Nhập Excel
              </button> */}
              <Link
                href="/admin/san-pham/them-moi"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all"
              >
                <Plus className="w-4 h-4 mr-2" />
                Thêm Sản Phẩm
              </Link>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-all"
                  placeholder="Tìm kiếm sản phẩm..."
                />
              </div>
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`inline-flex items-center px-6 py-3 border rounded-xl shadow-sm text-sm font-medium transition-all ${
                showFilters
                  ? 'border-blue-300 text-blue-700 bg-blue-50'
                  : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
              }`}
            >
              <Filter className="w-4 h-4 mr-2" />
              Bộ Lọc
            </button>
          </div>

          {/* Filters */}
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-4 p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl border border-gray-100"
            >
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Danh mục
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white transition-all"
                >
                  <option value="">Tất cả danh mục</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sắp xếp theo
                </label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 sm:text-sm"
                >
                  <option value="createdAt">Ngày tạo</option>
                  <option value="name">Tên sản phẩm</option>
                  <option value="price">Giá</option>
                  <option value="stock">Tồn kho</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Thứ tự
                </label>
                <select
                  value={sortOrder}
                  onChange={(e) => setSortOrder(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 sm:text-sm"
                >
                  <option value="desc">Giảm dần</option>
                  <option value="asc">Tăng dần</option>
                </select>
              </div>
            </motion.div>
          )}
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white overflow-hidden shadow-sm rounded-2xl hover:shadow-lg transition-all duration-300 border border-gray-100 hover:border-blue-200"
            >
              <div className="relative">
                <img
                  className="h-48 w-full object-cover"
                  src={getProductImages(product.images)}
                  alt={product.name}
                />
                <div className="absolute top-2 left-2 flex flex-col space-y-1">
                  {product.featured && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      Nổi bật
                    </span>
                  )}
                  {product.isOnSale && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Giảm giá
                    </span>
                  )}
                </div>
              </div>

              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                    {product.name}
                  </h3>
                </div>

                <p className="text-xs text-gray-500 mb-2">{product.category?.name || 'Chưa phân loại'}</p>

                <div className="flex items-center justify-between mb-3">
                  <div>
                    {product.isOnSale && product.originalPrice ? (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500 line-through">
                          {formatPrice(product.originalPrice)}
                        </span>
                        <span className="text-sm font-semibold text-red-600">
                          {formatPrice(product.price)}
                        </span>
                      </div>
                    ) : (
                      <span className="text-sm font-semibold text-gray-900">
                        {formatPrice(product.price)}
                      </span>
                    )}
                  </div>
                  <span className={`text-xs font-medium ${
                    product.stock > 10 ? 'text-green-600' : 
                    product.stock > 0 ? 'text-orange-600' : 'text-red-600'
                  }`}>
                    {product.stock > 0 ? `${product.stock} còn lại` : 'Hết hàng'}
                  </span>
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <div className="flex items-center space-x-3">
                    <span className="flex items-center">
                      <Star className="w-3 h-3 mr-1" />
                      {product._count?.reviews || 0}
                    </span>
                    <span className="flex items-center">
                      <Heart className="w-3 h-3 mr-1" />
                      {product._count?.wishlistItems || 0}
                    </span>
                    <span className="flex items-center">
                      <ShoppingCart className="w-3 h-3 mr-1" />
                      {product._count?.cartItems || 0}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-2">
                  <Link
                    href={`/admin/san-pham/${product.id}`}
                    className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-200 rounded-lg text-xs font-medium text-gray-700 bg-white hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all"
                  >
                    <Eye className="w-3 h-3 mr-1" />
                    Xem
                  </Link>
                  <Link
                    href={`/admin/san-pham/${product.id}/sua`}
                    className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-200 rounded-lg text-xs font-medium text-gray-700 bg-white hover:bg-green-50 hover:border-green-300 hover:text-green-700 transition-all"
                  >
                    <Edit className="w-3 h-3 mr-1" />
                    Sửa
                  </Link>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleDeleteProduct(product);
                    }}
                    disabled={deleting}
                    className="inline-flex items-center justify-center px-3 py-2 border border-red-200 rounded-lg text-xs font-medium text-red-700 bg-white hover:bg-red-50 hover:border-red-300 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    title={`Xóa sản phẩm ${product.name}`}
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {products.length === 0 && !loading && (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Không có sản phẩm</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || selectedCategory ? 'Không tìm thấy sản phẩm phù hợp' : 'Bắt đầu bằng cách tạo sản phẩm đầu tiên'}
            </p>
            {!searchTerm && !selectedCategory && (
              <div className="mt-6">
                <Link
                  href="/admin/san-pham/them-moi"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Thêm Sản Phẩm
                </Link>
              </div>
            )}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-8 flex justify-center">
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Trước
              </button>
              
              {[...Array(totalPages)].map((_, index) => {
                const page = index + 1;
                return (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      currentPage === page
                        ? 'z-10 bg-gray-50 border-gray-500 text-gray-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                );
              })}
              
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Sau
              </button>
            </nav>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}

export default function AdminProductsPage() {
  return <AdminProductsPageContent />;
}
