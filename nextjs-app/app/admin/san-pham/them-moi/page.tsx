'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Save,
  Upload,
  X,
  AlertCircle,
  Check,
  Loader2,
  Plus,
  Package
} from 'lucide-react';
import AdminLayout from '../../../../src/components/admin/AdminLayout';
import { prepareCategoryOptions } from '../../../../src/utils/categoryUtils';

interface Category {
  id: string;
  name: string;
  slug: string;
  level: number;
  parentId: string | null;
  isActive?: boolean;
  sortOrder?: number;
}

interface ProductSize {
  id: string;
  name: string;
  price?: number;
  stock?: number;
}

interface ProductVariant {
  id: string;
  name: string;
  value: string;
  price?: number;
  stock?: number;
}

interface FormData {
  name: string;
  slug: string;
  shortDescription: string;
  description: string;
  images: string[];
  categoryId: string;
  brand: string;
  sku: string;
  price: number;
  originalPrice: number;
  stock: number;
  sizes: ProductSize[];
  variants: ProductVariant[];
  tags: string;
  featured: boolean;
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT';
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
}

interface FormErrors {
  [key: string]: string;
}

export default function AddProductPage() {
  const router = useRouter();
  const customSizeInputRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [slugChecking, setSlugChecking] = useState(false);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const [skuChecking, setSkuChecking] = useState(false);
  const [skuAvailable, setSkuAvailable] = useState<boolean | null>(null);

  const checkSlugAvailability = async (slug: string) => {
    setSlugChecking(true);
    try {
      const response = await fetch(`/api/admin/products/check-slug?slug=${encodeURIComponent(slug)}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        setSlugAvailable(result.available);
      }
    } catch (error) {
      console.error('Error checking slug:', error);
      setSlugAvailable(null);
    } finally {
      setSlugChecking(false);
    }
  };

  const checkSKUAvailability = async (sku: string) => {
    setSkuChecking(true);
    try {
      const response = await fetch(`/api/admin/products/check-sku?sku=${encodeURIComponent(sku)}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        setSkuAvailable(result.available);
      }
    } catch (error) {
      console.error('Error checking SKU:', error);
      setSkuAvailable(null);
    } finally {
      setSkuChecking(false);
    }
  };

  const [formData, setFormData] = useState<FormData>({
    name: '',
    slug: '',
    shortDescription: '',
    description: '',
    images: [],
    categoryId: '',
    brand: '',
    sku: '',
    price: 0,
    originalPrice: 0,
    stock: 0,
    sizes: [],
    variants: [],
    tags: '',
    featured: false,
    status: 'ACTIVE',
    seoTitle: '',
    seoDescription: '',
    seoKeywords: ''
  });

  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    fetchCategories();
    generateSKU();
  }, []);

  // Auto-generate slug from name
  useEffect(() => {
    if (formData.name && !formData.slug) {
      const slug = generateSlug(formData.name);
      setFormData(prev => ({ ...prev, slug }));
    }
  }, [formData.name]);

  // Auto-generate SEO title from name
  useEffect(() => {
    if (formData.name && !formData.seoTitle) {
      setFormData(prev => ({
        ...prev,
        seoTitle: `${formData.name} - ThinLuong Fashion`
      }));
    }
  }, [formData.name]);

  // Check slug availability
  useEffect(() => {
    if (formData.slug && formData.slug.length > 2) {
      checkSlugAvailability(formData.slug);
    } else {
      setSlugAvailable(null);
    }
  }, [formData.slug]);

  // Check SKU availability
  useEffect(() => {
    if (formData.sku && formData.sku.length > 2) {
      checkSKUAvailability(formData.sku);
    } else {
      setSkuAvailable(null);
    }
  }, [formData.sku]);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories?limit=100', {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Handle nested data structure from /api/admin/categories
          const categoriesData = result.data?.data || result.data || [];
          setCategories(Array.isArray(categoriesData) ? categoriesData : []);
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      setCategories([]);
    }
  };

  const generateSlug = (name: string): string => {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/đ/g, 'd')
      .replace(/Đ/g, 'D')
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim()
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
  };

  const generateSKU = () => {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    const sku = `TL${timestamp}${random}`;
    setFormData(prev => ({ ...prev, sku }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked :
               type === 'number' ? parseFloat(value) || 0 : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    const fileArray = Array.from(files);
    const validFiles: File[] = [];
    const newPreviews: string[] = [];

    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml'
    ];

    for (const file of fileArray) {
      // Validate file type
      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({ ...prev, images: 'Chỉ chấp nhận: JPEG, PNG, GIF, WebP, SVG' }));
        continue;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, images: 'Kích thước file không được vượt quá 5MB' }));
        continue;
      }

      validFiles.push(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        newPreviews.push(result);

        if (newPreviews.length === validFiles.length) {
          setImagePreviews(prev => [...prev, ...newPreviews]);
          setFormData(prev => ({
            ...prev,
            images: [...prev.images, ...newPreviews]
          }));
        }
      };
      reader.readAsDataURL(file);
    }

    setFormData(prev => ({ ...prev, images: [...prev.images, ...newPreviews] }));
    setErrors(prev => ({ ...prev, images: '' }));
  };

  const removeImage = (index: number) => {
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  // Size management functions
  const addSize = (sizeName: string) => {
    if (!sizeName.trim()) {
      return;
    }

    const newSize: ProductSize = {
      id: Date.now().toString(),
      name: sizeName.trim(),
      price: formData.price,
      stock: 0
    };

    setFormData(prev => ({
      ...prev,
      sizes: [...prev.sizes, newSize]
    }));
  };

  const updateSize = (sizeId: string, field: keyof ProductSize, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      sizes: prev.sizes.map(size =>
        size.id === sizeId
          ? { ...size, [field]: value }
          : size
      )
    }));
  };

  const removeSize = (sizeId: string) => {
    setFormData(prev => ({
      ...prev,
      sizes: prev.sizes.filter(size => size.id !== sizeId)
    }));
  };



  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên sản phẩm là bắt buộc';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Slug là bắt buộc';
    } else if (slugAvailable === false) {
      newErrors.slug = 'Slug đã tồn tại, vui lòng chọn slug khác';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU là bắt buộc';
    } else if (skuAvailable === false) {
      newErrors.sku = 'SKU đã tồn tại, vui lòng chọn SKU khác';
    }

    if (!formData.categoryId) {
      newErrors.categoryId = 'Danh mục là bắt buộc';
    }

    if (formData.price <= 0) {
      newErrors.price = 'Giá phải lớn hơn 0';
    }

    if (formData.originalPrice && formData.originalPrice <= formData.price) {
      newErrors.originalPrice = 'Giá gốc phải lớn hơn giá bán';
    }

    if (formData.stock < 0) {
      newErrors.stock = 'Số lượng tồn kho không được âm';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          name: formData.name.trim(),
          slug: formData.slug.trim(),
          shortDescription: formData.shortDescription.trim(),
          description: formData.description.trim(),
          images: formData.images,
          categoryId: formData.categoryId,
          brand: formData.brand.trim(),
          sku: formData.sku.trim(),
          price: formData.price,
          originalPrice: formData.originalPrice || null,
          stock: formData.stock,
          sizes: formData.sizes,
          variants: formData.variants,
          tags: formData.tags.trim(),
          featured: formData.featured,
          status: formData.status,
          seoTitle: formData.seoTitle.trim(),
          seoDescription: formData.seoDescription.trim(),
          seoKeywords: formData.seoKeywords.trim(),
        }),
      });

      const result = await response.json();

      if (result.success) {
        router.push('/admin/san-pham?success=created');
      } else {
        setErrors({ name: result.message || 'Có lỗi xảy ra khi tạo sản phẩm' });
      }
    } catch (error) {
      console.error('Error creating product:', error);
      setErrors({ name: 'Có lỗi xảy ra khi tạo sản phẩm' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto min-w-0">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => router.back()}
              className="inline-flex items-center p-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              <ArrowLeft className="w-4 h-4" />
            </button>
            <div>
              <div className="text-2xl font-bold text-gray-900">Thêm Sản Phẩm Mới</div>
              <p className="mt-1 text-sm text-gray-500">
                Tạo sản phẩm mới cho cửa hàng
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Basic Information */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Thông Tin Cơ Bản</h2>
              </div>

              <div className="px-6 py-6 space-y-6">
                {/* Product Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Tên Sản Phẩm <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={`block w-full min-w-0 border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.name ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Nhập tên sản phẩm..."
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.name}
                    </p>
                  )}
                </div>

                {/* Slug */}
                <div>
                  <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-2">
                    Slug <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="slug"
                      name="slug"
                      value={formData.slug}
                      onChange={handleInputChange}
                      className={`block w-full border rounded-md px-3 py-2 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.slug ? 'border-red-300' :
                        slugAvailable === true ? 'border-green-300' :
                        slugAvailable === false ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="slug-san-pham"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      {slugChecking ? (
                        <Loader2 className="w-4 h-4 text-gray-400 animate-spin" />
                      ) : slugAvailable === true ? (
                        <Check className="w-4 h-4 text-green-500" />
                      ) : slugAvailable === false ? (
                        <X className="w-4 h-4 text-red-500" />
                      ) : null}
                    </div>
                  </div>
                  {errors.slug && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.slug}
                    </p>
                  )}
                  {slugAvailable === true && (
                    <p className="mt-1 text-sm text-green-600 flex items-center">
                      <Check className="w-4 h-4 mr-1" />
                      Slug có thể sử dụng
                    </p>
                  )}
                </div>

                {/* Short Description */}
                <div>
                  <label htmlFor="shortDescription" className="block text-sm font-medium text-gray-700 mb-2">
                    Mô Tả Ngắn
                  </label>
                  <textarea
                    id="shortDescription"
                    name="shortDescription"
                    rows={3}
                    value={formData.shortDescription}
                    onChange={handleInputChange}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Mô tả ngắn về sản phẩm..."
                  />
                </div>

                {/* Description */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                    Mô Tả Chi Tiết
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={6}
                    value={formData.description}
                    onChange={handleInputChange}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Mô tả chi tiết về sản phẩm..."
                  />
                </div>
              </div>
            </div>

            {/* Images */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Hình Ảnh Sản Phẩm</h2>
              </div>

              <div className="px-6 py-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hình Ảnh
                  </label>

                  {/* Image Upload Area */}
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-2">
                      <label htmlFor="image-upload" className="cursor-pointer">
                        <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                          Tải lên hình ảnh
                        </span>
                        <input
                          id="image-upload"
                          type="file"
                          accept="image/jpeg,image/jpg,image/png,image/gif,image/webp,image/svg+xml"
                          multiple
                          onChange={handleImageUpload}
                          className="sr-only"
                        />
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      JPEG, PNG, GIF, WebP, SVG tối đa 5MB mỗi file
                    </p>
                  </div>

                  {/* Image Previews */}
                  {imagePreviews.length > 0 && (
                    <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                      {imagePreviews.map((preview, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={preview}
                            alt={`Preview ${index + 1}`}
                            className="w-full h-24 object-cover rounded-lg border border-gray-300"
                          />
                          <button
                            type="button"
                            onClick={() => removeImage(index)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {errors.images && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.images}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Size Management */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Quản Lý Size</h2>
                <p className="text-sm text-gray-500 mt-1">Thêm các size khác nhau cho sản phẩm</p>
              </div>

              <div className="px-6 py-6">
                {/* Add Size */}
                <div className="mb-6">
                  <div className="flex space-x-2">
                    <select
                      onChange={(e) => {
                        if (e.target.value) {
                          addSize(e.target.value);
                          e.target.value = '';
                        }
                      }}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Chọn size...</option>
                      <option value="XS">XS</option>
                      <option value="S">S</option>
                      <option value="M">M</option>
                      <option value="L">L</option>
                      <option value="XL">XL</option>
                      <option value="XXL">XXL</option>
                      <option value="XXXL">XXXL</option>
                      <option value="Free Size">Free Size</option>
                    </select>
                    <input
                      ref={customSizeInputRef}
                      type="text"
                      placeholder="Hoặc nhập size tùy chỉnh..."
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          const input = e.target as HTMLInputElement;
                          if (input.value.trim()) {
                            console.log('Adding custom size:', input.value.trim());
                            addSize(input.value.trim());
                            input.value = '';
                          }
                        }
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => {
                        const input = customSizeInputRef.current;
                        if (input && input.value.trim()) {
                          addSize(input.value.trim());
                          input.value = '';
                        }
                      }}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Size List */}
                {formData.sizes.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-900">Danh sách Size:</h4>
                    {formData.sizes.map((size) => (
                      <div key={size.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border">
                        <div className="flex-shrink-0">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {size.name}
                          </span>
                        </div>
                        <div className="flex-1 grid grid-cols-2 gap-3">
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Giá (VND)
                            </label>
                            <input
                              type="number"
                              value={size.price || ''}
                              onChange={(e) => updateSize(size.id, 'price', parseFloat(e.target.value) || 0)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              placeholder="Giá cho size này"
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Tồn kho
                            </label>
                            <input
                              type="number"
                              value={size.stock || ''}
                              onChange={(e) => updateSize(size.id, 'stock', parseInt(e.target.value) || 0)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              placeholder="Số lượng"
                            />
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => removeSize(size.id)}
                          className="flex-shrink-0 p-1 text-red-600 hover:text-red-800"
                          title="Xóa size"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                {formData.sizes.length === 0 && (
                  <div className="text-center py-6 text-gray-500">
                    <Package className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                    <p className="text-sm">Chưa có size nào. Hãy thêm size cho sản phẩm.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Product Details */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Chi Tiết Sản Phẩm</h2>
              </div>

              <div className="px-6 py-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Category */}
                  <div>
                    <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 mb-2">
                      Danh Mục <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="categoryId"
                      name="categoryId"
                      value={formData.categoryId}
                      onChange={handleInputChange}
                      className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.categoryId ? 'border-red-300' : 'border-gray-300'
                      }`}
                    >
                      <option value="">-- Chọn danh mục --</option>
                      {prepareCategoryOptions(categories).map(option => (
                        <option key={option.id} value={option.id}>
                          {option.displayName}
                        </option>
                      ))}
                    </select>
                    {errors.categoryId && (
                      <p className="mt-1 text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {errors.categoryId}
                      </p>
                    )}
                  </div>

                  {/* Brand */}
                  <div>
                    <label htmlFor="brand" className="block text-sm font-medium text-gray-700 mb-2">
                      Thương Hiệu
                    </label>
                    <input
                      type="text"
                      id="brand"
                      name="brand"
                      value={formData.brand}
                      onChange={handleInputChange}
                      className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Nhập thương hiệu..."
                    />
                  </div>
                </div>

                {/* SKU */}
                <div>
                  <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-2">
                    SKU <span className="text-red-500">*</span>
                  </label>
                  <div className="flex space-x-2">
                    <div className="relative flex-1">
                      <input
                        type="text"
                        id="sku"
                        name="sku"
                        value={formData.sku}
                        onChange={handleInputChange}
                        className={`block w-full border rounded-md px-3 py-2 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.sku ? 'border-red-300' :
                          skuAvailable === true ? 'border-green-300' :
                          skuAvailable === false ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="SKU sản phẩm"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        {skuChecking ? (
                          <Loader2 className="w-4 h-4 text-gray-400 animate-spin" />
                        ) : skuAvailable === true ? (
                          <Check className="w-4 h-4 text-green-500" />
                        ) : skuAvailable === false ? (
                          <X className="w-4 h-4 text-red-500" />
                        ) : null}
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={generateSKU}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                    >
                      Tạo mới
                    </button>
                  </div>
                  {errors.sku && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.sku}
                    </p>
                  )}
                  {skuAvailable === true && (
                    <p className="mt-1 text-sm text-green-600 flex items-center">
                      <Check className="w-4 h-4 mr-1" />
                      SKU có thể sử dụng
                    </p>
                  )}
                </div>

                {/* Price and Stock */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Price */}
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
                      Giá Bán <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        id="price"
                        name="price"
                        min="0"
                        step="1000"
                        value={formData.price}
                        onChange={handleInputChange}
                        className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.price ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="0"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 text-sm">VNĐ</span>
                      </div>
                    </div>
                    {errors.price && (
                      <p className="mt-1 text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {errors.price}
                      </p>
                    )}
                  </div>

                  {/* Original Price */}
                  <div>
                    <label htmlFor="originalPrice" className="block text-sm font-medium text-gray-700 mb-2">
                      Giá Gốc
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        id="originalPrice"
                        name="originalPrice"
                        min="0"
                        step="1000"
                        value={formData.originalPrice}
                        onChange={handleInputChange}
                        className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.originalPrice ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="0"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 text-sm">VNĐ</span>
                      </div>
                    </div>
                    {errors.originalPrice && (
                      <p className="mt-1 text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {errors.originalPrice}
                      </p>
                    )}
                    <p className="mt-1 text-xs text-gray-500">
                      Để trống nếu không có giảm giá
                    </p>
                  </div>

                  {/* Stock */}
                  <div>
                    <label htmlFor="stock" className="block text-sm font-medium text-gray-700 mb-2">
                      Số Lượng Tồn Kho
                    </label>
                    <input
                      type="number"
                      id="stock"
                      name="stock"
                      min="0"
                      value={formData.stock}
                      onChange={handleInputChange}
                      className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.stock ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="0"
                    />
                    {errors.stock && (
                      <p className="mt-1 text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {errors.stock}
                      </p>
                    )}
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
                    Tags
                  </label>
                  <input
                    type="text"
                    id="tags"
                    name="tags"
                    value={formData.tags}
                    onChange={handleInputChange}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="tag1, tag2, tag3..."
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Phân cách các tag bằng dấu phẩy
                  </p>
                </div>

                {/* Settings */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Featured */}
                  <div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="featured"
                        name="featured"
                        checked={formData.featured}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="featured" className="ml-2 block text-sm text-gray-700">
                        Sản phẩm nổi bật
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Hiển thị trong danh sách sản phẩm nổi bật
                    </p>
                  </div>

                  {/* Status */}
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                      Trạng Thái
                    </label>
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="ACTIVE">Hoạt động</option>
                      <option value="INACTIVE">Không hoạt động</option>
                      <option value="DRAFT">Bản nháp</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

           

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Hủy
              </button>
              <button
                type="submit"
                disabled={loading || slugChecking || skuChecking || slugAvailable === false || skuAvailable === false}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Đang tạo...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Tạo Sản Phẩm
                  </>
                )}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AdminLayout>
  );
}