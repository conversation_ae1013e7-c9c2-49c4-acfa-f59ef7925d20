'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Heart,
  TrendingUp,
  Users,
  Package,
  BarChart3,
  Calendar,
  Star,
  ShoppingCart,
  Eye,
} from 'lucide-react';
import AdminLayout from '../../../src/components/admin/AdminLayout';

interface WishlistAnalytics {
  summary: {
    totalWishlistItems: number;
    totalUsersWithWishlist: number;
    avgWishlistSize: number;
    conversionRate: number;
  };
  mostWishlistedProducts: Array<{
    id: string;
    name: string;
    price: number;
    images: string;
    category: {
      name: string;
    };
    _count: {
      wishlistItems: number;
    };
  }>;
  categoryDistribution: Array<{
    categoryId: string;
    categoryName: string;
    totalWishlistItems: number;
    productCount: number;
  }>;
  topWishlistUsers: Array<{
    id: string;
    name: string | null;
    email: string;
    _count: {
      wishlist: number;
    };
  }>;
  recentActivities: Array<{
    id: string;
    createdAt: string;
    user: {
      id: string;
      name: string | null;
      email: string;
    };
    product: {
      id: string;
      name: string;
      price: number;
      images: string;
      category: {
        name: string;
      };
    };
  }>;
}

export default function AdminWishlistPage() {
  const [analytics, setAnalytics] = useState<WishlistAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('30');

  useEffect(() => {
    fetchWishlistAnalytics();
  }, [selectedPeriod]);

  const fetchWishlistAnalytics = async () => {
    try {
      const response = await fetch(`/api/admin/wishlist/analytics?period=${selectedPeriod}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setAnalytics(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching wishlist analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const getProductImage = (images: string | string[]) => {
    try {
      const imageArray = Array.isArray(images)
        ? images
        : (images ? JSON.parse(images) : []);
      return imageArray.length > 0 ? imageArray[0] : '/images/placeholder.jpg';
    } catch {
      return '/images/placeholder.jpg';
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!analytics) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <Heart className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Lỗi tải dữ liệu</h3>
          <p className="mt-1 text-sm text-gray-500">Không thể tải dữ liệu phân tích wishlist</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-8">
          <div>
            <div className="text-2xl font-bold text-gray-900">Phân Tích Wishlist</div>
            <p className="mt-1 text-sm text-gray-500">
              Theo dõi và phân tích hành vi wishlist của khách hàng
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500 sm:text-sm"
            >
              <option value="7">7 ngày qua</option>
              <option value="30">30 ngày qua</option>
              <option value="90">90 ngày qua</option>
              <option value="365">1 năm qua</option>
            </select>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Heart className="h-6 w-6 text-red-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Tổng Wishlist
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {analytics.summary.totalWishlistItems.toLocaleString()}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-6 w-6 text-blue-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Người Dùng Có Wishlist
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {analytics.summary.totalUsersWithWishlist.toLocaleString()}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BarChart3 className="h-6 w-6 text-green-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Trung Bình/Người
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {analytics.summary.avgWishlistSize}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-6 w-6 text-purple-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Tỷ Lệ Chuyển Đổi
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {analytics.summary.conversionRate.toFixed(1)}%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Most Wishlisted Products */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="bg-white shadow rounded-lg"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Sản Phẩm Được Yêu Thích Nhất</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {analytics.mostWishlistedProducts.slice(0, 5).map((product, index) => (
                  <div key={product.id} className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <img
                        className="w-12 h-12 rounded-lg object-cover"
                        src={getProductImage(product.images)}
                        alt={product.name}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {product.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {product.category.name} • {formatPrice(product.price)}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Heart className="w-4 h-4 text-red-500" />
                      <span className="text-sm font-medium text-gray-900">
                        {product._count.wishlistItems}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <a
                  href="/admin/san-pham"
                  className="text-sm font-medium text-blue-600 hover:text-blue-500"
                >
                  Xem tất cả sản phẩm →
                </a>
              </div>
            </div>
          </motion.div>

          {/* Category Distribution */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="bg-white shadow rounded-lg"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Phân Bố Theo Danh Mục</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {analytics.categoryDistribution.slice(0, 5).map((category, index) => (
                  <div key={category.categoryId} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {category.categoryName}
                        </p>
                        <p className="text-xs text-gray-500">
                          {category.productCount} sản phẩm
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {category.totalWishlistItems}
                      </p>
                      <p className="text-xs text-gray-500">lượt yêu thích</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Top Wishlist Users */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="bg-white shadow rounded-lg"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Khách Hàng Tích Cực Nhất</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {analytics.topWishlistUsers.slice(0, 5).map((user, index) => (
                  <div key={user.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-700">
                          {user.name?.charAt(0) || user.email.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {user.name || 'Khách hàng'}
                        </p>
                        <p className="text-xs text-gray-500">{user.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Heart className="w-4 h-4 text-red-500" />
                      <span className="text-sm font-medium text-gray-900">
                        {user._count.wishlist}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <a
                  href="/admin/khach-hang"
                  className="text-sm font-medium text-blue-600 hover:text-blue-500"
                >
                  Xem tất cả khách hàng →
                </a>
              </div>
            </div>
          </motion.div>

          {/* Recent Activities */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="bg-white shadow rounded-lg"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Hoạt Động Gần Đây</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {analytics.recentActivities.slice(0, 5).map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <img
                        className="w-10 h-10 rounded-lg object-cover"
                        src={getProductImage(activity.product.images)}
                        alt={activity.product.name}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">
                        <span className="font-medium">
                          {activity.user.name || activity.user.email}
                        </span>
                        {' '}đã thêm{' '}
                        <span className="font-medium">{activity.product.name}</span>
                        {' '}vào wishlist
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(activity.createdAt).toLocaleString('vi-VN')}
                      </p>
                    </div>
                    <div className="flex-shrink-0">
                      <Heart className="w-4 h-4 text-red-500" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AdminLayout>
  );
}
