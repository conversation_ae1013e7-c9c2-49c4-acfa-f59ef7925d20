'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Heart,
  Search,
  Trash2,
  User,
  Package,
  Calendar,
  RefreshCw,
  Download,
  Filter,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import AdminLayout from '../../../../src/components/admin/AdminLayout';

interface WishlistItem {
  id: string;
  userId: string;
  productId: string;
  createdAt: string;
  user: {
    id: string;
    name: string | null;
    email: string;
    phone: string | null;
    createdAt: string;
  };
  product: {
    id: string;
    name: string;
    slug: string;
    price: number;
    originalPrice: number | null;
    images: string;
    stock: number;
    category: {
      id: string;
      name: string;
    } | null;
    _count: {
      reviews: number;
      wishlistItems: number;
      orderItems: number;
    };
  };
}

interface WishlistResponse {
  wishlistItems: WishlistItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export default function AdminWishlistManagePage() {
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedUserId, setSelectedUserId] = useState('');
  const [deletingItemId, setDeletingItemId] = useState<string | null>(null);

  useEffect(() => {
    fetchWishlistItems();
  }, [currentPage, searchTerm, sortBy, sortOrder, selectedUserId]);

  const fetchWishlistItems = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        search: searchTerm,
        sortBy,
        sortOrder,
      });

      if (selectedUserId) {
        params.append('userId', selectedUserId);
      }

      const response = await fetch(`/api/admin/wishlist?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result: { success: boolean; data: WishlistResponse } = await response.json();
        if (result.success) {
          setWishlistItems(result.data.wishlistItems);
          setTotalPages(result.data.pagination.totalPages);
          setTotal(result.data.pagination.total);
        }
      } else {
        console.error('Failed to fetch wishlist items');
      }
    } catch (error) {
      console.error('Error fetching wishlist items:', error);
    } finally {
      setLoading(false);
    }
  };

  const deleteWishlistItem = async (itemId: string) => {
    if (!confirm('Bạn có chắc chắn muốn xóa item này khỏi wishlist?')) {
      return;
    }

    setDeletingItemId(itemId);
    try {
      const response = await fetch(`/api/admin/wishlist?id=${itemId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        setWishlistItems(items => items.filter(item => item.id !== itemId));
        setTotal(prev => prev - 1);
      } else {
        console.error('Failed to delete wishlist item');
        alert('Lỗi khi xóa wishlist item');
      }
    } catch (error) {
      console.error('Error deleting wishlist item:', error);
      alert('Lỗi khi xóa wishlist item');
    } finally {
      setDeletingItemId(null);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const getProductImage = (images: string) => {
    try {
      const imageArray = images ? JSON.parse(images) : [];
      return imageArray.length > 0 ? imageArray[0] : '/images/placeholder.jpg';
    } catch {
      return '/images/placeholder.jpg';
    }
  };

  const exportWishlistData = async () => {
    try {
      // Fetch all data for export
      const response = await fetch(`/api/admin/wishlist?limit=1000&search=${searchTerm}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Create CSV content
          const csvContent = [
            ['User Email', 'User Name', 'Product Name', 'Category', 'Price', 'Added Date'].join(','),
            ...result.data.wishlistItems.map((item: WishlistItem) => [
              item.user.email,
              item.user.name || '',
              `"${item.product.name}"`,
              item.product.category?.name || '',
              item.product.price,
              new Date(item.createdAt).toLocaleDateString('vi-VN')
            ].join(','))
          ].join('\n');

          // Download CSV
          const blob = new Blob([csvContent], { type: 'text/csv' });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `wishlist-data-${new Date().toISOString().split('T')[0]}.csv`;
          a.click();
          window.URL.revokeObjectURL(url);
        }
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Lỗi khi xuất dữ liệu');
    }
  };

  if (loading && wishlistItems.length === 0) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Quản Lý Wishlist</h1>
            <p className="mt-1 text-sm text-gray-500">
              Quản lý tất cả wishlist items của khách hàng ({total} items)
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            <button
              onClick={fetchWishlistItems}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Làm mới
            </button>
            <button
              onClick={exportWishlistData}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
            >
              <Download className="w-4 h-4 mr-2" />
              Xuất CSV
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Tìm user hoặc sản phẩm..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Sort By */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="createdAt">Ngày thêm</option>
            <option value="user.name">Tên user</option>
            <option value="product.name">Tên sản phẩm</option>
            <option value="product.price">Giá sản phẩm</option>
          </select>

          {/* Sort Order */}
          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="desc">Mới nhất</option>
            <option value="asc">Cũ nhất</option>
          </select>

          {/* Clear Filters */}
          <button
            onClick={() => {
              setSearchTerm('');
              setSelectedUserId('');
              setSortBy('createdAt');
              setSortOrder('desc');
              setCurrentPage(1);
            }}
            className="px-4 py-2 text-sm text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <Filter className="w-4 h-4 inline mr-2" />
            Xóa bộ lọc
          </button>
        </div>

        {/* Wishlist Items Table */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          {wishlistItems.length === 0 ? (
            <div className="text-center py-12">
              <Heart className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Không có wishlist items</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Không tìm thấy kết quả phù hợp' : 'Chưa có wishlist items nào'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Khách hàng
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Sản phẩm
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Danh mục
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Giá
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ngày thêm
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Thao tác
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {wishlistItems.map((item) => (
                    <motion.tr
                      key={item.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="hover:bg-gray-50"
                    >
                      {/* User Info */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <User className="h-5 w-5 text-gray-600" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {item.user.name || 'N/A'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {item.user.email}
                            </div>
                          </div>
                        </div>
                      </td>

                      {/* Product Info */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-12 w-12">
                            <img
                              className="h-12 w-12 rounded-lg object-cover"
                              src={getProductImage(item.product.images)}
                              alt={item.product.name}
                            />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                              {item.product.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              Stock: {item.product.stock}
                            </div>
                          </div>
                        </div>
                      </td>

                      {/* Category */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {item.product.category?.name || 'N/A'}
                        </span>
                      </td>

                      {/* Price */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="font-medium">
                          {formatPrice(item.product.price)}
                        </div>
                        {item.product.originalPrice && item.product.originalPrice > item.product.price && (
                          <div className="text-xs text-gray-500 line-through">
                            {formatPrice(item.product.originalPrice)}
                          </div>
                        )}
                      </td>

                      {/* Date Added */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {new Date(item.createdAt).toLocaleDateString('vi-VN')}
                        </div>
                      </td>

                      {/* Actions */}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => deleteWishlistItem(item.id)}
                          disabled={deletingItemId === item.id}
                          className="text-red-600 hover:text-red-900 disabled:opacity-50"
                        >
                          {deletingItemId === item.id ? (
                            <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <Trash2 className="w-4 h-4" />
                          )}
                        </button>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Hiển thị {((currentPage - 1) * 20) + 1} đến {Math.min(currentPage * 20, total)} của {total} kết quả
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="w-4 h-4" />
              </button>
              
              <span className="px-4 py-2 text-sm text-gray-700">
                Trang {currentPage} / {totalPages}
              </span>
              
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
} 