import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import prisma from '../../../../../src/lib/db';

const JWT_SECRET = process.env.ADMIN_JWT_SECRET || 'fallback-secret';

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('admin-token')?.value;

    if (token) {
      try {
        // Verify token and get admin info
        const payload = jwt.verify(token, JWT_SECRET) as { adminId: string; email: string; role: string };

        // Delete session from database
        await prisma.adminSession.deleteMany({
          where: { token },
        });

        // Log logout activity
        const ipAddress = request.headers.get('x-forwarded-for') ||
                         request.headers.get('x-real-ip') ||
                         'unknown';
        const userAgent = request.headers.get('user-agent') || '';

        await prisma.adminLog.create({
          data: {
            adminId: payload.adminId,
            action: 'LOGOUT',
            resource: 'AUTH',
            details: { ipAddress, userAgent },
            ipAddress,
            userAgent,
          },
        });
      } catch (error) {
        // Token invalid, but still clear cookie
        console.log('Invalid token during logout:', error);
      }
    }

    // Clear cookie and return response
    const response = NextResponse.json({
      success: true,
      message: 'Đăng xuất thành công',
    });

    response.cookies.delete('admin-token');
    return response;
  } catch (error) {
    console.error('Admin logout error:', error);
    return NextResponse.json(
      { success: false, message: 'Lỗi server nội bộ' },
      { status: 500 }
    );
  }
}
