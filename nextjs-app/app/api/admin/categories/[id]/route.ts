import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { prisma } from '../../../../../src/lib/prisma';

const JWT_SECRET = process.env.ADMIN_JWT_SECRET || 'fallback-secret';

// Middleware to verify admin authentication
async function verifyAdmin(request: NextRequest) {
  const token = request.cookies.get('admin-token')?.value;

  if (!token) {
    return null;
  }

  try {
    const payload = jwt.verify(token, JWT_SECRET) as { adminId: string; email: string; role: string };

    const admin = await prisma.adminUser.findUnique({
      where: { id: payload.adminId },
      select: { id: true, email: true, name: true, role: true, isActive: true },
    });

    if (!admin || !admin.isActive) {
      return null;
    }

    return admin;
  } catch (error) {
    return null;
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;

    const category = await prisma.category.findUnique({
      where: { id },
      include: {
        parent: {
          select: { id: true, name: true }
        },
        children: {
          select: { id: true, name: true, isActive: true }
        },
        _count: {
          select: { products: true }
        }
      }
    });

    if (!category) {
      return NextResponse.json(
        { success: false, message: 'Danh mục không tồn tại' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error('Error fetching category:', error);
    return NextResponse.json(
      { success: false, error: 'Lỗi khi tải danh mục' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const { name, slug, description, image, parentId, sortOrder, isActive } = body;

    if (!name || !slug) {
      return NextResponse.json(
        { success: false, message: 'Tên và slug là bắt buộc' },
        { status: 400 }
      );
    }

    // Check if category exists
    const existingCategory = await prisma.category.findUnique({
      where: { id },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, message: 'Danh mục không tồn tại' },
        { status: 404 }
      );
    }

    // Check if slug already exists (excluding current category)
    const slugExists = await prisma.category.findFirst({
      where: {
        slug,
        id: { not: id }
      }
    });

    if (slugExists) {
      return NextResponse.json(
        { success: false, message: 'Slug đã tồn tại' },
        { status: 400 }
      );
    }

    // Determine level based on parent
    let level = 0;
    if (parentId) {
      const parent = await prisma.category.findUnique({
        where: { id: parentId },
        select: { level: true }
      });
      if (parent) {
        level = parent.level + 1;
      }
    }

    // Prevent setting parent as itself or its children
    if (parentId === id) {
      return NextResponse.json(
        { success: false, message: 'Không thể đặt danh mục làm cha của chính nó' },
        { status: 400 }
      );
    }

    const category = await prisma.category.update({
      where: { id },
      data: {
        name,
        slug,
        description,
        image,
        parentId,
        level,
        sortOrder: sortOrder || 0,
        isActive
      },
      include: {
        parent: {
          select: { id: true, name: true }
        },
        _count: {
          select: { products: true }
        }
      }
    });

    // Log admin activity
    await prisma.adminLog.create({
      data: {
        adminId: admin.id,
        action: 'UPDATE',
        resource: 'CATEGORY',
        resourceId: category.id,
        details: { categoryName: category.name },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || '',
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Danh mục đã được cập nhật thành công',
      data: category
    });
  } catch (error) {
    console.error('Error updating category:', error);
    return NextResponse.json(
      { success: false, error: 'Lỗi khi cập nhật danh mục' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;

    // Check if category exists
    const category = await prisma.category.findUnique({
      where: { id },
      include: {
        _count: {
          select: { products: true, children: true }
        }
      }
    });

    if (!category) {
      return NextResponse.json(
        { success: false, message: 'Danh mục không tồn tại' },
        { status: 404 }
      );
    }

    // Check if category has products
    if (category._count.products > 0) {
      return NextResponse.json(
        { success: false, message: `Không thể xóa danh mục có ${category._count.products} sản phẩm` },
        { status: 400 }
      );
    }

    // Check if category has children
    if (category._count.children > 0) {
      return NextResponse.json(
        { success: false, message: `Không thể xóa danh mục có ${category._count.children} danh mục con` },
        { status: 400 }
      );
    }

    await prisma.category.delete({
      where: { id }
    });

    // Log admin activity
    await prisma.adminLog.create({
      data: {
        adminId: admin.id,
        action: 'DELETE',
        resource: 'CATEGORY',
        resourceId: id,
        details: { categoryName: category.name },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || '',
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Danh mục đã được xóa thành công'
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    return NextResponse.json(
      { success: false, error: 'Lỗi khi xóa danh mục' },
      { status: 500 }
    );
  }
}
