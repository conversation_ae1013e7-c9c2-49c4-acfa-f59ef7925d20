import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { prisma } from '../../../../src/lib/prisma';

const JWT_SECRET = process.env.ADMIN_JWT_SECRET || 'fallback-secret';

// Middleware to verify admin authentication
async function verifyAdmin(request: NextRequest) {
  const token = request.cookies.get('admin-token')?.value;

  if (!token) {
    return null;
  }

  try {
    const payload = jwt.verify(token, JWT_SECRET) as { adminId: string; email: string; role: string };

    const admin = await prisma.adminUser.findUnique({
      where: { id: payload.adminId },
      select: { id: true, email: true, name: true, role: true, isActive: true },
    });

    if (!admin || !admin.isActive) {
      return null;
    }

    return admin;
  } catch (error) {
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const parentId = searchParams.get('parentId');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (parentId) {
      where.parentId = parentId;
    } else if (parentId === null) {
      where.parentId = null; // Root categories only
    }

    const [categories, total] = await Promise.all([
      prisma.category.findMany({
        where,
        skip,
        take: limit,
        orderBy: [
          { sortOrder: 'asc' },
          { name: 'asc' }
        ],
        include: {
          parent: {
            select: { id: true, name: true }
          },
          children: {
            select: { id: true, name: true, isActive: true }
          },
          _count: {
            select: { productCategories: true }
          }
        }
      }),
      prisma.category.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        data: categories, // Frontend expects data.data
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit) // Frontend expects totalPages
        }
      }
    });
  } catch (error) {
    console.error('Categories fetch error:', error);
    return NextResponse.json(
      { success: false, message: 'Lỗi server nội bộ' },
      { status: 500 }
    );
  }
}

// POST - Create new category
export async function POST(request: NextRequest) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, slug, description, image, parentId, sortOrder } = body;

    if (!name || !slug) {
      return NextResponse.json(
        { success: false, message: 'Tên và slug là bắt buộc' },
        { status: 400 }
      );
    }

    // Check if slug already exists
    const existingCategory = await prisma.category.findUnique({
      where: { slug }
    });

    if (existingCategory) {
      return NextResponse.json(
        { success: false, message: 'Slug đã tồn tại' },
        { status: 400 }
      );
    }

    // Determine level based on parent
    let level = 0;
    if (parentId) {
      const parent = await prisma.category.findUnique({
        where: { id: parentId },
        select: { level: true }
      });
      if (parent) {
        level = parent.level + 1;
      }
    }

    const category = await prisma.category.create({
      data: {
        name,
        slug,
        description,
        image,
        parentId,
        level,
        sortOrder: sortOrder || 0,
        isActive: true
      },
      include: {
        parent: {
          select: { id: true, name: true }
        },
        _count: {
          select: { productCategories: true }
        }
      }
    });

    // Log admin activity
    await prisma.adminLog.create({
      data: {
        adminId: admin.id,
        action: 'CREATE',
        resource: 'CATEGORY',
        resourceId: category.id,
        details: { categoryName: category.name },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || '',
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Danh mục đã được tạo thành công',
      data: category
    });
  } catch (error) {
    console.error('Category creation error:', error);
    return NextResponse.json(
      { success: false, message: 'Lỗi server nội bộ' },
      { status: 500 }
    );
  }
}
