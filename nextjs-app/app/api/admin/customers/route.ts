import { NextRequest, NextResponse } from 'next/server';
import { validateAdminSession } from '../../../../src/lib/auth';
import prisma from '../../../../src/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Get token from cookie
    const token = request.cookies.get('admin-token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify token
    const { verifyToken } = await import('../../../../src/lib/auth');
    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'orderCount';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build search filter
    const searchFilter = search ? {
      OR: [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } },
      ]
    } : {};

    // Get customers with aggregated order data
    const customers = await prisma.user.findMany({
      where: {
        ...searchFilter,
        // Only include users who have placed orders
        orders: {
          some: {}
        }
      },
      include: {
        orders: {
          select: {
            id: true,
            total: true,
            status: true,
            createdAt: true,
            shippingAddress: true,
          }
        },
        _count: {
          select: {
            orders: true,
          }
        }
      },
      skip,
      take: limit,
      orderBy: sortBy === 'orderCount' ? {
        orders: {
          _count: sortOrder as 'asc' | 'desc'
        }
      } : sortBy === 'totalValue' ? {
        orders: {
          _count: sortOrder as 'asc' | 'desc' // We'll calculate total value in post-processing
        }
      } : {
        [sortBy]: sortOrder as 'asc' | 'desc'
      }
    });

    // Calculate additional customer metrics
    const customersWithMetrics = customers.map(customer => {
      const orders = customer.orders;
      const totalValue = orders.reduce((sum, order) => sum + order.total, 0);
      const firstOrderDate = orders.length > 0 ? 
        new Date(Math.min(...orders.map(o => new Date(o.createdAt).getTime()))) : null;
      const lastOrderDate = orders.length > 0 ? 
        new Date(Math.max(...orders.map(o => new Date(o.createdAt).getTime()))) : null;

      return {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        createdAt: customer.createdAt,
        orderCount: customer._count.orders,
        totalValue,
        firstOrderDate,
        lastOrderDate,
        // Get most recent shipping address from orders
        lastShippingAddress: orders.length > 0 ?
          orders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0].shippingAddress : null
      };
    });

    // Sort by total value if requested (post-processing)
    if (sortBy === 'totalValue') {
      customersWithMetrics.sort((a, b) => {
        return sortOrder === 'desc' ? b.totalValue - a.totalValue : a.totalValue - b.totalValue;
      });
    }

    // Get total count for pagination
    const totalCount = await prisma.user.count({
      where: {
        ...searchFilter,
        orders: {
          some: {}
        }
      }
    });

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      customers: customersWithMetrics,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    });

  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Get customer details with full order history
export async function POST(request: NextRequest) {
  try {
    // Get token from cookie
    const token = request.cookies.get('admin-token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify token
    const { verifyToken } = await import('../../../../src/lib/auth');
    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const { customerId } = await request.json();

    if (!customerId) {
      return NextResponse.json({ error: 'Customer ID is required' }, { status: 400 });
    }

    // Get customer with full order details
    const customer = await prisma.user.findUnique({
      where: { id: customerId },
      include: {
        orders: {
          include: {
            orderItems: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    images: true,
                    price: true,
                  }
                }
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        },
        _count: {
          select: {
            orders: true,
            wishlistItems: true,
            cartItems: true,
          }
        }
      }
    });

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 });
    }

    // Calculate customer metrics
    const totalValue = customer.orders.reduce((sum, order) => sum + order.total, 0);
    const averageOrderValue = customer.orders.length > 0 ? totalValue / customer.orders.length : 0;

    return NextResponse.json({
      customer: {
        ...customer,
        totalValue,
        averageOrderValue,
      }
    });

  } catch (error) {
    console.error('Error fetching customer details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
