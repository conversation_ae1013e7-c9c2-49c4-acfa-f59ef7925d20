import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { prisma } from '../../../../../src/lib/prisma';

const JWT_SECRET = process.env.ADMIN_JWT_SECRET || 'fallback-secret';

// Middleware to verify admin authentication
async function verifyAdmin(request: NextRequest) {
  const token = request.cookies.get('admin-token')?.value;

  if (!token) {
    return null;
  }

  try {
    const payload = jwt.verify(token, JWT_SECRET) as { adminId: string; email: string; role: string };

    const admin = await prisma.adminUser.findUnique({
      where: { id: payload.adminId },
      select: { id: true, email: true, name: true, role: true, isActive: true },
    });

    if (!admin || !admin.isActive) {
      return null;
    }

    return admin;
  } catch (error) {
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get recent activities from the last 24 hours
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Get recent orders
    const recentOrders = await prisma.order.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      where: { createdAt: { gte: yesterday } },
      include: {
        user: {
          select: { name: true, email: true }
        }
      }
    });

    // Get recent customers (new registrations)
    const recentCustomers = await prisma.user.findMany({
      take: 2,
      orderBy: { createdAt: 'desc' },
      where: { createdAt: { gte: yesterday } },
      select: { id: true, name: true, email: true, createdAt: true }
    });

    // Get recently added products
    const recentProducts = await prisma.product.findMany({
      take: 2,
      orderBy: { createdAt: 'desc' },
      where: { createdAt: { gte: yesterday } },
      select: { id: true, name: true, createdAt: true }
    });

    // Combine and format activities
    const activities = [];

    // Add order activities
    recentOrders.forEach(order => {
      activities.push({
        id: `order-${order.id}`,
        type: 'order',
        title: `Đơn hàng mới ${order.orderNumber}`,
        description: order.user?.name || order.user?.email || 'Khách hàng',
        time: order.createdAt,
        icon: 'ShoppingCart',
        color: 'text-green-600',
        bgColor: 'bg-green-100',
      });
    });

    // Add customer activities
    recentCustomers.forEach(customer => {
      activities.push({
        id: `customer-${customer.id}`,
        type: 'customer',
        title: 'Khách hàng mới đăng ký',
        description: customer.name || customer.email,
        time: customer.createdAt,
        icon: 'Users',
        color: 'text-purple-600',
        bgColor: 'bg-purple-100',
      });
    });

    // Add product activities
    recentProducts.forEach(product => {
      activities.push({
        id: `product-${product.id}`,
        type: 'product',
        title: 'Sản phẩm mới được thêm',
        description: product.name,
        time: product.createdAt,
        icon: 'Package',
        color: 'text-blue-600',
        bgColor: 'bg-blue-100',
      });
    });

    // Sort by time and take top 5
    activities.sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());
    const topActivities = activities.slice(0, 5);

    // Format time to relative time
    const formatRelativeTime = (date: Date) => {
      const now = new Date();
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      
      if (diffInMinutes < 1) return 'Vừa xong';
      if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;
      
      const diffInHours = Math.floor(diffInMinutes / 60);
      if (diffInHours < 24) return `${diffInHours} giờ trước`;
      
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} ngày trước`;
    };

    const formattedActivities = topActivities.map(activity => ({
      ...activity,
      timeFormatted: formatRelativeTime(new Date(activity.time))
    }));

    return NextResponse.json({
      success: true,
      data: formattedActivities
    });
  } catch (error) {
    console.error('Dashboard activities error:', error);
    return NextResponse.json(
      { success: false, message: 'Lỗi server nội bộ' },
      { status: 500 }
    );
  }
}
