import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { prisma } from '../../../../../src/lib/prisma';

const JWT_SECRET = process.env.ADMIN_JWT_SECRET || 'fallback-secret';

// Middleware to verify admin authentication
async function verifyAdmin(request: NextRequest) {
  const token = request.cookies.get('admin-token')?.value;

  if (!token) {
    return null;
  }

  try {
    const payload = jwt.verify(token, JWT_SECRET) as { adminId: string; email: string; role: string };

    const admin = await prisma.adminUser.findUnique({
      where: { id: payload.adminId },
      select: { id: true, email: true, name: true, role: true, isActive: true },
    });

    if (!admin || !admin.isActive) {
      return null;
    }

    return admin;
  } catch (error) {
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get current date for calculations
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    // Get basic counts
    const [
      totalProducts,
      totalCategories,
      totalOrders,
      totalCustomers,
      lowStockProducts,
    ] = await Promise.all([
      prisma.product.count({ where: { status: 'ACTIVE' } }),
      prisma.category.count({ where: { isActive: true } }),
      prisma.order.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.product.count({ where: { stock: { lte: 10 }, status: 'ACTIVE' } }),
    ]);

    // Calculate total revenue and monthly stats
    const [
      totalRevenue,
      monthlyOrders,
      lastMonthOrders,
      monthlyRevenue,
      lastMonthRevenue,
      recentOrders
    ] = await Promise.all([
      // Total revenue
      prisma.order.aggregate({
        _sum: { total: true },
        where: { status: { in: ['DELIVERED', 'CONFIRMED'] } }
      }),

      // Monthly orders
      prisma.order.count({
        where: { createdAt: { gte: startOfMonth } }
      }),

      // Last month orders
      prisma.order.count({
        where: {
          createdAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        }
      }),

      // Monthly revenue
      prisma.order.aggregate({
        _sum: { total: true },
        where: {
          createdAt: { gte: startOfMonth },
          status: { in: ['DELIVERED', 'CONFIRMED'] }
        }
      }),

      // Last month revenue
      prisma.order.aggregate({
        _sum: { total: true },
        where: {
          createdAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          },
          status: { in: ['DELIVERED', 'CONFIRMED'] }
        }
      }),

      // Recent orders
      prisma.order.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: { name: true, email: true }
          }
        }
      })
    ]);

    // Get top products by sales
    const topProductsData = await prisma.orderItem.groupBy({
      by: ['productId'],
      _sum: { quantity: true },
      _count: { productId: true },
      orderBy: { _sum: { quantity: 'desc' } },
      take: 5
    });

    // Get product details for top products
    const topProducts = await Promise.all(
      topProductsData.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          select: {
            id: true,
            name: true,
            price: true,
            images: true,
            category: {
              select: { name: true }
            }
          }
        });
        return {
          ...product,
          totalSold: item._sum.quantity || 0,
          orderCount: item._count.productId
        };
      })
    );

    // Calculate percentage changes
    const orderChange = lastMonthOrders > 0
      ? ((monthlyOrders - lastMonthOrders) / lastMonthOrders * 100).toFixed(1)
      : '0';

    const revenueChange = (lastMonthRevenue._sum.total || 0) > 0
      ? (((monthlyRevenue._sum.total || 0) - (lastMonthRevenue._sum.total || 0)) / (lastMonthRevenue._sum.total || 0) * 100).toFixed(1)
      : '0';

    // Get order status distribution
    const orderStatusStats = await prisma.order.groupBy({
      by: ['status'],
      _count: {
        status: true,
      },
    });

    const stats = {
      totalProducts,
      totalCategories,
      totalOrders,
      totalCustomers,
      totalRevenue: totalRevenue._sum.total || 0,
      lowStockProducts,
      monthlyOrders,
      monthlyRevenue: monthlyRevenue._sum.total || 0,
      orderChange: parseFloat(orderChange),
      revenueChange: parseFloat(revenueChange),
      recentOrders: recentOrders.map(order => ({
        id: order.id,
        orderNumber: order.orderNumber,
        total: order.total,
        status: order.status,
        customerName: order.user?.name || 'Khách hàng',
        customerEmail: order.user?.email,
        createdAt: order.createdAt
      })),
      topProducts: topProducts.filter(p => p !== null),
      orderStatusStats
    };

    return NextResponse.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { success: false, message: 'Lỗi server nội bộ' },
      { status: 500 }
    );
  }
}
