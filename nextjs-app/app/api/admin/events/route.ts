import { NextRequest } from 'next/server';
import { requireAdminAuth } from '../../../../src/lib/adminAuth';

// Store active connections
const connections = new Set<ReadableStreamDefaultController>();

export async function GET(request: NextRequest) {
  const authResult = await requireAdminAuth(request);
  if (authResult instanceof Response) {
    return authResult;
  }

  // Create a readable stream for Server-Sent Events
  const stream = new ReadableStream({
    start(controller) {
      // Add this connection to our set
      connections.add(controller);

      // Send initial connection message
      const data = JSON.stringify({
        type: 'connected',
        timestamp: new Date().toISOString(),
        message: 'Kết nối real-time thành công'
      });
      
      controller.enqueue(`data: ${data}\n\n`);

      // Send periodic heartbeat
      const heartbeat = setInterval(() => {
        try {
          const heartbeatData = JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().toISOString()
          });
          controller.enqueue(`data: ${heartbeatData}\n\n`);
        } catch (error) {
          clearInterval(heartbeat);
          connections.delete(controller);
        }
      }, 30000); // Every 30 seconds

      // Clean up when connection closes
      request.signal.addEventListener('abort', () => {
        clearInterval(heartbeat);
        connections.delete(controller);
        try {
          controller.close();
        } catch (error) {
          // Connection already closed
        }
      });
    },
    cancel() {
      // Connection was cancelled
      connections.delete(this as any);
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  });
}

// Function to broadcast events to all connected admin clients
export function broadcastToAdmins(event: {
  type: string;
  data: any;
  message?: string;
}) {
  const eventData = JSON.stringify({
    ...event,
    timestamp: new Date().toISOString(),
  });

  // Send to all connected clients
  connections.forEach((controller) => {
    try {
      controller.enqueue(`data: ${eventData}\n\n`);
    } catch (error) {
      // Remove failed connections
      connections.delete(controller);
    }
  });
}

// Export for use in other API routes
export { connections };
