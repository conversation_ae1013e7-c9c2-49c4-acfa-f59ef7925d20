import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAdminAuth } from '../../../../../../src/lib/auth';

const prisma = new PrismaClient();

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const orderId = params.id;

    // Get current order
    const existingOrder = await prisma.order.findUnique({
      where: { id: orderId },
      select: { id: true, orderNumber: true, status: true }
    });

    if (!existingOrder) {
      return NextResponse.json(
        { error: 'Đơn hàng không tồn tại' },
        { status: 404 }
      );
    }

    // Business rule: Cannot cancel DELIVERED orders
    if (existingOrder.status === 'DELIVERED') {
      return NextResponse.json(
        { error: 'Không thể hủy đơn hàng đã giao thành công' },
        { status: 400 }
      );
    }

    // Business rule: Cannot cancel already CANCELLED orders
    if (existingOrder.status === 'CANCELLED') {
      return NextResponse.json(
        { error: 'Đơn hàng đã được hủy trước đó' },
        { status: 400 }
      );
    }

    // Parse request body for cancellation reason
    const body = await request.json();
    const { cancelReason } = body;

    // Update order status to CANCELLED
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: {
        status: 'CANCELLED',
        cancelReason: cancelReason || 'Hủy bởi admin',
        updatedAt: new Date()
      },
      include: {
        user: {
          select: { name: true, email: true, phone: true }
        },
        orderItems: {
          include: {
            product: {
              select: { name: true, slug: true, images: true, price: true, categoryId: true }
            }
          }
        }
      }
    });

    // Log the cancellation action
    console.log(`✅ Order cancelled: ${existingOrder.orderNumber} by admin ${authResult.admin.email}`);

    return NextResponse.json({
      success: true,
      message: `Đơn hàng ${existingOrder.orderNumber} đã được hủy thành công`,
      order: updatedOrder
    });

  } catch (error) {
    console.error('❌ Error cancelling order:', error);
    return NextResponse.json(
      { error: 'Lỗi hệ thống khi hủy đơn hàng' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
