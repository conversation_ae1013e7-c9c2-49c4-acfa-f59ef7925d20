import { NextRequest, NextResponse } from 'next/server';
import { requireAdminAuth, getClientIP, getUserAgent } from '../../../../../../src/lib/adminAuth';
import { prisma } from '../../../../../../src/lib/db';
import { logAdminAction } from '../../../../../../src/lib/auth';

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const authResult = await requireAdminAuth(request);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const { id: orderId } = await params;

    // Get current order with all related data
    const existingOrder = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        user: {
          select: { name: true, email: true }
        },
        orderItems: {
          select: { id: true, quantity: true, unitPrice: true }
        }
      }
    });

    if (!existingOrder) {
      return NextResponse.json(
        { success: false, error: 'Đơn hàng không tồn tại' },
        { status: 404 }
      );
    }

    // Business rule: Allow deletion of orders in ANY status
    console.log(`🗑️ Admin ${authResult.user.email} deleting order ${existingOrder.orderNumber} (${existingOrder.status}) - ${existingOrder.orderItems.length} items`);

    // Use transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Delete order items first (foreign key constraint)
      const deletedItems = await tx.orderItem.deleteMany({
        where: { orderId: orderId }
      });

      // Delete the order
      const deletedOrder = await tx.order.delete({
        where: { id: orderId }
      });

      return { deletedOrder, deletedItemsCount: deletedItems.count };
    });

    // Log admin action for audit trail
    await logAdminAction(
      authResult.user.id,
      'DELETE',
      'ORDER',
      orderId,
      {
        orderNumber: existingOrder.orderNumber,
        status: existingOrder.status,
        total: existingOrder.total,
        customerEmail: existingOrder.user.email,
        itemsDeleted: result.deletedItemsCount
      },
      getClientIP(request),
      getUserAgent(request)
    );

    console.log(`✅ Order deleted successfully: ${existingOrder.orderNumber} (${existingOrder.status}) by ${authResult.user.email}`);

    return NextResponse.json({
      success: true,
      message: `Đơn hàng ${existingOrder.orderNumber} (${existingOrder.status}) đã được xóa vĩnh viễn thành công`,
      data: {
        orderNumber: existingOrder.orderNumber,
        status: existingOrder.status,
        total: existingOrder.total,
        customerName: existingOrder.user?.name,
        itemsDeleted: result.deletedItemsCount
      }
    });

  } catch (error) {
    console.error('❌ Error deleting order:', error);

    // Handle specific database errors
    if (error instanceof Error) {
      if (error.message.includes('foreign key constraint')) {
        return NextResponse.json(
          { success: false, error: 'Không thể xóa đơn hàng do có dữ liệu liên quan' },
          { status: 400 }
        );
      }

      if (error.message.includes('Record to delete does not exist')) {
        return NextResponse.json(
          { success: false, error: 'Đơn hàng không tồn tại' },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Lỗi hệ thống khi xóa đơn hàng',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
