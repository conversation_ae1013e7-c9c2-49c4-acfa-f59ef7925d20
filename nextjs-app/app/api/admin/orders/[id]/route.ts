import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../src/lib/db';
import { requireAdminAuth, getClientIP, getUserAgent } from '../../../../../src/lib/adminAuth';
import { logAdminAction } from '../../../../../src/lib/auth';
import { ApiResponse } from '../../../../../src/types';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const authResult = await requireAdminAuth(request);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const { id } = await params;

    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
                images: true,
                price: true,
                category: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!order) {
      return NextResponse.json(
        { success: false, error: 'Không tìm thấy đơn hàng' },
        { status: 404 }
      );
    }

    const response: ApiResponse<typeof order> = {
      success: true,
      data: order,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching order:', error);
    return NextResponse.json(
      { success: false, error: 'Lỗi khi tải đơn hàng' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const authResult = await requireAdminAuth(request);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const { id } = await params;
    const body = await request.json();
    const {
      status,
      shippingAddress,
      trackingNumber,
      adminNotes,
      cancelReason,
      estimatedDelivery
    } = body;

    if (!status) {
      return NextResponse.json(
        { success: false, error: 'Trạng thái đơn hàng là bắt buộc' },
        { status: 400 }
      );
    }

    // Validate status
    const validStatuses = ['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Trạng thái đơn hàng không hợp lệ' },
        { status: 400 }
      );
    }

    // Check if order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!existingOrder) {
      return NextResponse.json(
        { success: false, error: 'Không tìm thấy đơn hàng' },
        { status: 404 }
      );
    }

    // Validate status transition
    const statusTransitions: Record<string, string[]> = {
      PENDING: ['CONFIRMED', 'CANCELLED'],
      CONFIRMED: ['PROCESSING', 'CANCELLED'],
      PROCESSING: ['SHIPPED', 'CANCELLED'],
      SHIPPED: ['DELIVERED'],
      DELIVERED: [],
      CANCELLED: [],
    };

    if (!statusTransitions[existingOrder.status].includes(status)) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Không thể chuyển từ trạng thái ${existingOrder.status} sang ${status}` 
        },
        { status: 400 }
      );
    }

    const updateData: any = { status };
    if (shippingAddress) {
      updateData.shippingAddress = shippingAddress;
    }
    if (trackingNumber !== undefined) {
      updateData.trackingNumber = trackingNumber;
    }
    if (adminNotes !== undefined) {
      updateData.adminNotes = adminNotes;
    }
    if (cancelReason !== undefined) {
      updateData.cancelReason = cancelReason;
    }
    if (estimatedDelivery !== undefined) {
      updateData.estimatedDelivery = estimatedDelivery ? new Date(estimatedDelivery) : null;
    }

    // Auto-set delivered date if status is DELIVERED
    if (status === 'DELIVERED') {
      updateData.deliveredAt = new Date();
    }

    const updatedOrder = await prisma.order.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                images: true,
                price: true,
              },
            },
          },
        },
      },
    });

    // Log admin action
    await logAdminAction(
      authResult.user.id,
      'UPDATE',
      'ORDER',
      id,
      { 
        oldStatus: existingOrder.status,
        newStatus: status,
        customerEmail: existingOrder.user.email,
        orderTotal: existingOrder.total,
      },
      getClientIP(request),
      getUserAgent(request)
    );

    // TODO: Send notification email to customer about status change
    // await sendOrderStatusUpdateEmail(updatedOrder);

    const response: ApiResponse<typeof updatedOrder> = {
      success: true,
      data: updatedOrder,
      message: 'Đơn hàng đã được cập nhật thành công',
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error updating order:', error);
    return NextResponse.json(
      { success: false, error: 'Lỗi khi cập nhật đơn hàng' },
      { status: 500 }
    );
  }
}

// DELETE method removed - Use /api/admin/orders/[id]/delete endpoint instead
// That endpoint allows deletion of orders in ANY status without restrictions
