import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { prisma } from '../../../../../src/lib/prisma';

const JWT_SECRET = process.env.ADMIN_JWT_SECRET || 'fallback-secret';

// Middleware to verify admin authentication
async function verifyAdmin(request: NextRequest) {
  try {
    const token = request.cookies.get('admin-token')?.value;

    if (!token) {
  
      return null;
    }

    const decoded = jwt.verify(token, JWT_SECRET) as { adminId: string; email: string; role: string };
    const admin = await prisma.adminUser.findUnique({
      where: { id: decoded.adminId, isActive: true },
      select: { id: true, email: true, name: true, role: true, isActive: true },
    });

    if (!admin) {
      console.log('❌ Admin not found or inactive');
      return null;
    }

    console.log('✅ Admin verified:', admin.email);
    return admin;
  } catch (error) {
    console.log('❌ Token verification failed:', error);
    return null;
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        productCategories: {
          include: {
            category: {
              select: { id: true, name: true, slug: true }
            }
          }
        },
        variants: {
          where: { isActive: true },
          select: {
            id: true,
            name: true,
            attributes: true,
            price: true,
            stock: true
          },
          orderBy: { name: 'asc' }
        },
        reviews: {
          select: {
            id: true,
            rating: true,
            comment: true,
            author: true,
            reviewTime: true,
            images: true,
            isApproved: true,
            createdAt: true,
            user: {
              select: {
                name: true,
                email: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 50 // Limit to recent 50 reviews
        },
        _count: {
          select: {
            reviews: true,
            wishlistItems: true,
            cartItems: true,
          },
        },
      }
    });

    if (!product) {
      return NextResponse.json(
        { success: false, message: 'Sản phẩm không tồn tại' },
        { status: 404 }
      );
    }

    // Format response with enhanced data
    const formattedProduct = {
      ...product,
      categories: product.productCategories.map(pc => pc.category),
      category: product.productCategories[0]?.category || null, // For backward compatibility
    };

    return NextResponse.json({
      success: true,
      data: formattedProduct
    });
  } catch (error) {
    console.error('Product fetch error:', error);
    return NextResponse.json(
      { success: false, message: 'Lỗi server nội bộ' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log('🔄 PUT request received for product update');

    const admin = await verifyAdmin(request);
    if (!admin) {
      console.log('❌ Admin verification failed');
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    console.log('📝 Updating product ID:', id);

    const body = await request.json();
    console.log('📦 Request body received:', Object.keys(body));
    
    const {
      name,
      slug,
      shortDescription,
      description,
      images,
      categoryId, // For backward compatibility
      categoryIds, // New multiple categories
      colors,
      sizes,
      brand,
      sku,
      price,
      originalPrice,
      stock,
      tags,
      featured,
      status,
      seoTitle,
      seoDescription,
      seoKeywords,
    } = body;

    // Determine which categories to use (new multiple or legacy single)
    let categoriesToUse: string[] = [];
    if (categoryIds && Array.isArray(categoryIds) && categoryIds.length > 0) {
      categoriesToUse = categoryIds;
    } else if (categoryId) {
      categoriesToUse = [categoryId];
    }

    // Validation
    if (!name || !slug || !sku || !price || categoriesToUse.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Tên, slug, SKU, giá và ít nhất một danh mục là bắt buộc' },
        { status: 400 }
      );
    }

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id }
    });

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, message: 'Sản phẩm không tồn tại' },
        { status: 404 }
      );
    }

    // Validate price
    const numericPrice = parseFloat(price.toString());
    const numericOriginalPrice = originalPrice ? parseFloat(originalPrice.toString()) : null;
    const numericStock = parseInt(stock.toString()) || 0;

    if (numericPrice <= 0) {
      return NextResponse.json(
        { success: false, message: 'Giá phải lớn hơn 0' },
        { status: 400 }
      );
    }

    // Validate original price if provided
    if (numericOriginalPrice && numericOriginalPrice <= numericPrice) {
      return NextResponse.json(
        { success: false, message: 'Giá gốc phải lớn hơn giá bán' },
        { status: 400 }
      );
    }

    // Validate stock
    if (numericStock < 0) {
      return NextResponse.json(
        { success: false, message: 'Số lượng tồn kho không được âm' },
        { status: 400 }
      );
    }

    // Check if slug already exists (excluding current product)
    const slugExists = await prisma.product.findFirst({
      where: {
        slug,
        id: { not: id }
      }
    });

    if (slugExists) {
      return NextResponse.json(
        { success: false, message: 'Slug đã tồn tại' },
        { status: 400 }
      );
    }

    // Check if SKU already exists (excluding current product)
    const skuExists = await prisma.product.findFirst({
      where: {
        sku,
        id: { not: id }
      }
    });

    if (skuExists) {
      return NextResponse.json(
        { success: false, message: 'SKU đã tồn tại' },
        { status: 400 }
      );
    }

    // Validate all categories exist
    const categories = await prisma.category.findMany({
      where: { id: { in: categoriesToUse } },
    });

    if (categories.length !== categoriesToUse.length) {
      return NextResponse.json(
        { success: false, message: 'Một hoặc nhiều danh mục không tồn tại' },
        { status: 400 }
      );
    }

    console.log('💾 Starting database transaction...');

    // Use transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      try {
        // Prepare tag array
        const tagsArray = typeof tags === 'string' 
          ? tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) 
          : (Array.isArray(tags) ? tags : []);

        // Prepare keyword array
        const keywordsArray = typeof seoKeywords === 'string' 
          ? seoKeywords.split(',').map((keyword: string) => keyword.trim()).filter(Boolean) 
          : (Array.isArray(seoKeywords) ? seoKeywords : []);

        // Prepare sizes array
        const sizesArray = Array.isArray(sizes) 
          ? sizes.map(s => typeof s === 'string' ? s : (s.name || '')) 
          : [];

                 // Update product basic info
         await tx.product.update({
           where: { id },
           data: {
            name,
            slug,
            shortDescription: shortDescription || '',
            description: description || '',
            images: Array.isArray(images) ? images : [],
            colors: Array.isArray(colors) ? colors : [],
            sizes: sizesArray,
            brand: brand || '',
            sku,
            price: numericPrice,
            originalPrice: numericOriginalPrice,
            stock: numericStock,
            tags: tagsArray,
            featured: Boolean(featured),
            status: status || 'ACTIVE',
            seoTitle: seoTitle || name,
            seoDescription: seoDescription || shortDescription || '',
            seoKeywords: keywordsArray,
            updatedAt: new Date()
          },
        });

        console.log('✅ Product basic info updated');

        // Update product-category relationships
        // First, delete existing relationships
        await tx.productCategory.deleteMany({
          where: { productId: id }
        });

        // Then create new relationships
        if (categoriesToUse.length > 0) {
          await tx.productCategory.createMany({
            data: categoriesToUse.map(categoryId => ({
              productId: id,
              categoryId
            }))
          });
        }

        console.log('✅ Product-category relationships updated');

        // Handle variants from sizes if provided
        if (sizes && Array.isArray(sizes) && sizes.length > 0) {
          console.log('🔄 Processing variants from sizes:', sizes.length);

          // Delete existing variants
          await tx.productVariant.deleteMany({
            where: { productId: id }
          });

                     // Create new variants from sizes
           const variantData = sizes.map((size: { name?: string; price?: number; stock?: number }, index: number) => {
            const sizeName = String(size.name || '').trim();
            if (!sizeName) {
              throw new Error(`Tên kích thước không được để trống (kích thước ${index + 1})`);
            }

            // Generate unique SKU for variant
            const cleanSizeName = sizeName.toUpperCase().replace(/[^A-Z0-9]/g, '');
            const variantSku = `${sku}-${cleanSizeName}`;

            return {
              productId: id,
              name: sizeName,
              sku: variantSku,
              attributes: { size: sizeName },
              price: size.price ? parseFloat(size.price.toString()) : numericPrice,
              stock: size.stock ? parseInt(size.stock.toString()) : 0,
              isActive: true,
            };
          });

          await tx.productVariant.createMany({
            data: variantData,
          });

          console.log('✅ Created', variantData.length, 'variants successfully');
        } else {
          // If no sizes provided, delete existing variants
          await tx.productVariant.deleteMany({
            where: { productId: id }
          });
          console.log('✅ Cleared variants (no sizes provided)');
        }

        // Return updated product with relationships
        return await tx.product.findUnique({
          where: { id },
          include: {
            productCategories: {
              include: {
                category: {
                  select: { id: true, name: true, slug: true }
                }
              }
            },
            variants: {
              where: { isActive: true },
              select: {
                id: true,
                name: true,
                attributes: true,
                price: true,
                stock: true
              }
            },
            _count: {
              select: {
                reviews: true,
                wishlistItems: true,
                cartItems: true,
              },
            },
          },
        });

      } catch (error) {
        console.error('❌ Transaction error:', error);
        throw error; // This will cause the transaction to rollback
      }
    });

    console.log('✅ Transaction completed successfully');

    // Log admin activity
    try {
      await prisma.adminLog.create({
        data: {
          adminId: admin.id,
          action: 'UPDATE',
          resource: 'PRODUCT',
          resourceId: result.id,
          details: { productName: result.name, sku: result.sku },
          ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || '',
        },
      });
    } catch (logError) {
      console.warn('Failed to log admin activity:', logError);
      // Don't fail the main operation if logging fails
    }

    // Format response with enhanced data
    const formattedProduct = {
      ...result,
      categories: result?.productCategories?.map(pc => pc.category) || [],
      category: result?.productCategories?.[0]?.category || null, // For backward compatibility
    };

    return NextResponse.json({
      success: true,
      message: 'Sản phẩm đã được cập nhật thành công',
      data: formattedProduct
    });

  } catch (error) {
    console.error('❌ Product update error:', error);
    
    // Handle specific Prisma errors
    if (error && typeof error === 'object' && 'code' in error) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { success: false, message: 'Slug hoặc SKU đã tồn tại' },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { success: false, message: 'Sản phẩm không tồn tại' },
            { status: 404 }
          );
        case 'P2003':
          return NextResponse.json(
            { success: false, message: 'Ràng buộc dữ liệu không hợp lệ' },
            { status: 400 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code);
      }
    }

    return NextResponse.json(
      { success: false, message: error.message || 'Lỗi server nội bộ' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log('🗑️ DELETE request received');

    const admin = await verifyAdmin(request);
    if (!admin) {
      console.log('❌ Admin verification failed');
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    console.log('🔍 Deleting product with ID:', id);

    // Check if product exists with all dependencies
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            reviews: true,
            wishlistItems: true,
            cartItems: true,
            orderItems: true,
          },
        },
        category: {
          select: {
            name: true,
          },
        },
      }
    });

    if (!product) {
      return NextResponse.json(
        { success: false, message: 'Sản phẩm không tồn tại hoặc đã bị xóa' },
        { status: 404 }
      );
    }

    // Check critical dependencies that prevent deletion
    const criticalDependencies: string[] = [];
    
    if (product._count.orderItems > 0) {
      criticalDependencies.push(`${product._count.orderItems} đơn hàng`);
    }

    if (criticalDependencies.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: `Không thể xóa sản phẩm vì đã có trong ${criticalDependencies.join(', ')}. Sản phẩm chỉ có thể bị ẩn thay vì xóa.` 
        },
        { status: 400 }
      );
    }

    // Warn about soft dependencies but allow deletion
    const softDependencies: string[] = [];
    if (product._count.cartItems > 0) {
      softDependencies.push(`${product._count.cartItems} giỏ hàng`);
    }
    if (product._count.reviews > 0) {
      softDependencies.push(`${product._count.reviews} đánh giá`);
    }
    if (product._count.wishlistItems > 0) {
      softDependencies.push(`${product._count.wishlistItems} danh sách yêu thích`);
    }

    // Use transaction to ensure data consistency
    console.log('🔄 Starting transaction to delete product and dependencies');
    await prisma.$transaction(async (tx) => {
      // Delete soft dependencies in the correct order
      if (product._count.cartItems > 0) {
        console.log(`🗑️ Deleting ${product._count.cartItems} cart items`);
        await tx.cartItem.deleteMany({
          where: { productId: id }
        });
      }

      if (product._count.wishlistItems > 0) {
        console.log(`🗑️ Deleting ${product._count.wishlistItems} wishlist items`);
        await tx.wishlistItem.deleteMany({
          where: { productId: id }
        });
      }

      if (product._count.reviews > 0) {
        console.log(`🗑️ Deleting ${product._count.reviews} reviews`);
        await tx.review.deleteMany({
          where: { productId: id }
        });
      }

      // Finally delete the product
      console.log('🗑️ Deleting product from database');
      await tx.product.delete({
        where: { id }
      });

      // Log admin activity with detailed information
      await tx.adminLog.create({
        data: {
          adminId: admin.id,
          action: 'DELETE',
          resource: 'PRODUCT',
          resourceId: id,
          details: {
            productName: product.name,
            sku: product.sku || 'N/A',
            category: product.category?.name || 'Không có danh mục',
            price: product.price,
            status: product.status,
            deletedDependencies: softDependencies,
            dependencyCounts: {
              reviews: product._count.reviews,
              cartItems: product._count.cartItems,
              wishlistItems: product._count.wishlistItems,
            }
          },
          ipAddress: request.headers.get('x-forwarded-for') ||
                    request.headers.get('x-real-ip') ||
                    'unknown',
          userAgent: request.headers.get('user-agent') || '',
        },
      });
    });

    let message = 'Sản phẩm đã được xóa thành công';
    if (softDependencies.length > 0) {
      message += ` (bao gồm ${softDependencies.join(', ')} liên quan)`;
    }

    console.log('✅ Product deletion completed successfully');
    console.log('📤 Sending success response:', { message, deletedDependencies: softDependencies });

    return NextResponse.json({
      success: true,
      message,
      deletedDependencies: softDependencies
    });
  } catch (error: unknown) {
    console.error('Product delete error:', error);

    // Handle specific Prisma errors
    if (error && typeof error === 'object' && 'code' in error) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { success: false, message: 'Không thể xóa sản phẩm do có ràng buộc dữ liệu. Vui lòng liên hệ quản trị viên.' },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { success: false, message: 'Sản phẩm không tồn tại hoặc đã bị xóa' },
            { status: 404 }
          );
        case 'P2002':
          return NextResponse.json(
            { success: false, message: 'Xung đột dữ liệu khi xóa sản phẩm' },
            { status: 409 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code);
      }
    }

    // Handle network/timeout errors
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = error.message as string;
      if (errorMessage.includes('timeout') || errorMessage.includes('TIMEOUT')) {
        return NextResponse.json(
          { success: false, message: 'Thao tác xóa sản phẩm bị timeout. Vui lòng thử lại.' },
          { status: 408 }
        );
      }
    }

    return NextResponse.json(
      { success: false, message: 'Lỗi server nội bộ khi xóa sản phẩm. Vui lòng thử lại sau.' },
      { status: 500 }
    );
  }
}
