import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { prisma } from '../../../../src/lib/prisma';

const JWT_SECRET = process.env.ADMIN_JWT_SECRET || 'fallback-secret';

// Middleware to verify admin authentication
async function verifyAdmin(request: NextRequest) {
  const token = request.cookies.get('admin-token')?.value;

  if (!token) {
    return null;
  }

  try {
    const payload = jwt.verify(token, JWT_SECRET) as { adminId: string; email: string; role: string };

    const admin = await prisma.adminUser.findUnique({
      where: { id: payload.adminId },
      select: { id: true, email: true, name: true, role: true, isActive: true },
    });

    if (!admin || !admin.isActive) {
      return null;
    }

    return admin;
  } catch (error) {
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const skip = (page - 1) * limit;

    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { slug: { contains: search } },
      ];
    }

    if (category) {
      where.categoryId = category;
    }

    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    const products = await prisma.product.findMany({
      where,
      orderBy,
      skip,
      take: limit,
    });

    const total = await prisma.product.count({ where });

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: {
        data: products,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      },
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { success: false, message: 'Lỗi khi tải sản phẩm' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      name,
      slug,
      shortDescription,
      description,
      images,
      categoryId, // For backward compatibility
      categoryIds, // New multiple categories
      colors,
      sizes,
      brand,
      sku,
      price,
      originalPrice,
      stock,
      variants,
      tags,
      featured,
      status,
      seoTitle,
      seoDescription,
      seoKeywords,
    } = body;

    // Determine which categories to use (new multiple or legacy single)
    const categoriesToUse = categoryIds && Array.isArray(categoryIds) && categoryIds.length > 0
      ? categoryIds
      : (categoryId ? [categoryId] : []);

    if (!name || !slug || !sku || !price || categoriesToUse.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Tên, slug, SKU, giá và ít nhất một danh mục là bắt buộc' },
        { status: 400 }
      );
    }

    // Validate price
    if (price <= 0) {
      return NextResponse.json(
        { success: false, message: 'Giá phải lớn hơn 0' },
        { status: 400 }
      );
    }

    // Validate original price if provided
    if (originalPrice && originalPrice <= price) {
      return NextResponse.json(
        { success: false, message: 'Giá gốc phải lớn hơn giá bán' },
        { status: 400 }
      );
    }

    // Validate stock
    if (stock < 0) {
      return NextResponse.json(
        { success: false, message: 'Số lượng tồn kho không được âm' },
        { status: 400 }
      );
    }

    // Validate sizes if provided
    if (sizes && Array.isArray(sizes)) {
      for (let i = 0; i < sizes.length; i++) {
        const size = sizes[i];
        const sizeName = String(size.name || '').trim();

        if (!sizeName) {
          return NextResponse.json(
            { success: false, message: `Tên kích thước không được để trống (kích thước ${i + 1})` },
            { status: 400 }
          );
        }

        // Validate size price if provided
        if (size.price && (isNaN(parseFloat(size.price)) || parseFloat(size.price) < 0)) {
          return NextResponse.json(
            { success: false, message: `Giá kích thước "${sizeName}" không hợp lệ` },
            { status: 400 }
          );
        }

        // Validate size stock if provided
        if (size.stock && (isNaN(parseInt(size.stock)) || parseInt(size.stock) < 0)) {
          return NextResponse.json(
            { success: false, message: `Số lượng tồn kho kích thước "${sizeName}" không hợp lệ` },
            { status: 400 }
          );
        }
      }

      // Check for duplicate size names
      const sizeNames = sizes.map((size: any) => String(size.name || '').trim().toLowerCase());
      const uniqueSizeNames = new Set(sizeNames);
      if (sizeNames.length !== uniqueSizeNames.size) {
        return NextResponse.json(
          { success: false, message: 'Không được có kích thước trùng lặp' },
          { status: 400 }
        );
      }
    }

    // Check if slug already exists
    const existingSlug = await prisma.product.findUnique({
      where: { slug },
    });

    if (existingSlug) {
      return NextResponse.json(
        { success: false, message: 'Slug đã tồn tại' },
        { status: 400 }
      );
    }

    // Check if SKU already exists
    const existingSku = await prisma.product.findUnique({
      where: { sku },
    });

    if (existingSku) {
      return NextResponse.json(
        { success: false, message: 'SKU đã tồn tại' },
        { status: 400 }
      );
    }

    // Validate all categories exist
    const categories = await prisma.category.findMany({
      where: { id: { in: categoriesToUse } },
    });

    if (categories.length !== categoriesToUse.length) {
      return NextResponse.json(
        { success: false, message: 'Một hoặc nhiều danh mục không tồn tại' },
        { status: 400 }
      );
    }

    // Use transaction to create product and categories
    const product = await prisma.$transaction(async (tx) => {
      // Create product
      const newProduct = await tx.product.create({
        data: {
          name,
          slug,
          shortDescription: shortDescription || '',
          description: description || '',
          images: images || [],
          colors: Array.isArray(colors) ? colors : [],
          sizes: [], // Will be handled separately for variants
          brand: brand || '',
          sku,
          price: parseFloat(price.toString()),
          originalPrice: originalPrice ? parseFloat(originalPrice.toString()) : null,
          stock: parseInt(stock.toString()) || 0,
          tags: Array.isArray(tags) ? tags : (tags ? tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : []),
          featured: Boolean(featured),
          status: status || 'ACTIVE',
          seoTitle: seoTitle || name,
          seoDescription: seoDescription || shortDescription || '',
          seoKeywords: Array.isArray(seoKeywords) ? seoKeywords : (seoKeywords ? seoKeywords.split(',').map((keyword: string) => keyword.trim()).filter(Boolean) : []),
        },
      });

      // Create product-category relationships
      await tx.productCategory.createMany({
        data: categoriesToUse.map(categoryId => ({
          productId: newProduct.id,
          categoryId
        }))
      });

      // Return product with relationships
      return await tx.product.findUnique({
        where: { id: newProduct.id },
        include: {
          productCategories: {
            include: {
              category: {
                select: { id: true, name: true, slug: true }
              }
            }
          },
          _count: {
            select: {
              reviews: true,
              wishlistItems: true,
              cartItems: true,
            },
          },
        },
      });
    });

    // Create variants from sizes if provided
    if (sizes && Array.isArray(sizes) && sizes.length > 0) {
      console.log('📦 Creating variants from sizes:', sizes);

      const variantData = sizes.map((size: any, index: number) => {
        // Ensure size.name is a string
        const sizeName = String(size.name || '').trim();
        if (!sizeName) {
          throw new Error(`Size name is required for variant ${index + 1}`);
        }

        // Generate unique SKU for variant
        const variantSku = `${product.sku}-${sizeName.toUpperCase().replace(/[^A-Z0-9]/g, '')}-${Date.now()}-${index}`;

        const variant = {
          productId: product.id,
          name: sizeName, // Ensure this is a string
          sku: variantSku,
          attributes: { size: sizeName }, // Store size as string in attributes
          price: size.price ? parseFloat(size.price.toString()) : product.price,
          stock: size.stock ? parseInt(size.stock.toString()) : 0,
          isActive: true,
        };

        console.log('🔧 Created variant data:', variant);
        return variant;
      });

      console.log('📤 Sending variant data to database:', variantData);
      await prisma.productVariant.createMany({
        data: variantData,
      });

      console.log('✅ Created', variantData.length, 'variants successfully');
    } else {
      console.log('ℹ️ No sizes provided or sizes is not an array');
    }

    // Log admin activity
    await prisma.adminLog.create({
      data: {
        adminId: admin.id,
        action: 'CREATE',
        resource: 'PRODUCT',
        resourceId: product.id,
        details: { productName: product.name, sku: product.sku },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || '',
      },
    });

    // Format response with enhanced data
    const formattedProduct = {
      ...product,
      categories: product?.productCategories?.map(pc => pc.category) || [],
      category: product?.productCategories?.[0]?.category || null, // For backward compatibility
    };

    return NextResponse.json({
      success: true,
      data: formattedProduct,
      message: 'Sản phẩm đã được tạo thành công',
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating product:', error);
    return NextResponse.json(
      { success: false, message: 'Lỗi khi tạo sản phẩm' },
      { status: 500 }
    );
  }
}
