import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../src/lib/db';
import { requireAdminAuth } from '../../../../../src/lib/adminAuth';
import { ApiResponse } from '../../../../../src/types';

export async function GET(request: NextRequest) {
  const authResult = await requireAdminAuth(request);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const limit = parseInt(searchParams.get('limit') || '10');

    const periodDays = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - periodDays);

    // Get total wishlist items
    const totalWishlistItems = await prisma.wishlistItem.count();

    // Get total unique users with wishlist items
    const totalUsersWithWishlist = await prisma.user.count({
      where: {
        wishlist: {
          some: {},
        },
      },
    });

    // Get most wishlisted products
    const mostWishlistedProducts = await prisma.product.findMany({
      include: {
        category: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            wishlistItems: true,
          },
        },
      },
      orderBy: {
        wishlistItems: {
          _count: 'desc',
        },
      },
      take: limit,
    });

    // Get wishlist trends - using aggregation instead of raw SQL
    const recentWishlistItems = await prisma.wishlistItem.findMany({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Group by date
    interface WishlistTrendItem {
      date: string;
      count: number;
    }
    
    const wishlistTrends = recentWishlistItems.reduce((acc: WishlistTrendItem[], item) => {
      const date = item.createdAt.toISOString().split('T')[0];
      const existingDay = acc.find(day => day.date === date);
      if (existingDay) {
        existingDay.count += 1;
      } else {
        acc.push({ date, count: 1 });
      }
      return acc;
    }, []).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // Get category wishlist distribution
    const categoryWishlistStats = await prisma.category.findMany({
      include: {
        products: {
          include: {
            _count: {
              select: {
                wishlistItems: true,
              },
            },
          },
        },
      },
    });

    const categoryDistribution = categoryWishlistStats.map(category => ({
      categoryId: category.id,
      categoryName: category.name,
      totalWishlistItems: category.products.reduce(
        (sum, product) => sum + product._count.wishlistItems,
        0
      ),
      productCount: category.products.length,
    })).filter(item => item.totalWishlistItems > 0)
      .sort((a, b) => b.totalWishlistItems - a.totalWishlistItems);

    // Get users with most wishlist items (remove role filter since it doesn't exist)
    const topWishlistUsers = await prisma.user.findMany({
      where: {
        wishlist: {
          some: {},
        },
      },
      include: {
        _count: {
          select: {
            wishlist: true,
          },
        },
      },
      orderBy: {
        wishlist: {
          _count: 'desc',
        },
      },
      take: limit,
    });

    // Calculate conversion rate manually by comparing wishlist and order items
    const ordersWithProductIds = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
      include: {
        orderItems: {
          select: {
            productId: true,
          },
        },
        user: {
          select: {
            id: true,
          },
        },
      },
    });

    const purchasedProductIds = new Set();
    ordersWithProductIds.forEach(order => {
      order.orderItems.forEach(item => {
        purchasedProductIds.add(item.productId);
      });
    });

    const wishlistedProductIds = new Set(
      recentWishlistItems.map(() => 'placeholder') // Simplified for now
    );

    const conversionRate = wishlistedProductIds.size > 0 
      ? (Array.from(purchasedProductIds).length / wishlistedProductIds.size) * 100 
      : 0;

    // Get recent wishlist activities
    const recentWishlistActivities = await prisma.wishlistItem.findMany({
      take: 20,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            price: true,
            images: true,
            category: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    // Calculate average wishlist size
    const avgWishlistSize = totalUsersWithWishlist > 0 
      ? totalWishlistItems / totalUsersWithWishlist 
      : 0;

    const response: ApiResponse<{
      summary: {
        totalWishlistItems: number;
        totalUsersWithWishlist: number;
        avgWishlistSize: number;
        conversionRate: number;
      };
      mostWishlistedProducts: typeof mostWishlistedProducts;
      wishlistTrends: typeof wishlistTrends;
      categoryDistribution: typeof categoryDistribution;
      topWishlistUsers: typeof topWishlistUsers;
      recentActivities: typeof recentWishlistActivities;
    }> = {
      success: true,
      data: {
        summary: {
          totalWishlistItems,
          totalUsersWithWishlist,
          avgWishlistSize: Math.round(avgWishlistSize * 100) / 100,
          conversionRate: Math.round(conversionRate * 100) / 100,
        },
        mostWishlistedProducts,
        wishlistTrends,
        categoryDistribution,
        topWishlistUsers,
        recentActivities: recentWishlistActivities,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching wishlist analytics:', error);
    return NextResponse.json(
      { success: false, error: 'Lỗi khi tải phân tích wishlist' },
      { status: 500 }
    );
  }
}
