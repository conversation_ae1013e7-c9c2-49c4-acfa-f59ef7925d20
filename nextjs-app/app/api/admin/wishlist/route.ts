import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../src/lib/db';
import { requireAdminAuth, getClientIP, getUserAgent } from '../../../../src/lib/adminAuth';
import { logAdminAction } from '../../../../src/lib/auth';
import { ApiResponse } from '../../../../src/types';

export async function GET(request: NextRequest) {
  const authResult = await requireAdminAuth(request);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const userId = searchParams.get('userId');

    const skip = (page - 1) * limit;

    // Build where condition
    interface WhereCondition {
      OR?: Array<{
        user?: {
          name?: { contains: string; mode: 'insensitive' } | { contains: string; mode: 'insensitive' };
          email?: { contains: string; mode: 'insensitive' };
        };
        product?: {
          name?: { contains: string; mode: 'insensitive' };
        };
      }>;
      userId?: string;
    }

    const whereCondition: WhereCondition = {};

    if (search) {
      whereCondition.OR = [
        {
          user: {
            name: {
              contains: search,
              mode: 'insensitive'
            }
          }
        },
        {
          user: {
            email: {
              contains: search,
              mode: 'insensitive'
            }
          }
        },
        {
          product: {
            name: {
              contains: search,
              mode: 'insensitive'
            }
          }
        }
      ];
    }

    if (userId) {
      whereCondition.userId = userId;
    }

    // Get total count
    const total = await prisma.wishlistItem.count({
      where: whereCondition
    });

    // Get wishlist items with pagination
    const wishlistItems = await prisma.wishlistItem.findMany({
      where: whereCondition,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            createdAt: true
          }
        },
        product: {
          include: {
            category: {
              select: {
                id: true,
                name: true
              }
            },
            _count: {
              select: {
                reviews: true,
                wishlistItems: true,
                orderItems: true
              }
            }
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take: limit
    });

    const totalPages = Math.ceil(total / limit);

    // Log admin action
    await logAdminAction(
      authResult.user.id,
      'VIEW',
      'WISHLIST',
      undefined,
      {
        page,
        limit,
        search,
        total
      },
      getClientIP(request),
      getUserAgent(request)
    );

    const response: ApiResponse<{
      wishlistItems: typeof wishlistItems;
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    }> = {
      success: true,
      data: {
        wishlistItems,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching wishlist items:', error);
    return NextResponse.json(
      { success: false, error: 'Lỗi khi tải danh sách wishlist' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const authResult = await requireAdminAuth(request);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const { searchParams } = new URL(request.url);
    const wishlistItemId = searchParams.get('id');
    const userId = searchParams.get('userId');
    const productId = searchParams.get('productId');

    if (!wishlistItemId && (!userId || !productId)) {
      return NextResponse.json(
        { success: false, error: 'Wishlist item ID hoặc userId và productId là bắt buộc' },
        { status: 400 }
      );
    }

    let deletedItem;
    
    if (wishlistItemId) {
      // Delete by wishlist item ID
      deletedItem = await prisma.wishlistItem.delete({
        where: { id: wishlistItemId },
        include: {
          user: { select: { name: true, email: true } },
          product: { select: { name: true } }
        }
      });
    } else {
      // Delete by userId and productId
      deletedItem = await prisma.wishlistItem.delete({
        where: {
          userId_productId: {
            userId: userId!,
            productId: productId!
          }
        },
        include: {
          user: { select: { name: true, email: true } },
          product: { select: { name: true } }
        }
      });
    }

    // Log admin action
    await logAdminAction(
      authResult.user.id,
      'DELETE',
      'WISHLIST_ITEM',
      deletedItem.id,
      {
        user: deletedItem.user,
        product: deletedItem.product
      },
      getClientIP(request),
      getUserAgent(request)
    );

    const response: ApiResponse<null> = {
      success: true,
      message: 'Wishlist item đã được xóa thành công'
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error deleting wishlist item:', error);
    return NextResponse.json(
      { success: false, error: 'Lỗi khi xóa wishlist item' },
      { status: 500 }
    );
  }
} 