import { NextRequest, NextResponse } from 'next/server';
import prisma from '../../../../../src/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const search = searchParams.get('search') || '';
    const featured = searchParams.get('featured');
    const inStock = searchParams.get('inStock');
    
    const skip = (page - 1) * limit;

    // First, get the category
    const category = await prisma.category.findUnique({
      where: { 
        slug,
        isActive: true 
      },
      include: {
        children: {
          where: { isActive: true },
          select: { id: true }
        }
      }
    });

    if (!category) {
      return NextResponse.json(
        { success: false, message: 'Category not found' },
        { status: 404 }
      );
    }

    // Get all category IDs (including children)
    const categoryIds = [category.id, ...category.children.map(child => child.id)];

    // Build where clause for many-to-many relationship
    const where: any = {
      productCategories: {
        some: {
          categoryId: { in: categoryIds }
        }
      },
      status: 'ACTIVE'
    };

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { shortDescription: { contains: search } },
        { tags: { hasSome: [search] } }
      ];
    }

    if (minPrice || maxPrice) {
      where.price = {};
      if (minPrice) where.price.gte = parseFloat(minPrice);
      if (maxPrice) where.price.lte = parseFloat(maxPrice);
    }

    if (featured === 'true') {
      where.featured = true;
    }

    if (inStock === 'true') {
      where.stock = { gt: 0 };
    }

    // Build orderBy
    const orderBy: any = {};
    if (sortBy === 'price') {
      orderBy.price = sortOrder;
    } else if (sortBy === 'name') {
      orderBy.name = sortOrder;
    } else if (sortBy === 'stock') {
      orderBy.stock = sortOrder;
    } else {
      orderBy.createdAt = sortOrder;
    }

    // Get products and total count
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          productCategories: {
            include: {
              category: {
                select: { id: true, name: true, slug: true }
              }
            }
          },
          _count: {
            select: {
              reviews: true,
              wishlistItems: true
            }
          }
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.product.count({ where })
    ]);

    const totalPages = Math.ceil(total / limit);

    // Format products with enhanced data
    const formattedProducts = products.map((product: any) => ({
      ...product,
      categories: product.productCategories?.map((pc: any) => pc.category) || [],
      category: product.productCategories?.[0]?.category || null, // For backward compatibility
    }));

    return NextResponse.json({
      success: true,
      data: {
        category: {
          id: category.id,
          name: category.name,
          slug: category.slug,
          description: category.description,
          image: category.image
        },
        products: formattedProducts,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Error fetching category products:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
