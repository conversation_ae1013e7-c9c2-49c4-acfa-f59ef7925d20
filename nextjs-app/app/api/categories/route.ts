import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../src/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeChildren = searchParams.get('includeChildren') === 'true';
    const parentId = searchParams.get('parentId');
    const limit = parseInt(searchParams.get('limit') || '50');

    // Build where clause for active categories
    const where: any = {
      isActive: true
    };

    if (parentId) {
      where.parentId = parentId;
    } else if (parentId === null || searchParams.get('rootOnly') === 'true') {
      where.parentId = null; // Root categories only
    }

    let categories;

    if (includeChildren) {
      // Get hierarchical structure with children
      categories = await prisma.category.findMany({
        where: {
          isActive: true,
          parentId: null // Start with root categories
        },
        orderBy: [
          { sortOrder: 'asc' },
          { name: 'asc' }
        ],
        include: {
          children: {
            where: { isActive: true },
            orderBy: [
              { sortOrder: 'asc' },
              { name: 'asc' }
            ],
            include: {
              children: {
                where: { isActive: true },
                orderBy: [
                  { sortOrder: 'asc' },
                  { name: 'asc' }
                ],
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  description: true,
                  image: true,
                  level: true,
                  sortOrder: true
                }
              },
              _count: {
                select: { productCategories: true }
              }
            }
          },
          _count: {
            select: { productCategories: true }
          }
        },
        take: limit
      });
    } else {
      // Get flat list
      categories = await prisma.category.findMany({
        where,
        orderBy: [
          { sortOrder: 'asc' },
          { name: 'asc' }
        ],
        select: {
          id: true,
          name: true,
          slug: true,
          description: true,
          image: true,
          parentId: true,
          level: true,
          sortOrder: true,
          _count: {
            select: { productCategories: true }
          }
        },
        take: limit
      });
    }

    // Transform data for frontend menu
    const transformedCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      image: category.image,
      level: category.level,
      sortOrder: category.sortOrder,
      productCount: category._count.productCategories,
      children: includeChildren && 'children' in category ? category.children.map(child => ({
        id: child.id,
        name: child.name,
        slug: child.slug,
        description: child.description,
        image: child.image,
        level: child.level,
        sortOrder: child.sortOrder,
        productCount: child._count.productCategories,
        children: child.children || []
      })) : undefined
    }));

    return NextResponse.json({
      success: true,
      data: transformedCategories
    });

  } catch (error) {
    console.error('Categories fetch error:', error);
    return NextResponse.json(
      { success: false, message: 'Lỗi server nội bộ' },
      { status: 500 }
    );
  }
}

// GET single category by slug
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { slug } = body;

    if (!slug) {
      return NextResponse.json(
        { success: false, message: 'Slug is required' },
        { status: 400 }
      );
    }

    const category = await prisma.category.findUnique({
      where: {
        slug,
        isActive: true
      },
      include: {
        parent: {
          select: { id: true, name: true, slug: true }
        },
        children: {
          where: { isActive: true },
          orderBy: [
            { sortOrder: 'asc' },
            { name: 'asc' }
          ],
          select: {
            id: true,
            name: true,
            slug: true,
            description: true,
            image: true,
            _count: {
              select: { productCategories: true }
            }
          }
        },
        _count: {
          select: { productCategories: true }
        }
      }
    });

    if (!category) {
      return NextResponse.json(
        { success: false, message: 'Category not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
        image: category.image,
        level: category.level,
        parent: category.parent,
        children: category.children.map(child => ({
          id: child.id,
          name: child.name,
          slug: child.slug,
          description: child.description,
          image: child.image,
          productCount: child._count.productCategories
        })),
        productCount: category._count.productCategories
      }
    });

  } catch (error) {
    console.error('Category fetch error:', error);
    return NextResponse.json(
      { success: false, message: 'Lỗi server nội bộ' },
      { status: 500 }
    );
  }
}
