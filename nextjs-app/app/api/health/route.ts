import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '../../../src/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Test database connection
    await prisma.$connect()
    
    // Get basic stats
    const stats = {
      database: 'connected',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'unknown',
      vercel: process.env.VERCEL_ENV || 'not-vercel'
    }

    // Try to get some basic counts (if tables exist)
    try {
      const adminCount = await prisma.adminUser.count()
      stats.adminUsers = adminCount
    } catch (error) {
      stats.adminUsers = 'table not accessible'
    }

    try {
      const categoryCount = await prisma.category.count()
      stats.categories = categoryCount
    } catch (error) {
      stats.categories = 'table not accessible'
    }

    try {
      const productCount = await prisma.product.count()
      stats.products = productCount
    } catch (error) {
      stats.products = 'table not accessible'
    }

    return NextResponse.json({
      success: true,
      message: 'Application is healthy',
      data: stats
    })
    
  } catch (error) {
    console.error('Health check failed:', error)
    
    return NextResponse.json({
      success: false,
      message: 'Application health check failed',
      error: error.message,
      data: {
        database: 'disconnected',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'unknown',
        vercel: process.env.VERCEL_ENV || 'not-vercel'
      }
    }, { status: 500 })
    
  } finally {
    await prisma.$disconnect()
  }
}
