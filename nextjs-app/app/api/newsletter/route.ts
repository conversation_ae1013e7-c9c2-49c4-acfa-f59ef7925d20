import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '../../../src/lib/prisma'
import { ApiResponse } from '../../../src/types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = body

    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Check if email already exists
    const existingSubscription = await prisma.newsletter.findUnique({
      where: { email }
    })

    if (existingSubscription) {
      return NextResponse.json(
        { success: false, error: 'Email already subscribed' },
        { status: 409 }
      )
    }

    // Create new subscription
    const subscription = await prisma.newsletter.create({
      data: { email }
    })

    const response: ApiResponse<typeof subscription> = {
      success: true,
      data: subscription,
      message: 'Successfully subscribed to newsletter'
    }

    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Error subscribing to newsletter:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to subscribe to newsletter' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const skip = (page - 1) * limit

    const [subscriptions, total] = await Promise.all([
      prisma.newsletter.findMany({
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.newsletter.count()
    ])

    const totalPages = Math.ceil(total / limit)

    const response: ApiResponse<{
      data: typeof subscriptions
      pagination: {
        page: number
        limit: number
        total: number
        totalPages: number
      }
    }> = {
      success: true,
      data: {
        data: subscriptions,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching newsletter subscriptions:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch subscriptions' },
      { status: 500 }
    )
  }
}
