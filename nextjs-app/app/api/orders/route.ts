import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '../../../src/lib/prisma'
import { ApiResponse } from '../../../src/types'

interface OrderItemData {
  productId: string
  variantId?: string | null
  productName: string
  quantity: number
  unitPrice: number
  totalPrice: number
  productSnapshot: {
    name: string
    price: number
    images: string | string[]
    size?: string
    color?: string
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      userId = 'guest',
      customerInfo,
      cartItems,
      paymentMethod = 'COD',
      notes = ''
    } = body

    if (!customerInfo || !cartItems || cartItems.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Thông tin đơn hàng không đầy đủ' },
        { status: 400 }
      )
    }

    // Validate customer info - Updated for 2-level addressing
    const { name, email, phone, address, ward, province } = customerInfo
    if (!name || !email || !phone || !address || !ward || !province) {
      return NextResponse.json(
        { success: false, error: '<PERSON>ui lòng điền đầy đủ thông tin liên hệ và địa chỉ' },
        { status: 400 }
      )
    }

    // Validate cart items and calculate total
    let subtotal = 0
    const orderItemsData: OrderItemData[] = []

    for (const item of cartItems) {
      // Fetch product to get current price and stock
      const product = await prisma.product.findUnique({
        where: { id: item.productId },
        select: { id: true, name: true, price: true, stock: true, images: true }
      })

      if (!product) {
        return NextResponse.json(
          { success: false, error: `Sản phẩm không tồn tại: ${item.productId}` },
          { status: 400 }
        )
      }

      // Check variant if provided
      let variant = null;
      let effectivePrice = product.price;
      let effectiveStock = product.stock;
      let sizeInfo = null;

      if (item.variantId) {
        // First check if variantId is a valid ObjectId (24 character hex string)
        const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(item.variantId);

        if (isValidObjectId) {
          // Try to find actual variant record
          variant = await prisma.productVariant.findUnique({
            where: { id: item.variantId },
            select: { id: true, name: true, price: true, stock: true, attributes: true }
          });

          if (variant) {
            // Use variant price if available, otherwise use product price
            effectivePrice = variant.price || product.price;
            effectiveStock = variant.stock;
            sizeInfo = variant.attributes?.size || variant.name;
          } else {
            return NextResponse.json(
              { success: false, error: `Size không tồn tại cho sản phẩm "${product.name}"` },
              { status: 400 }
            )
          }
        } else {
          // variantId is actually a size name (like "XS", "S", "M", "L")
          // This happens when product uses sizes array instead of variants

          // Get product with sizes to validate
          const productWithSizes = await prisma.product.findUnique({
            where: { id: item.productId },
            select: { sizes: true }
          });

          if (productWithSizes?.sizes && Array.isArray(productWithSizes.sizes)) {
            if (!productWithSizes.sizes.includes(item.variantId)) {
              return NextResponse.json(
                { success: false, error: `Size "${item.variantId}" không có sẵn cho sản phẩm "${product.name}"` },
                { status: 400 }
              )
            }
            // Use the size name directly
            sizeInfo = item.variantId;
            // Keep product price and stock since no variant exists
          } else {
            return NextResponse.json(
              { success: false, error: `Sản phẩm "${product.name}" không có thông tin size` },
              { status: 400 }
            )
          }
        }
      }

      if (effectiveStock < item.quantity) {
        const stockMessage = item.variantId
          ? `Sản phẩm "${product.name}" size ${sizeInfo} không đủ số lượng trong kho`
          : `Sản phẩm "${product.name}" không đủ số lượng trong kho`;

        return NextResponse.json(
          { success: false, error: stockMessage },
          { status: 400 }
        )
      }

      const itemTotal = effectivePrice * item.quantity
      subtotal += itemTotal

      // Only include variantId if it's a valid ObjectId (actual variant)
      const orderItemData: any = {
        productId: product.id,
        productName: product.name,
        quantity: item.quantity,
        unitPrice: effectivePrice,
        totalPrice: itemTotal,
        productSnapshot: {
          name: product.name,
          price: effectivePrice,
          images: product.images,
          size: sizeInfo,
          color: item.selectedColor || null
        }
      };

      // Only add variantId if it's a valid ObjectId
      if (item.variantId && /^[0-9a-fA-F]{24}$/.test(item.variantId)) {
        orderItemData.variantId = item.variantId;
      }

      orderItemsData.push(orderItemData);
    }

    // Calculate shipping and total
    const shippingFee = 0 // Phí ship sẽ báo giá sau
    const tax = 0
    const discount = 0
    const total = subtotal + shippingFee + tax - discount

    // Generate order number
    const orderNumber = `DH${Date.now()}${Math.random().toString(36).substring(2, 5).toUpperCase()}`

    // Create order in transaction
    const order = await prisma.$transaction(async (tx) => {
      // Create or get user
      let user
      if (userId === 'guest') {
        // For guest checkout, create a guest user record or use existing
        user = await tx.user.upsert({
          where: { email },
          create: {
            email,
            name,
            phone,
            password: '', // Guest users don't have passwords
          },
          update: {
            name,
            phone
          }
        })
      } else {
        user = await tx.user.findUnique({ where: { id: userId } })
        if (!user) {
          throw new Error('Người dùng không tồn tại')
        }
      }

      // Create order
      const newOrder = await tx.order.create({
        data: {
          orderNumber,
          userId: user.id,
          status: 'PENDING',
          paymentStatus: 'PENDING',
          paymentMethod,
          subtotal,
          shippingFee,
          tax,
          discount,
          total,
          shippingAddress: JSON.stringify({
            fullName: name,
            email,
            phone,
            address,
            ward,
            province
          }),
          notes,
          orderItems: {
            createMany: {
              data: orderItemsData
            }
          }
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true
            }
          },
          orderItems: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  images: true,
                  price: true
                }
              }
            }
          }
        }
      })

      // Update product and variant stock
      for (const item of cartItems) {
        // Update product stock
        await tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity
            }
          }
        })

        // Update variant stock if variant is specified and is a valid ObjectId
        if (item.variantId && /^[0-9a-fA-F]{24}$/.test(item.variantId)) {
          await tx.productVariant.update({
            where: { id: item.variantId },
            data: {
              stock: {
                decrement: item.quantity
              }
            }
          })
        }
        // If variantId is a size name (not ObjectId), we only update product stock
      }

      return newOrder
    })

    const response: ApiResponse<typeof order> = {
      success: true,
      data: order,
      message: 'Đơn hàng đã được tạo thành công'
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error creating order:', error)
    return NextResponse.json(
      { success: false, error: 'Có lỗi xảy ra khi tạo đơn hàng' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    if (!userId || userId === 'guest') {
      return NextResponse.json({
        success: true,
        data: {
          orders: [],
          pagination: {
            page,
            limit,
            total: 0,
            totalPages: 0
          }
        }
      })
    }

    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where: { userId },
        include: {
          orderItems: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  images: true,
                  price: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.order.count({ where: { userId } })
    ])

    const totalPages = Math.ceil(total / limit)

    const response: ApiResponse<{
      orders: typeof orders
      pagination: {
        page: number
        limit: number
        total: number
        totalPages: number
      }
    }> = {
      success: true,
      data: {
        orders,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching orders:', error)
    return NextResponse.json(
      { success: false, error: 'Có lỗi xảy ra khi tải đơn hàng' },
      { status: 500 }
    )
  }
} 