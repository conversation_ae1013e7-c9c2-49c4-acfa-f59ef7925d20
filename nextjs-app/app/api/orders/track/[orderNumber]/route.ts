/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Rate limiting store (in production, use Redis or database)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Rate limiting function
function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  const maxRequests = 10; // Max 10 requests per 15 minutes per IP

  const record = rateLimitStore.get(ip);
  
  if (!record || now > record.resetTime) {
    rateLimitStore.set(ip, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (record.count >= maxRequests) {
    return false;
  }
  
  record.count++;
  return true;
}

// Validate order number format
function validateOrderNumber(orderNumber: string): boolean {
  // Order number format: DH + timestamp + random characters
  const orderNumberRegex = /^DH\d{13}[A-Z0-9]{2,4}$/;
  return orderNumberRegex.test(orderNumber);
}

// Get order status in Vietnamese
function getOrderStatusText(status: string): string {
  const statusMap: { [key: string]: string } = {
    'PENDING': 'Chờ xác nhận',
    'CONFIRMED': 'Đã xác nhận',
    'PROCESSING': 'Đang xử lý',
    'SHIPPED': 'Đang giao hàng',
    'DELIVERED': 'Đã giao hàng',
    'CANCELLED': 'Đã hủy'
  };
  return statusMap[status] || status;
}

// Get payment status in Vietnamese
function getPaymentStatusText(status: string): string {
  const statusMap: { [key: string]: string } = {
    'PENDING': 'Chờ thanh toán',
    'PAID': 'Đã thanh toán',
    'FAILED': 'Thanh toán thất bại',
    'REFUNDED': 'Đã hoàn tiền'
  };
  return statusMap[status] || status;
}

// Get payment method in Vietnamese
function getPaymentMethodText(method: string): string {
  const methodMap: { [key: string]: string } = {
    'COD': 'Thanh toán khi nhận hàng',
    'BANK_TRANSFER': 'Chuyển khoản ngân hàng',
    'CREDIT_CARD': 'Thẻ tín dụng',
    'E_WALLET': 'Ví điện tử'
  };
  return methodMap[method] || method;
}

// Generate order timeline
function generateOrderTimeline(order: any): Array<{
  status: string;
  statusText: string;
  timestamp: string;
  description: string;
  completed: boolean;
}> {
  const timeline = [
    {
      status: 'PENDING',
      statusText: 'Đơn hàng được tạo',
      timestamp: order.createdAt,
      description: 'Đơn hàng của bạn đã được tạo và đang chờ xác nhận',
      completed: true
    }
  ];

  // Add status progression based on current status
  const statusProgression = ['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED'];
  const currentStatusIndex = statusProgression.indexOf(order.status);

  if (currentStatusIndex >= 1) {
    timeline.push({
      status: 'CONFIRMED',
      statusText: 'Đã xác nhận',
      timestamp: order.updatedAt,
      description: 'Đơn hàng đã được xác nhận và đang chuẩn bị',
      completed: true
    });
  }

  if (currentStatusIndex >= 2) {
    timeline.push({
      status: 'PROCESSING',
      statusText: 'Đang xử lý',
      timestamp: order.updatedAt,
      description: 'Đơn hàng đang được đóng gói và chuẩn bị giao',
      completed: true
    });
  }

  if (currentStatusIndex >= 3) {
    timeline.push({
      status: 'SHIPPED',
      statusText: 'Đang giao hàng',
      timestamp: order.updatedAt,
      description: 'Đơn hàng đã được giao cho đơn vị vận chuyển',
      completed: true
    });
  }

  if (currentStatusIndex >= 4) {
    timeline.push({
      status: 'DELIVERED',
      statusText: 'Đã giao hàng',
      timestamp: order.deliveredAt || order.updatedAt,
      description: 'Đơn hàng đã được giao thành công',
      completed: true
    });
  }

  // Handle cancelled status
  if (order.status === 'CANCELLED') {
    timeline.push({
      status: 'CANCELLED',
      statusText: 'Đã hủy',
      timestamp: order.updatedAt,
      description: order.cancelReason || 'Đơn hàng đã được hủy',
      completed: true
    });
  }

  return timeline;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderNumber: string }> }
) {
  try {
    const { orderNumber } = await params;

    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Check rate limit
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Quá nhiều yêu cầu tra cứu. Vui lòng thử lại sau 15 phút.' 
        },
        { status: 429 }
      );
    }

    // Validate order number format
    if (!validateOrderNumber(orderNumber)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Mã đơn hàng không hợp lệ. Vui lòng kiểm tra lại.' 
        },
        { status: 400 }
      );
    }

    // Find order by order number
    const order = await prisma.order.findUnique({
      where: {
        orderNumber: orderNumber
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            phone: true
          }
        },
        orderItems: {
          include: {
            product: {
              select: {
                name: true,
                slug: true,
                images: true,
                price: true
              }
            }
          }
        }
      }
    });

    if (!order) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Không tìm thấy đơn hàng với mã này. Vui lòng kiểm tra lại mã đơn hàng.' 
        },
        { status: 404 }
      );
    }

    // Parse shipping address
    let shippingAddress = {};
    try {
      shippingAddress = JSON.parse(order.shippingAddress || '{}');
    } catch (error) {
      console.error('Error parsing shipping address:', error);
    }

    // Generate timeline
    const timeline = generateOrderTimeline(order);

    // Prepare response data (exclude sensitive information)
    const responseData = {
      success: true,
      data: {
        orderNumber: order.orderNumber,
        status: order.status,
        statusText: getOrderStatusText(order.status),
        paymentStatus: order.paymentStatus,
        paymentStatusText: getPaymentStatusText(order.paymentStatus),
        paymentMethod: order.paymentMethod,
        paymentMethodText: getPaymentMethodText(order.paymentMethod),
        subtotal: order.subtotal,
        shippingFee: order.shippingFee,
        tax: order.tax,
        discount: order.discount,
        total: order.total,
        currency: order.currency || 'VND',
        createdAt: order.createdAt,
        updatedAt: order.updatedAt,
        deliveredAt: order.deliveredAt,
        estimatedDelivery: order.estimatedDelivery,
        trackingNumber: order.trackingNumber,
        notes: order.notes,
        cancelReason: order.cancelReason,
        customer: {
          name: order.user?.name,
          email: order.user?.email,
          phone: order.user?.phone
        },
        shippingAddress,
        items: order.orderItems.map(item => ({
          id: item.id,
          productName: item.productName,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          productSnapshot: item.productSnapshot,
          product: item.product ? {
            name: item.product.name,
            slug: item.product.slug,
            images: item.product.images,
            price: item.product.price
          } : null
        })),
        timeline
      }
    };

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Error tracking order:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Có lỗi xảy ra khi tra cứu đơn hàng. Vui lòng thử lại sau.' 
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
