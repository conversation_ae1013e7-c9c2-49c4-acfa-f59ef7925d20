import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '../../../../src/lib/prisma'
import { ApiResponse, ProductWithCategoryAndReviews } from '../../../../src/types'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params

    const product = await prisma.product.findUnique({
      where: { slug },
      include: {
        productCategories: {
          include: {
            category: true
          }
        },
        variants: {
          where: { isActive: true },
          select: {
            id: true,
            name: true,
            attributes: true,
            price: true,
            stock: true
          }
        },
        reviews: {
          include: {
            user: {
              select: {
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        },
        _count: {
          select: {
            reviews: true,
            wishlistItems: true
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    // Format response with enhanced data for backward compatibility
    const formattedProduct = {
      ...product,
      categories: product.productCategories?.map(pc => pc.category) || [],
      category: product.productCategories?.[0]?.category || null, // For backward compatibility
    };

    const response: ApiResponse<ProductWithCategoryAndReviews> = {
      success: true,
      data: formattedProduct as ProductWithCategoryAndReviews
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching product:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch product' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params
    const body = await request.json()
    const { name, description, price, originalPrice, categoryId, images, stock, featured, isOnSale } = body

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { slug }
    })

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    // Generate new slug if name changed
    let newSlug = slug
    if (name && name !== existingProduct.name) {
      newSlug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    }

    const updatedProduct = await prisma.product.update({
      where: { slug },
      data: {
        name: name || existingProduct.name,
        slug: newSlug,
        description: description !== undefined ? description : existingProduct.description,
        price: price !== undefined ? parseFloat(price) : existingProduct.price,
        originalPrice: originalPrice !== undefined ? (originalPrice ? parseFloat(originalPrice) : null) : existingProduct.originalPrice,
        categoryId: categoryId || existingProduct.categoryId,
        images: images !== undefined ? JSON.stringify(images) : existingProduct.images,
        stock: stock !== undefined ? parseInt(stock) : existingProduct.stock,
        featured: featured !== undefined ? featured : existingProduct.featured,
        isOnSale: isOnSale !== undefined ? isOnSale : existingProduct.isOnSale
      },
      include: {
        category: true,
        reviews: true,
        _count: {
          select: {
            reviews: true
          }
        }
      }
    })

    const response: ApiResponse<ProductWithCategoryAndReviews> = {
      success: true,
      data: updatedProduct as ProductWithCategoryAndReviews,
      message: 'Product updated successfully'
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error updating product:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update product' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { slug }
    })

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    await prisma.product.delete({
      where: { slug }
    })

    const response: ApiResponse<null> = {
      success: true,
      message: 'Product deleted successfully'
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error deleting product:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete product' },
      { status: 500 }
    )
  }
}
