import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../src/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Adding sizes and colors to products...');

    // Get all products
    const products = await prisma.products.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        sizes: true,
        colors: true
      }
    });

    console.log(`📦 Found ${products.length} products`);

    // Sample sizes and colors
    const sampleSizes = ['S', 'M', 'L', 'XL', 'XXL'];
    const sampleColors = ['đen', 'trắng', 'đỏ', 'xanh', 'vàng', 'hồng', 'xám'];

    const updatedProducts = [];

    for (const product of products) {
      // Skip if product already has sizes and colors
      if (product.sizes && product.sizes.length > 0 && product.colors && product.colors.length > 0) {
        console.log(`⏭️  Skipping ${product.name} - already has sizes and colors`);
        continue;
      }

      // Randomly select 3-5 sizes and 2-4 colors
      const randomSizes = sampleSizes
        .sort(() => 0.5 - Math.random())
        .slice(0, Math.floor(Math.random() * 3) + 3); // 3-5 sizes

      const randomColors = sampleColors
        .sort(() => 0.5 - Math.random())
        .slice(0, Math.floor(Math.random() * 3) + 2); // 2-4 colors

      const updatedProduct = await prisma.products.update({
        where: { id: product.id },
        data: {
          sizes: randomSizes,
          colors: randomColors
        }
      });

      updatedProducts.push({
        name: product.name,
        sizes: randomSizes,
        colors: randomColors
      });

      console.log(`✅ Updated ${product.name}:`);
      console.log(`   Sizes: ${randomSizes.join(', ')}`);
      console.log(`   Colors: ${randomColors.join(', ')}`);
    }

    console.log('🎉 Successfully added sizes and colors to all products!');

    return NextResponse.json({
      success: true,
      message: `Successfully updated ${updatedProducts.length} products with sizes and colors`,
      data: updatedProducts
    });

  } catch (error) {
    console.error('❌ Error adding sizes and colors:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to add sizes and colors to products',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
