import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '../../../../../src/lib/db'
import { ApiResponse, ProductWithCategoryAndReviews } from '../../../../../src/types'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Product ID is required' },
        { status: 400 }
      )
    }

    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        productCategories: {
          include: {
            category: {
              select: {
                id: true,
                name: true,
                slug: true
              }
            }
          }
        },
        variants: {
          select: {
            id: true,
            name: true,
            price: true,
            stock: true,
            attributes: true
          },
          orderBy: {
            createdAt: 'asc'
          }
        },
        reviews: {
          include: {
            user: {
              select: {
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        },
        _count: {
          select: {
            reviews: true,
            wishlistItems: true
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    // Format response with enhanced data for backward compatibility
    const formattedProduct = {
      ...product,
      categories: product.productCategories?.map(pc => pc.category) || [],
      category: product.productCategories?.[0]?.category || null, // For backward compatibility
    };

    const response: ApiResponse<ProductWithCategoryAndReviews> = {
      success: true,
      data: formattedProduct as unknown as ProductWithCategoryAndReviews
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching product by ID:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
} 