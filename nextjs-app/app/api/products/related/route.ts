import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('productId')
    const limit = parseInt(searchParams.get('limit') || '8')

    if (!productId) {
      return NextResponse.json(
        { success: false, message: 'Product ID is required' },
        { status: 400 }
      )
    }

    // Get the current product to find related products
    const currentProduct = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        category: true
      }
    })

    if (!currentProduct) {
      return NextResponse.json(
        { success: false, message: 'Product not found' },
        { status: 404 }
      )
    }

    // Find related products based on category and tags
    const relatedProducts = await prisma.product.findMany({
      where: {
        AND: [
          { id: { not: productId } }, // Exclude current product
          { status: 'ACTIVE' },
          {
            OR: [
              { categoryId: currentProduct.categoryId }, // Same category
              {
                tags: {
                  hasSome: currentProduct.tags // Products with similar tags
                }
              }
            ]
          }
        ]
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        _count: {
          select: {
            reviews: true,
            wishlistItems: true
          }
        }
      },
      orderBy: [
        { featured: 'desc' },
        { createdAt: 'desc' }
      ],
      take: limit
    })

    // Format products for public API
    const formattedProducts = relatedProducts.map(product => ({
      id: product.id,
      name: product.name,
      slug: product.slug,
      shortDescription: product.shortDescription,
      price: product.price,
      originalPrice: product.originalPrice,
      salePrice: product.originalPrice ? product.price : undefined,
      stock: product.stock,
      images: product.images,
      featured: product.featured,
      category: product.category,
      reviewCount: product._count.reviews,
      wishlistCount: product._count.wishlistItems,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    }))

    return NextResponse.json({
      success: true,
      data: formattedProducts
    })
  } catch (error) {
    console.error('Error fetching related products:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
} 