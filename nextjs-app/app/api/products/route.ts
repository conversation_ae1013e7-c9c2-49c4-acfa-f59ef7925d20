import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '../../../src/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    const categorySlug = searchParams.get('category')
    const categoryId = searchParams.get('categoryId')
    const search = searchParams.get('search')
    const featured = searchParams.get('featured') === 'true'
    const latest = searchParams.get('latest') === 'true'
    const sale = searchParams.get('sale') === 'true'
    const exclude = searchParams.get('exclude') // Product ID to exclude
    const minPrice = searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined
    const maxPrice = searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    const inStock = searchParams.get('inStock') === 'true'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      status: 'ACTIVE'
    }

    // Exclude specific product
    if (exclude) {
      where.id = { not: exclude }
    }

    // Category filter - support both slug and direct categoryId
    if (categorySlug) {
      const category = await prisma.category.findUnique({
        where: {
          slug: categorySlug,
          isActive: true
        },
        include: {
          children: {
            where: { isActive: true },
            select: { id: true }
          }
        }
      })

      if (category) {
        const categoryIds = [category.id, ...category.children.map(child => child.id)]
        where.productCategories = {
          some: {
            categoryId: { in: categoryIds }
          }
        }
      }
    } else if (categoryId) {
      where.productCategories = {
        some: {
          categoryId: categoryId
        }
      }
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { shortDescription: { contains: search } },
        { tags: { hasSome: [search] } }
      ]
    }

    if (featured) {
      where.featured = true
    }

    if (sale) {
      where.originalPrice = { not: null }
    }

    if (inStock) {
      where.stock = { gt: 0 }
    }

    if (minPrice !== undefined || maxPrice !== undefined) {
      where.price = {}
      if (minPrice !== undefined) where.price.gte = minPrice
      if (maxPrice !== undefined) where.price.lte = maxPrice
    }

    // Build orderBy
    const orderBy: any = {}
    if (latest) {
      orderBy.createdAt = 'desc'
    } else if (sortBy === 'price') {
      orderBy.price = sortOrder
    } else if (sortBy === 'name') {
      orderBy.name = sortOrder
    } else if (sortBy === 'featured') {
      orderBy.featured = sortOrder
    } else {
      orderBy.createdAt = sortOrder
    }

    // Get products with pagination
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          productCategories: {
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true
                }
              }
            }
          },
          _count: {
            select: {
              reviews: true,
              wishlistItems: true
            }
          }
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.product.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    // Format products for public API
    const formattedProducts = products.map((product: any) => ({
      id: product.id,
      name: product.name,
      slug: product.slug,
      shortDescription: product.shortDescription,
      price: product.price,
      originalPrice: product.originalPrice,
      salePrice: product.isOnSale ? product.price : undefined,
      stock: product.stock,
      images: product.images,
      featured: product.featured,
      categories: product.productCategories?.map((pc: any) => pc.category) || [],
      category: product.productCategories?.[0]?.category || null, // For backward compatibility
      reviewCount: product._count?.reviews || 0,
      wishlistCount: product._count?.wishlistItems || 0,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    }))

    return NextResponse.json({
      success: true,
      data: {
        products: formattedProducts,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })
  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get specific product collections for homepage
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type = 'featured', limit = 8 } = body

    let where: any = {
      status: 'ACTIVE'
    }

    let orderBy: any = {}

    switch (type) {
      case 'featured':
        where.featured = true
        orderBy.createdAt = 'desc'
        break
      case 'latest':
        orderBy.createdAt = 'desc'
        break
      case 'bestseller':
        // You could add a bestseller field or calculate based on orders
        orderBy.createdAt = 'desc'
        break
      case 'sale':
        where.originalPrice = { not: null }
        orderBy.createdAt = 'desc'
        break
      default:
        orderBy.createdAt = 'desc'
    }

    const products = await prisma.product.findMany({
      where,
      include: {
        productCategories: {
          include: {
            category: {
              select: {
                id: true,
                name: true,
                slug: true
              }
            }
          }
        },
        _count: {
          select: {
            reviews: true,
            wishlistItems: true
          }
        }
      },
      orderBy,
      take: limit,
    })

    // Format products for public API
    const formattedProducts = products.map((product: any) => ({
      id: product.id,
      name: product.name,
      slug: product.slug,
      shortDescription: product.shortDescription,
      price: product.price,
      originalPrice: product.originalPrice,
      salePrice: product.isOnSale ? product.price : undefined,
      stock: product.stock,
      images: product.images,
      featured: product.featured,
      categories: product.productCategories?.map((pc: any) => pc.category) || [],
      category: product.productCategories?.[0]?.category || null, // For backward compatibility
      reviewCount: product._count?.reviews || 0,
      wishlistCount: product._count?.wishlistItems || 0,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    }))

    return NextResponse.json({
      success: true,
      data: formattedProducts
    })
  } catch (error) {
    console.error('Error fetching featured products:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
