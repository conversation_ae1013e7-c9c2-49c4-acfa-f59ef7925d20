import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../src/lib/prisma';
import {
  getFeaturedReviews,
  calculateReviewStats,
  formatReviewForDisplay
} from '../../../../src/lib/reviewUtils';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '8');
    const minRating = parseInt(searchParams.get('minRating') || '4');

    // Get featured reviews with high ratings
    const reviews = await prisma.review.findMany({
      where: {
        rating: {
          gte: minRating
        },
        isApproved: true,
        comment: {
          not: null,
          not: ''
        }
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            images: true,
            category: {
              select: {
                name: true
              }
            }
          }
        },
        user: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: [
        { rating: 'desc' },
        { createdAt: 'desc' }
      ],
      take: limit * 2 // Get more to filter better ones
    });

    // Use utility function to get featured reviews
    const featuredReviews = getFeaturedReviews(reviews, limit);

    // If we don't have enough high-quality reviews, get some 3-star reviews too
    if (featuredReviews.length < limit) {
      const additionalReviews = await prisma.review.findMany({
        where: {
          rating: {
            gte: 3,
            lt: minRating
          },
          isApproved: true,
          comment: {
            not: null,
            not: ''
          },
          id: {
            notIn: featuredReviews.map(r => r.id)
          }
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
              images: true,
              category: {
                select: {
                  name: true
                }
              }
            }
          },
          user: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: [
          { rating: 'desc' },
          { createdAt: 'desc' }
        ],
        take: limit - featuredReviews.length
      });

      // Format additional reviews using utility function
      const formattedAdditionalReviews = getFeaturedReviews(additionalReviews, limit - featuredReviews.length);
      featuredReviews.push(...formattedAdditionalReviews);
    }

    // Get all reviews for statistics
    const allReviews = await prisma.review.findMany({
      where: {
        isApproved: true
      },
      select: {
        rating: true
      }
    });

    // Calculate statistics using utility function
    const stats = calculateReviewStats(allReviews);

    return NextResponse.json({
      success: true,
      data: {
        reviews: featuredReviews.slice(0, limit),
        stats
      }
    });

  } catch (error) {
    console.error('Error fetching featured reviews:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch featured reviews',
        data: {
          reviews: [],
          stats: {
            totalReviews: 0,
            averageRating: 0
          }
        }
      },
      { status: 500 }
    );
  }
}
