import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = parseInt(searchParams.get('limit') || '10')
    const type = searchParams.get('type') || 'all' // 'products', 'categories', 'all'

    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        success: true,
        data: {
          products: [],
          categories: [],
          total: 0
        }
      })
    }

    const searchTerm = query.trim()
    const results: any = {
      products: [],
      categories: [],
      total: 0
    }

    // Search products
    if (type === 'all' || type === 'products') {
      const products = await prisma.product.findMany({
        where: {
          OR: [
            {
              name: {
                contains: searchTerm,
                mode: 'insensitive'
              }
            },
            {
              description: {
                contains: searchTerm,
                mode: 'insensitive'
              }
            },
            {
              category: {
                name: {
                  contains: searchTerm,
                  mode: 'insensitive'
                }
              }
            }
          ]
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          variants: {
            select: {
              id: true,
              name: true,
              price: true,
              stock: true,
              attributes: true
            },
            orderBy: {
              createdAt: 'asc'
            }
          },
          _count: {
            select: {
              reviews: true
            }
          }
        },
        orderBy: [
          {
            name: 'asc'
          }
        ],
        take: type === 'products' ? limit : Math.min(limit, 8)
      })

      results.products = products.map(product => ({
        id: product.id,
        name: product.name,
        slug: product.slug,
        price: product.price,
        images: product.images,
        category: product.category,
        variants: product.variants,
        reviewCount: product._count.reviews,
        stock: product.stock
      }))
    }

    // Search categories
    if (type === 'all' || type === 'categories') {
      const categories = await prisma.category.findMany({
        where: {
          OR: [
            {
              name: {
                contains: searchTerm,
                mode: 'insensitive'
              }
            },
            {
              description: {
                contains: searchTerm,
                mode: 'insensitive'
              }
            }
          ]
        },
        include: {
          _count: {
            select: {
              products: true
            }
          }
        },
        orderBy: {
          name: 'asc'
        },
        take: type === 'categories' ? limit : Math.min(limit, 5)
      })

      results.categories = categories.map(category => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
        image: category.image,
        productCount: category._count.products
      }))
    }

    results.total = results.products.length + results.categories.length

    return NextResponse.json({
      success: true,
      data: results,
      query: searchTerm
    })

  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Lỗi khi tìm kiếm'
      },
      { status: 500 }
    )
  }
}
