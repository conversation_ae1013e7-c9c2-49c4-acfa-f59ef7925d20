import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '../../../../src/lib/db'
import { ApiResponse } from '../../../../src/types'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ productId: string }> }
) {
  try {
    const { productId } = await params
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId') || 'guest'

    if (!productId) {
      return NextResponse.json(
        { success: false, error: 'Product ID is required' },
        { status: 400 }
      )
    }

    // For guest users, return error (they need to login)
    if (userId === 'guest') {
      return NextResponse.json(
        { success: false, error: 'Please login to manage wishlist' },
        { status: 401 }
      )
    }

    // Check if item exists in wishlist
    const existingItem = await prisma.wishlistItem.findUnique({
      where: {
        userId_productId: {
          userId,
          productId
        }
      }
    })

    if (!existingItem) {
      return NextResponse.json(
        { success: false, error: 'Product not found in wishlist' },
        { status: 404 }
      )
    }

    // Remove item from wishlist
    await prisma.wishlistItem.delete({
      where: {
        userId_productId: {
          userId,
          productId
        }
      }
    })

    const response: ApiResponse<null> = {
      success: true,
      message: 'Product removed from wishlist successfully'
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error removing from wishlist:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to remove from wishlist' },
      { status: 500 }
    )
  }
} 