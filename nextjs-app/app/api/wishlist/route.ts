import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '../../../src/lib/db'
import { ApiResponse, WishlistItemWithProduct } from '../../../src/types'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId') || 'guest'

    // For guest users, return empty array
    if (userId === 'guest') {
      return NextResponse.json({
        success: true,
        data: []
      })
    }

    const wishlistItems = await prisma.wishlistItem.findMany({
      where: { userId },
      include: {
        product: {
          include: {
            category: true,
            reviews: {
              include: {
                user: {
                  select: {
                    name: true,
                    email: true
                  }
                }
              }
            },
            _count: {
              select: {
                reviews: true,
                wishlistItems: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    const response: ApiResponse<WishlistItemWithProduct[]> = {
      success: true,
      data: wishlistItems as WishlistItemWithProduct[]
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching wishlist:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch wishlist' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { productId, userId = 'guest' } = body

    if (!productId) {
      return NextResponse.json(
        { success: false, error: 'Product ID is required' },
        { status: 400 }
      )
    }

    // For guest users, return error (they need to login)
    if (userId === 'guest') {
      return NextResponse.json(
        { success: false, error: 'Please login to add items to wishlist' },
        { status: 401 }
      )
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId }
    })

    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    // Check if item already exists in wishlist
    const existingItem = await prisma.wishlistItem.findUnique({
      where: {
        userId_productId: {
          userId,
          productId
        }
      }
    })

    if (existingItem) {
      return NextResponse.json(
        { success: false, error: 'Product already in wishlist' },
        { status: 409 }
      )
    }

    // Add item to wishlist
    const wishlistItem = await prisma.wishlistItem.create({
      data: {
        userId,
        productId
      },
      include: {
        product: {
          include: {
            category: true,
            reviews: {
              include: {
                user: {
                  select: {
                    name: true,
                    email: true
                  }
                }
              }
            },
            _count: {
              select: {
                reviews: true,
                wishlistItems: true
              }
            }
          }
        }
      }
    })

    const response: ApiResponse<WishlistItemWithProduct> = {
      success: true,
      data: wishlistItem as WishlistItemWithProduct,
      message: 'Product added to wishlist successfully'
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error adding to wishlist:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to add to wishlist' },
      { status: 500 }
    )
  }
} 