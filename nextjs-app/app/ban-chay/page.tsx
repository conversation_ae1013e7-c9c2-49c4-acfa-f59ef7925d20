'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, ShoppingCart, Eye, Heart } from 'lucide-react';
import Link from 'next/link';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { useCart } from '../../src/contexts/CartContext';
import { useWishlist } from '../../src/contexts/WishlistContext';
import { useToast } from '../../src/components/ui/Toast';

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: {
    id: string;
    name: string;
    slug: string;
  };
  reviewCount: number;
  wishlistCount: number;
  totalSold?: number;
  featured: boolean;
  stock: number;
}

export default function BanChayPage() {
  const [bestsellingProducts, setBestsellingProducts] = useState<Product[]>([]);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [cartLoading, setCartLoading] = useState<string | null>(null);
  const [wishlistLoading, setWishlistLoading] = useState<string | null>(null);
  
  const { addItem: addToCart } = useCart();
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist();
  const { showToast } = useToast();

  const handleAddToCart = async (product: Product, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCartLoading(product.id);
    try {
      await addToCart(product.id, 1);
      showToast(`${product.name} đã được thêm vào giỏ hàng`, 'success', 'cart');
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast('Có lỗi xảy ra khi thêm vào giỏ hàng', 'error');
    } finally {
      setCartLoading(null);
    }
  };

  const handleWishlistToggle = async (product: Product, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setWishlistLoading(product.id);
    try {
      const isInWishlistState = isInWishlist(product.id);
      if (isInWishlistState) {
        await removeFromWishlist(product.id);
        showToast(`${product.name} đã được xóa khỏi danh sách yêu thích`, 'info', 'heart');
      } else {
        await addToWishlist(product.id);
        showToast(`${product.name} đã được thêm vào danh sách yêu thích`, 'success', 'heart');
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      showToast('Có lỗi xảy ra khi cập nhật danh sách yêu thích', 'error');
    } finally {
      setWishlistLoading(null);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch bestselling products
        const response = await fetch('/api/products?page=1&limit=20&bestselling=true');
        if (response.ok) {
          const data = await response.json();
          console.log('📊 Bestselling API response:', data);

          if (data.success && data.data) {
            // Handle different response structures
            let products = [];
            if (Array.isArray(data.data)) {
              // For regular product listing: { data: [...] }
              products = data.data;
            } else if (data.data.products && Array.isArray(data.data.products)) {
              // For bestselling products: { data: { products: [...] } }
              products = data.data.products;
            } else {
              console.warn('⚠️ Unexpected data structure:', data.data);
              products = [];
            }

            console.log('📦 Setting bestselling products:', products);
            setBestsellingProducts(products);

            // Get related products if we have bestselling products
            if (products.length > 0) {
              const firstProduct = products[0];
              const relatedResponse = await fetch(`/api/products/related?productId=${firstProduct.id}&limit=8`);
              if (relatedResponse.ok) {
                const relatedData = await relatedResponse.json();
                if (relatedData.success) {
                  setRelatedProducts(relatedData.data || []);
                }
              }
            }
          } else {
            console.warn('⚠️ API response not successful:', data);
            setBestsellingProducts([]);
          }
        } else {
          console.error('❌ Failed to fetch bestselling products:', response.status);
          setBestsellingProducts([]);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <main className="min-h-screen">

      {/* Best Sellers */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Sản Phẩm Bán Chạy Nhất
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Dựa trên số lượng bán ra thực tế, đây là những sản phẩm được khách hàng tin tưởng và lựa chọn nhiều nhất
            </p>
          </div>

          {!Array.isArray(bestsellingProducts) || bestsellingProducts.length === 0 ? (
            <div className="text-center py-16">
              <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Chưa có dữ liệu bán hàng</h3>
              <p className="text-gray-600">Vui lòng quay lại sau khi có thêm đơn hàng.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {bestsellingProducts.map((product, index) => (
                <motion.div
                  key={product.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow group relative"
                >
                  {/* Ranking Badge */}
                  <div className="absolute top-3 left-3 z-10">
                    <div className={`px-3 py-1 rounded-full text-xs font-bold text-white ${
                      index < 3 ? 'bg-gradient-to-r from-yellow-400 to-orange-500' : 'bg-orange-500'
                    }`}>
                      #{index + 1}
                    </div>
                  </div>

                  {/* Sale Badge */}
                  {product.originalPrice && (
                    <div className="absolute top-3 right-3 z-10">
                      <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                        -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                      </div>
                    </div>
                  )}

                  <div className="relative overflow-hidden">
                    <img
                      src={product.images[0] || '/images/placeholder.svg'}
                      alt={product.name}
                      className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    
                    {/* Hover Actions */}
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-3">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={(e) => handleWishlistToggle(product, e)}
                        disabled={wishlistLoading === product.id}
                        className={`p-3 rounded-full transition-all duration-200 ${
                          isInWishlist(product.id)
                            ? 'bg-red-500 text-white'
                            : 'bg-white text-gray-800 hover:bg-gray-100'
                        }`}
                        title={isInWishlist(product.id) ? 'Xóa khỏi yêu thích' : 'Thêm vào yêu thích'}
                      >
                        {wishlistLoading === product.id ? (
                          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <Heart className={`w-5 h-5 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
                        )}
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={(e) => handleAddToCart(product, e)}
                        disabled={cartLoading === product.id}
                        className="p-3 bg-white rounded-full text-gray-800 hover:bg-gray-100 transition-colors"
                        title="Thêm vào giỏ hàng"
                      >
                        {cartLoading === product.id ? (
                          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <ShoppingCart className="w-5 h-5" />
                        )}
                      </motion.button>
                      <Link href={`/san-pham/${product.slug}`}>
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-3 bg-white rounded-full text-gray-800 hover:bg-gray-100 transition-colors"
                          title="Xem chi tiết"
                        >
                          <Eye className="w-5 h-5" />
                        </motion.button>
                      </Link>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Related Products */}
      {relatedProducts.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Sản Phẩm Liên Quan
              </h2>
              <p className="text-gray-600">
                Các sản phẩm tương tự mà bạn có thể quan tâm
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {relatedProducts.map((product, index) => (
                <motion.div
                  key={product.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow group"
                >
                  {product.originalPrice && (
                    <div className="absolute top-3 right-3 z-10">
                      <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                        -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                      </div>
                    </div>
                  )}

                  <div className="relative overflow-hidden">
                    <img
                      src={product.images[0] || '/images/placeholder.svg'}
                      alt={product.name}
                      className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    
                    {/* Hover Actions */}
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-3">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={(e) => handleWishlistToggle(product, e)}
                        disabled={wishlistLoading === product.id}
                        className={`p-3 rounded-full transition-all duration-200 ${
                          isInWishlist(product.id)
                            ? 'bg-red-500 text-white'
                            : 'bg-white text-gray-800 hover:bg-gray-100'
                        }`}
                        title={isInWishlist(product.id) ? 'Xóa khỏi yêu thích' : 'Thêm vào yêu thích'}
                      >
                        {wishlistLoading === product.id ? (
                          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <Heart className={`w-5 h-5 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
                        )}
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={(e) => handleAddToCart(product, e)}
                        disabled={cartLoading === product.id}
                        className="p-3 bg-white rounded-full text-gray-800 hover:bg-gray-100 transition-colors"
                        title="Thêm vào giỏ hàng"
                      >
                        {cartLoading === product.id ? (
                          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <ShoppingCart className="w-5 h-5" />
                        )}
                      </motion.button>
                      <Link href={`/san-pham/${product.slug}`}>
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-3 bg-white rounded-full text-gray-800 hover:bg-gray-100 transition-colors"
                          title="Xem chi tiết"
                        >
                          <Eye className="w-5 h-5" />
                        </motion.button>
                      </Link>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <p className="text-sm text-orange-600 font-medium mb-1">
                      {product.category.name}
                    </p>
                    <h3 className="font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-orange-600 transition-colors">
                      {product.name}
                    </h3>
                    
                    {/* Stats */}
                    <div className="flex items-center gap-4 mb-3 text-sm text-gray-500">
                      {product.totalSold !== undefined && (
                        <span className="flex items-center gap-1">
                          <ShoppingCart className="w-4 h-4" />
                          {product.totalSold} đã bán
                        </span>
                      )}
                      {product.reviewCount > 0 && (
                        <span className="flex items-center gap-1">
                          ⭐ {product.reviewCount}
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        {product.originalPrice ? (
                          <div>
                            <span className="text-lg font-bold text-red-600">
                              {product.price.toLocaleString('vi-VN')}đ
                            </span>
                            <span className="text-sm text-gray-400 line-through ml-2">
                              {product.originalPrice.toLocaleString('vi-VN')}đ
                            </span>
                          </div>
                        ) : (
                          <span className="text-lg font-bold text-gray-900">
                            {product.price.toLocaleString('vi-VN')}đ
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      )}
    </main>
  );
}
