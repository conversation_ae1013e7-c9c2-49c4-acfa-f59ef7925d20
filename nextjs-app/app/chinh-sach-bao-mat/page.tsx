'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Lock, Eye, Database, UserCheck, AlertTriangle, Phone, Mail } from 'lucide-react';

export default function ChinhSachBaoMatPage() {
  const privacyPrinciples = [
    {
      icon: <Lock className="w-6 h-6" />,
      title: "Bảo mật thông tin",
      description: "Mọi thông tin cá nhân được mã hóa và bảo vệ an toàn"
    },
    {
      icon: <Eye className="w-6 h-6" />,
      title: "<PERSON> bạch sử dụng",
      description: "Chúng tôi chỉ sử dụng thông tin cho mục đích đã thông báo"
    },
    {
      icon: <UserCheck className="w-6 h-6" />,
      title: "Quyền kiểm soát",
      description: "Bạn có quyền truy cập, chỉnh sửa và xóa thông tin cá nhân"
    },
    {
      icon: <Database className="w-6 h-6" />,
      title: "<PERSON><PERSON><PERSON> trữ an toàn",
      description: "<PERSON><PERSON> liệu được lưu trữ trên hệ thống bảo mật cao cấp"
    }
  ];

  const dataCollection = [
    {
      category: "Thông tin cá nhân",
      items: [
        "Họ tên, số điện thoại, email",
        "Địa chỉ giao hàng và thanh toán",
        "Ngày sinh, giới tính (nếu cung cấp)"
      ]
    },
    {
      category: "Thông tin giao dịch",
      items: [
        "Lịch sử mua hàng và đơn hàng",
        "Phương thức thanh toán",
        "Thông tin vận chuyển"
      ]
    },
    {
      category: "Thông tin kỹ thuật",
      items: [
        "Địa chỉ IP và thông tin thiết bị",
        "Cookies và dữ liệu trình duyệt",
        "Hành vi sử dụng website"
      ]
    }
  ];

  const dataUsage = [
    "Xử lý đơn hàng và giao hàng",
    "Cung cấp dịch vụ chăm sóc khách hàng",
    "Gửi thông tin khuyến mãi và sản phẩm mới",
    "Cải thiện chất lượng dịch vụ",
    "Phân tích và thống kê kinh doanh",
    "Tuân thủ các quy định pháp luật"
  ];

  const securityMeasures = [
    {
      title: "Mã hóa SSL",
      description: "Tất cả dữ liệu được truyền tải qua kết nối SSL 256-bit"
    },
    {
      title: "Tường lửa bảo mật",
      description: "Hệ thống tường lửa đa lớp bảo vệ khỏi các cuộc tấn công"
    },
    {
      title: "Kiểm soát truy cập",
      description: "Chỉ nhân viên được ủy quyền mới có thể truy cập dữ liệu"
    },
    {
      title: "Sao lưu định kỳ",
      description: "Dữ liệu được sao lưu thường xuyên để đảm bảo an toàn"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mb-6">
              <Shield className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Chính Sách Bảo Mật
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Chúng tôi cam kết bảo vệ thông tin cá nhân của bạn với các biện pháp bảo mật hàng đầu.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Privacy Principles */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Nguyên Tắc Bảo Mật
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Chúng tôi tuân thủ các nguyên tắc bảo mật nghiêm ngặt để đảm bảo an toàn thông tin
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {privacyPrinciples.map((principle, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center p-6 bg-blue-50 rounded-lg hover:shadow-lg transition-shadow"
              >
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="text-blue-600">
                    {principle.icon}
                  </div>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">{principle.title}</h3>
                <p className="text-gray-600 text-sm">{principle.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Data Collection */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Thông Tin Chúng Tôi Thu Thập
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Chúng tôi chỉ thu thập những thông tin cần thiết để cung cấp dịch vụ tốt nhất
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {dataCollection.map((category, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white p-6 rounded-lg shadow-lg"
                >
                  <h3 className="font-bold text-gray-900 mb-4 text-lg">{category.category}</h3>
                  <ul className="space-y-2">
                    {category.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                        <span className="text-gray-600 text-sm">{item}</span>
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Data Usage */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Mục Đích Sử Dụng Thông Tin
              </h2>
              <p className="text-gray-600">
                Thông tin của bạn được sử dụng cho các mục đích sau
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {dataUsage.map((usage, index) => (
                  <div key={index} className="flex items-center">
                    <div className="w-3 h-3 bg-blue-500 rounded-full mr-3 flex-shrink-0" />
                    <span className="text-blue-900">{usage}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Security Measures */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Biện Pháp Bảo Mật
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Chúng tôi áp dụng các công nghệ bảo mật tiên tiến nhất
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {securityMeasures.map((measure, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <h3 className="font-bold text-gray-900 mb-2 text-lg">{measure.title}</h3>
                <p className="text-gray-600">{measure.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Rights & Contact */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Quyền Của Bạn
              </h2>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-lg mb-8">
              <div className="flex items-center mb-4">
                <AlertTriangle className="w-6 h-6 text-blue-600 mr-2" />
                <h3 className="font-bold text-blue-900">Quyền kiểm soát dữ liệu cá nhân</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Bạn có quyền:</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Truy cập và xem thông tin cá nhân</li>
                    <li>• Chỉnh sửa thông tin không chính xác</li>
                    <li>• Yêu cầu xóa thông tin cá nhân</li>
                    <li>• Từ chối nhận email marketing</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Liên hệ để:</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Báo cáo vi phạm bảo mật</li>
                    <li>• Đặt câu hỏi về chính sách</li>
                    <li>• Yêu cầu hỗ trợ dữ liệu</li>
                    <li>• Khiếu nại về quyền riêng tư</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg text-center"
              >
                <Phone className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                <h3 className="font-bold text-gray-900 mb-2">Hotline Bảo Mật</h3>
                <p className="text-gray-600 mb-4">Báo cáo vấn đề bảo mật</p>
                <a href="tel:0932935085" className="text-blue-600 font-bold text-lg">
                  0932.935.085
                </a>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg text-center"
              >
                <Mail className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                <h3 className="font-bold text-gray-900 mb-2">Email Bảo Mật</h3>
                <p className="text-gray-600 mb-4">Gửi yêu cầu về dữ liệu</p>
                <a href="mailto:<EMAIL>" className="text-blue-600 font-bold text-lg">
                  <EMAIL>
                </a>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
