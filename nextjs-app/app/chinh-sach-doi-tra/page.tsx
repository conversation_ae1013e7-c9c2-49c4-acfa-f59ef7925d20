'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { RefreshCw, Clock, CheckCircle, XCircle, Package, CreditCard, Phone, Mail } from 'lucide-react';

export default function ChinhSachDoiTraPage() {
  const returnConditions = [
    {
      icon: <Clock className="w-6 h-6" />,
      title: "Thời gian đổi trả",
      description: "Trong vòng 30 ngày kể từ ngày nhận hàng"
    },
    {
      icon: <Package className="w-6 h-6" />,
      title: "Tình trạng sản phẩm",
      description: "Sản phẩm còn nguyên tem mác, chưa qua sử dụng"
    },
    {
      icon: <CheckCircle className="w-6 h-6" />,
      title: "Hóa đơn mua hàng",
      description: "<PERSON><PERSON> hóa đơn hoặc biên lai mua hàng hợp lệ"
    },
    {
      icon: <RefreshCw className="w-6 h-6" />,
      title: "<PERSON>ý do đổi trả",
      description: "Lỗi từ nhà sản xuất hoặc không vừa size"
    }
  ];

  const returnProcess = [
    {
      step: "01",
      title: "Liên hệ hỗ trợ",
      description: "Gọi hotline hoặc gửi email thông báo đổi trả"
    },
    {
      step: "02", 
      title: "Xác nhận thông tin",
      description: "Cung cấp mã đơn hàng và lý do đổi trả"
    },
    {
      step: "03",
      title: "Gửi sản phẩm",
      description: "Đóng gói cẩn thận và gửi về địa chỉ của chúng tôi"
    },
    {
      step: "04",
      title: "Kiểm tra & xử lý",
      description: "Chúng tôi kiểm tra và xử lý đổi trả trong 3-5 ngày"
    }
  ];

  const nonReturnableItems = [
    "Sản phẩm đã qua sử dụng hoặc có dấu hiệu hư hỏng do người dùng",
    "Sản phẩm đã bị tháo tem mác, nhãn hiệu",
    "Sản phẩm thuộc danh mục đồ lót, tất/vớ vì lý do vệ sinh",
    "Sản phẩm được mua trong chương trình khuyến mãi đặc biệt",
    "Sản phẩm quá thời hạn đổi trả (30 ngày)"
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-50 via-white to-orange-50 py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-orange-500 to-red-600 rounded-full mb-6">
              <RefreshCw className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Chính Sách Đổi Trả
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Chúng tôi cam kết mang đến trải nghiệm mua sắm tốt nhất với chính sách đổi trả linh hoạt và thuận tiện.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Return Conditions */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Điều Kiện Đổi Trả
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Để đảm bảo quyền lợi của khách hàng, vui lòng đọc kỹ các điều kiện sau
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {returnConditions.map((condition, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow"
              >
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="text-orange-600">
                    {condition.icon}
                  </div>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">{condition.title}</h3>
                <p className="text-gray-600 text-sm">{condition.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Return Process */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Quy Trình Đổi Trả
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Quy trình đổi trả đơn giản và nhanh chóng chỉ với 4 bước
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {returnProcess.map((process, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="text-center relative"
                >
                  <div className="w-16 h-16 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white font-bold text-lg">
                    {process.step}
                  </div>
                  <h3 className="font-bold text-gray-900 mb-2">{process.title}</h3>
                  <p className="text-gray-600 text-sm">{process.description}</p>
                  
                  {/* Connector Line */}
                  {index < returnProcess.length - 1 && (
                    <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-orange-200 -translate-x-1/2" />
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Non-returnable Items */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Sản Phẩm Không Được Đổi Trả
              </h2>
              <p className="text-gray-600">
                Một số sản phẩm không áp dụng chính sách đổi trả vì lý do vệ sinh và chất lượng
              </p>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <XCircle className="w-6 h-6 text-red-600 mr-2" />
                <h3 className="font-bold text-red-900">Lưu ý quan trọng</h3>
              </div>
              <ul className="space-y-3">
                {nonReturnableItems.map((item, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                    <span className="text-red-800">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-orange-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              Cần Hỗ Trợ Đổi Trả?
            </h2>
            <p className="text-gray-600 mb-8">
              Đội ngũ chăm sóc khách hàng của chúng tôi luôn sẵn sàng hỗ trợ bạn
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <Phone className="w-8 h-8 text-orange-600 mx-auto mb-4" />
                <h3 className="font-bold text-gray-900 mb-2">Hotline</h3>
                <p className="text-gray-600 mb-4">Gọi ngay để được hỗ trợ</p>
                <a href="tel:0932935085" className="text-orange-600 font-bold text-lg">
                  0932.935.085
                </a>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <Mail className="w-8 h-8 text-orange-600 mx-auto mb-4" />
                <h3 className="font-bold text-gray-900 mb-2">Email</h3>
                <p className="text-gray-600 mb-4">Gửi email cho chúng tôi</p>
                <a href="mailto:<EMAIL>" className="text-orange-600 font-bold text-lg">
                  <EMAIL>
                </a>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
