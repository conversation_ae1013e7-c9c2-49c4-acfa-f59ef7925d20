'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ProductGrid from '../../src/components/ui/ProductGrid';
import ProductSlider from '../../src/components/ui/ProductSlider';
import CategoryFilter from '../../src/components/ui/CategoryFilter';
import { Category } from '@prisma/client';

export default function CollectionsPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [email, setEmail] = useState('');
  const [newsletterStatus, setNewsletterStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const result = await response.json();
      if (result.success) {
        setCategories(result.data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setNewsletterStatus('loading');

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (result.success) {
        setNewsletterStatus('success');
        setEmail('');
      } else {
        throw new Error(result.error || 'Failed to subscribe');
      }
    } catch (error) {
      console.error('Error subscribing to newsletter:', error);
      setNewsletterStatus('error');
    }
  };

  return (
    <main className="min-h-screen">
      {/* Elegant Hero Section */}
      <section className="bg-white py-20">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center space-x-2 bg-gray-100 rounded-full px-6 py-2 mb-6">
              <span className="text-sm font-medium text-gray-700">Bộ sưu tập được tuyển chọn</span>
            </div>
            <h1 className="elegant-title text-4xl md:text-5xl lg:text-6xl mb-6">
              Bộ Sưu Tập
            </h1>
            <p className="elegant-body text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
              Khám phá những bộ sưu tập được tuyển chọn với xu hướng mới nhất và phong cách vượt thời gian
            </p>
          </motion.div>
        </div>
      </section>

      {/* Featured Products Slider */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Sản Phẩm Nổi Bật</h2>
            <p className="text-gray-600">Những sản phẩm được tuyển chọn từ bộ sưu tập mới nhất</p>
          </motion.div>
          
          <ProductSlider
            filters={{ featured: true }}
            autoPlay={true}
            showDots={true}
          />
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-8 bg-gray-50">
        <div className="container mx-auto px-4">
          <CategoryFilter
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            loading={loading}
          />
        </div>
      </section>

      {/* All Products Grid */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <ProductGrid
            filters={{ 
              category: selectedCategory || undefined,
              sortBy: 'createdAt',
              sortOrder: 'desc'
            }}
            title={selectedCategory ? `Bộ Sưu Tập ${selectedCategory}` : 'Tất Cả Sản Phẩm'}
            showPagination={true}
            limit={12}
          />
        </div>
      </section>

      {/* Sale Products Slider */}
      <section className="py-16 bg-red-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-red-600 mb-4">Sản Phẩm Giảm Giá</h2>
            <p className="text-gray-600">Ưu đãi có thời hạn cho các sản phẩm được chọn</p>
          </motion.div>
          
          <ProductSlider
            filters={{ onSale: true }}
            autoPlay={false}
            showDots={false}
            slidesToShow={4}
          />
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">Cập Nhật Tin Tức</h2>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
              Đăng ký nhận bản tin để là người đầu tiên biết về các bộ sưu tập mới,
              ưu đãi độc quyền và mẹo thời trang.
            </p>
            <form onSubmit={handleNewsletterSubmit} className="max-w-md mx-auto">
              <div className="flex gap-4">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Nhập email của bạn"
                  required
                  className="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-white"
                />
                <button
                  type="submit"
                  disabled={newsletterStatus === 'loading'}
                  className="bg-white text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors disabled:opacity-50"
                >
                  {newsletterStatus === 'loading' ? 'Đang đăng ký...' : 'Đăng ký'}
                </button>
              </div>
              {newsletterStatus === 'success' && (
                <p className="text-green-300 mt-2 text-center">Đăng ký thành công!</p>
              )}
              {newsletterStatus === 'error' && (
                <p className="text-red-300 mt-2 text-center">Đăng ký thất bại. Vui lòng thử lại.</p>
              )}
            </form>
          </motion.div>
        </div>
      </section>
    </main>
  );
}
