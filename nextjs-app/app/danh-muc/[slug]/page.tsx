/* eslint-disable @next/next/no-img-element */
/* eslint-disable @next/next/no-html-link-for-pages */
'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Grid,
  List,
  Star,
  Heart,
  ShoppingCart,
  Search,
  SlidersHorizontal,
  ArrowUpDown,
  ChevronRight,
  Package,
  Loader2,
  Eye
} from 'lucide-react';
// Removed ConditionalLayout import as it's already handled by RootLayout
import { useCart } from '../../../src/contexts/CartContext';
import { useWishlist } from '../../../src/contexts/WishlistContext';

interface Product {
  id: string;
  name: string;
  slug: string;
  shortDescription: string | null;
  price: number;
  originalPrice: number | null;
  stock: number;
  images: string[];
  featured: boolean;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  _count: {
    reviews: number;
    wishlistItems: number;
  };
}

interface Category {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  image: string | null;
}

interface CategoryData {
  category: Category;
  products: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export default function CategoryPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { addItem } = useCart();
  const { items: wishlistItems, addItem: addToWishlist, removeItem: removeFromWishlist } = useWishlist();

  const categorySlug = params.slug as string;

  const [data, setData] = useState<CategoryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [addingToCart, setAddingToCart] = useState<string | null>(null);

  // Filter states
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [featuredOnly, setFeaturedOnly] = useState(false);
  const [inStockOnly, setInStockOnly] = useState(false);

  useEffect(() => {
    // Get initial values from URL params
    const page = parseInt(searchParams.get('page') || '1');
    const sort = searchParams.get('sortBy') || 'createdAt';
    const order = searchParams.get('sortOrder') || 'desc';
    const search = searchParams.get('search') || '';
    const min = searchParams.get('minPrice') || '';
    const max = searchParams.get('maxPrice') || '';
    const featured = searchParams.get('featured') === 'true';
    const inStock = searchParams.get('inStock') === 'true';

    setCurrentPage(page);
    setSortBy(sort);
    setSortOrder(order);
    setSearchTerm(search);
    setMinPrice(min);
    setMaxPrice(max);
    setFeaturedOnly(featured);
    setInStockOnly(inStock);

    const fetchData = async () => {
      try {
        const params = new URLSearchParams();
        params.set('page', page.toString());
        params.set('limit', '12');
        params.set('sortBy', sort);
        params.set('sortOrder', order);
        
        if (search) params.set('search', search);
        if (min) params.set('minPrice', min);
        if (max) params.set('maxPrice', max);
        if (featured) params.set('featured', 'true');
        if (inStock) params.set('inStock', 'true');

        const response = await fetch(`/api/categories/${categorySlug}/products?${params}`);
        
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setData(result.data);
          } else {
            console.error('Failed to fetch category data:', result.message);
          }
        } else {
          console.error('Failed to fetch category data:', response.status);
        }
      } catch (error) {
        console.error('Error fetching category data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [categorySlug, searchParams]);



  const updateURL = (newPage?: number, newSortBy?: string, newSortOrder?: string, newSearchTerm?: string, newMinPrice?: string, newMaxPrice?: string, newFeaturedOnly?: boolean, newInStockOnly?: boolean) => {
    const params = new URLSearchParams();
    const pageToUse = newPage !== undefined ? newPage : currentPage;
    const sortByToUse = newSortBy !== undefined ? newSortBy : sortBy;
    const sortOrderToUse = newSortOrder !== undefined ? newSortOrder : sortOrder;
    const searchTermToUse = newSearchTerm !== undefined ? newSearchTerm : searchTerm;
    const minPriceToUse = newMinPrice !== undefined ? newMinPrice : minPrice;
    const maxPriceToUse = newMaxPrice !== undefined ? newMaxPrice : maxPrice;
    const featuredOnlyToUse = newFeaturedOnly !== undefined ? newFeaturedOnly : featuredOnly;
    const inStockOnlyToUse = newInStockOnly !== undefined ? newInStockOnly : inStockOnly;

    if (pageToUse > 1) params.set('page', pageToUse.toString());
    if (sortByToUse !== 'createdAt') params.set('sortBy', sortByToUse);
    if (sortOrderToUse !== 'desc') params.set('sortOrder', sortOrderToUse);
    if (searchTermToUse) params.set('search', searchTermToUse);
    if (minPriceToUse) params.set('minPrice', minPriceToUse);
    if (maxPriceToUse) params.set('maxPrice', maxPriceToUse);
    if (featuredOnlyToUse) params.set('featured', 'true');
    if (inStockOnlyToUse) params.set('inStock', 'true');

    const newURL = `/danh-muc/${categorySlug}${params.toString() ? '?' + params.toString() : ''}`;
    router.push(newURL, { scroll: false });
  };

  const handleFilterChange = () => {
    updateURL(1, sortBy, sortOrder, searchTerm, minPrice, maxPrice, featuredOnly, inStockOnly);
  };

  const handlePageChange = (page: number) => {
    updateURL(page);
  };

  const handleAddToCart = async (productId: string) => {
    setAddingToCart(productId);
    try {
      await addItem(productId, 1);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setAddingToCart(null);
    }
  };

  const isInWishlist = (productId: string) => {
    return wishlistItems.some(item => item.productId === productId);
  };

  const handleWishlistToggle = async (productId: string) => {
    if (isInWishlist(productId)) {
      await removeFromWishlist(productId);
    } else {
      await addToWishlist(productId);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const getProductImage = (images: string[]) => {
    return images && images.length > 0 ? images[0] : '/images/placeholder.svg';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h2 className="text-2xl font-bold text-gray-900 mt-4">Không tìm thấy danh mục</h2>
            <p className="mt-2 text-gray-600">Danh mục bạn đang tìm không tồn tại.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
        {/* Breadcrumb */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <nav className="flex items-center space-x-2 text-sm">
              <a href="/" className="text-gray-500 hover:text-gray-700">Trang chủ</a>
              <ChevronRight className="w-4 h-4 text-gray-400" />
              <a href="/danh-muc" className="text-gray-500 hover:text-gray-700">Danh mục</a>
              <ChevronRight className="w-4 h-4 text-gray-400" />
              <span className="text-gray-900 font-medium">{data.category.name}</span>
            </nav>
          </div>
        </div>

        {/* Category Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              {data.category.image && (
                <div className="mb-6">
                  <img
                    src={data.category.image}
                    alt={data.category.name}
                    className="w-24 h-24 mx-auto rounded-full object-cover border-4 border-white shadow-lg"
                  />
                </div>
              )}
              <h1 className="text-4xl font-bold text-gray-900 mb-4">{data.category.name}</h1>
              {data.category.description && (
                <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-4">
                  {data.category.description}
                </p>
              )}
              <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <Package className="w-4 h-4 mr-1" />
                  {data.pagination.total} sản phẩm
                </span>
                <span className="flex items-center">
                  <Star className="w-4 h-4 mr-1 text-yellow-400" />
                  Chất lượng cao
                </span>
                <span className="flex items-center">
                  <Heart className="w-4 h-4 mr-1 text-red-400" />
                  Được yêu thích
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && updateURL(1, sortBy, sortOrder, e.currentTarget.value, minPrice, maxPrice, featuredOnly, inStockOnly)}
                    placeholder="Tìm kiếm sản phẩm..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-4">
                {/* Sort */}
                <div className="flex items-center space-x-2">
                  <ArrowUpDown className="w-4 h-4 text-gray-400" />
                  <select
                    value={`${sortBy}-${sortOrder}`}
                    onChange={(e) => {
                      const [newSortBy, newSortOrder] = e.target.value.split('-');
                      updateURL(1, newSortBy, newSortOrder, searchTerm, minPrice, maxPrice, featuredOnly, inStockOnly);
                    }}
                    className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="createdAt-desc">Mới nhất</option>
                    <option value="createdAt-asc">Cũ nhất</option>
                    <option value="price-asc">Giá thấp đến cao</option>
                    <option value="price-desc">Giá cao đến thấp</option>
                    <option value="name-asc">Tên A-Z</option>
                    <option value="name-desc">Tên Z-A</option>
                  </select>
                </div>

                {/* View Mode */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'text-gray-400 hover:text-gray-600'}`}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'text-gray-400 hover:text-gray-600'}`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>

                {/* Filter Toggle */}
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium transition-colors ${
                    showFilters
                      ? 'border-blue-300 text-blue-700 bg-blue-50'
                      : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                  }`}
                >
                  <SlidersHorizontal className="w-4 h-4 mr-2" />
                  Bộ lọc
                </button>
              </div>
            </div>

            {/* Advanced Filters */}
            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-4 pt-4 border-t border-gray-200"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* Price Range */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Khoảng giá</label>
                      <div className="flex space-x-2">
                        <input
                          type="number"
                          value={minPrice}
                          onChange={(e) => setMinPrice(e.target.value)}
                          placeholder="Từ"
                          className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <input
                          type="number"
                          value={maxPrice}
                          onChange={(e) => setMaxPrice(e.target.value)}
                          placeholder="Đến"
                          className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>

                    {/* Featured */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Sản phẩm nổi bật</label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={featuredOnly}
                          onChange={(e) => setFeaturedOnly(e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">Chỉ sản phẩm nổi bật</span>
                      </label>
                    </div>

                    {/* In Stock */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Tình trạng</label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={inStockOnly}
                          onChange={(e) => setInStockOnly(e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">Chỉ còn hàng</span>
                      </label>
                    </div>

                    {/* Apply Filters */}
                    <div className="flex items-end">
                      <button
                        onClick={handleFilterChange}
                        className="w-full bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        Áp dụng bộ lọc
                      </button>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Products Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
          {data.products.length > 0 ? (
            <>
              <div className={`grid gap-6 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                  : 'grid-cols-1'
              }`}>
                {data.products.map((product, index) => (
                  <motion.div
                    key={product.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className={`group bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-xl hover:border-blue-200 transition-all duration-300 ${
                      viewMode === 'list' ? 'flex' : ''
                    }`}
                  >
                    {/* Product Image */}
                    <div className={`relative overflow-hidden ${viewMode === 'list' ? 'w-48 flex-shrink-0' : 'aspect-square'}`}>
                      <img
                        src={getProductImage(product.images)}
                        alt={product.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />

                      {/* Overlay on hover */}
                      <div className="absolute inset-0 bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300" />

                      {/* Badges */}
                      <div className="absolute top-3 left-3 flex flex-col space-y-2">
                        {product.featured && (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-yellow-400 to-orange-400 text-white shadow-lg">
                            <Star className="w-3 h-3 mr-1 fill-current" />
                            Nổi bật
                          </span>
                        )}
                        {product.originalPrice && (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg">
                            -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                          </span>
                        )}
                        {product.stock <= 5 && product.stock > 0 && (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-orange-400 to-red-400 text-white shadow-lg">
                            Sắp hết
                          </span>
                        )}
                        {product.stock === 0 && (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-gray-500 text-white shadow-lg">
                            Hết hàng
                          </span>
                        )}
                      </div>

                      {/* Wishlist Button */}
                      <button
                        onClick={() => handleWishlistToggle(product.id)}
                        className="absolute top-3 right-3 p-2.5 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl hover:bg-white transition-all duration-200 group/wishlist"
                      >
                        <Heart
                          className={`w-5 h-5 transition-colors duration-200 ${
                            isInWishlist(product.id)
                              ? 'text-red-500 fill-current'
                              : 'text-gray-600 group-hover/wishlist:text-red-500'
                          }`}
                        />
                      </button>

                      {/* Quick View Button (appears on hover) */}
                      <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <a
                          href={`/san-pham/${product.slug}`}
                          className="w-full inline-flex items-center justify-center px-4 py-2 bg-white/90 backdrop-blur-sm text-gray-900 text-sm font-medium rounded-lg hover:bg-white transition-colors duration-200"
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          Xem nhanh
                        </a>
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className={`p-5 ${viewMode === 'list' ? 'flex-1 flex flex-col justify-between' : ''}`}>
                      <div>
                        {/* Category Badge */}
                        <div className="mb-2">
                          <span className="inline-block px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-full">
                            {product.category.name}
                          </span>
                        </div>

                        <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 mb-2 group-hover:text-blue-600 transition-colors duration-200">
                          <a href={`/san-pham/${product.slug}`}>
                            {product.name}
                          </a>
                        </h3>

                        {product.shortDescription && (
                          <p className="text-sm text-gray-600 line-clamp-2 mb-4">
                            {product.shortDescription}
                          </p>
                        )}

                        {/* Price */}
                        <div className="mb-4">
                          {product.originalPrice ? (
                            <div className="flex items-baseline space-x-2">
                              <span className="text-xl font-bold text-red-600">
                                {formatPrice(product.price)}
                              </span>
                              <span className="text-sm text-gray-500 line-through">
                                {formatPrice(product.originalPrice)}
                              </span>
                            </div>
                          ) : (
                            <span className="text-xl font-bold text-gray-900">
                              {formatPrice(product.price)}
                            </span>
                          )}
                        </div>

                        {/* Rating and Stats */}
                        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                          <div className="flex items-center space-x-1">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`w-4 h-4 ${
                                    i < 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="ml-1">({product._count.reviews})</span>
                          </div>
                          <span className="flex items-center text-red-500">
                            <Heart className="w-4 h-4 mr-1" />
                            {product._count.wishlistItems}
                          </span>
                        </div>

                        {/* Stock Status */}
                        <div className="mb-4">
                          {product.stock > 10 ? (
                            <span className="text-sm text-green-600 font-medium">✓ Còn hàng</span>
                          ) : product.stock > 0 ? (
                            <span className="text-sm text-orange-600 font-medium">⚠ Chỉ còn {product.stock} sản phẩm</span>
                          ) : (
                            <span className="text-sm text-red-600 font-medium">✗ Hết hàng</span>
                          )}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex space-x-2">
                        
                        <button
                          onClick={() => handleAddToCart(product.id)}
                          disabled={product.stock === 0 || addingToCart === product.id}
                          className="flex-1 inline-flex items-center justify-center px-4 py-2.5 border border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg"
                        >
                          {addingToCart === product.id ? (
                            <Loader2 className="w-4 h-4 animate-spin" />
                          ) : (
                            <>
                              <ShoppingCart className="w-4 h-4 mr-2" />
                              {product.stock === 0 ? 'Hết hàng' : 'Thêm vào giỏ'}
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Pagination */}
              {data.pagination.totalPages > 1 && (
                <div className="mt-12 flex flex-col items-center space-y-4">
                  {/* Page Info */}
                  <div className="text-sm text-gray-600">
                    Hiển thị {((currentPage - 1) * 12) + 1} - {Math.min(currentPage * 12, data.pagination.total)} trong tổng số {data.pagination.total} sản phẩm
                  </div>

                  {/* Pagination Controls */}
                  <nav className="relative z-0 inline-flex rounded-xl shadow-lg overflow-hidden">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={!data.pagination.hasPrev}
                      className="relative inline-flex items-center px-4 py-3 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                    >
                      <ChevronRight className="w-4 h-4 rotate-180 mr-1" />
                      Trước
                    </button>

                    {/* Page Numbers */}
                    <div className="flex">
                      {(() => {
                        const pages = [];
                        const totalPages = data.pagination.totalPages;
                        const current = currentPage;

                        // Always show first page
                        if (current > 3) {
                          pages.push(1);
                          if (current > 4) pages.push('...');
                        }

                        // Show pages around current
                        for (let i = Math.max(1, current - 2); i <= Math.min(totalPages, current + 2); i++) {
                          pages.push(i);
                        }

                        // Always show last page
                        if (current < totalPages - 2) {
                          if (current < totalPages - 3) pages.push('...');
                          pages.push(totalPages);
                        }

                        return pages.map((page, index) => {
                          if (page === '...') {
                            return (
                              <span
                                key={`ellipsis-${index}`}
                                className="relative inline-flex items-center px-4 py-3 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                              >
                                ...
                              </span>
                            );
                          }

                          return (
                            <button
                              key={page}
                              onClick={() => handlePageChange(page as number)}
                              className={`relative inline-flex items-center px-4 py-3 border text-sm font-medium transition-colors duration-200 ${
                                currentPage === page
                                  ? 'z-10 bg-blue-600 border-blue-600 text-white'
                                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                              }`}
                            >
                              {page}
                            </button>
                          );
                        });
                      })()}
                    </div>

                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={!data.pagination.hasNext}
                      className="relative inline-flex items-center px-4 py-3 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                    >
                      Sau
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </button>
                  </nav>
                </div>
              )}
            </>
          ) : (
            /* Empty State */
            <div className="text-center py-12">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Không có sản phẩm</h3>
              <p className="mt-1 text-sm text-gray-500">
                Không tìm thấy sản phẩm nào phù hợp với bộ lọc của bạn.
              </p>
              <button
                onClick={() => {
                  updateURL(1, 'createdAt', 'desc', '', '', '', false, false);
                }}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Xóa bộ lọc
              </button>
            </div>
          )}
        </div>
      </div>
  );
}
