'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Category } from '@prisma/client';
import ProductGrid from '../../src/components/ui/ProductGrid';
import CategoryFilter from '../../src/components/ui/CategoryFilter';

export default function DanhMucPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const result = await response.json();
      if (result.success) {
        setCategories(result.data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-50 to-indigo-50 py-20">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-6">
              Danh Mục Sản Phẩm
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Tìm kiếm sản phẩm theo danh mục để dễ dàng lựa chọn
            </p>
          </motion.div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-8 bg-gray-50">
        <div className="container mx-auto px-4">
          <CategoryFilter
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            loading={loading}
          />
        </div>
      </section>

      {/* Category Grid */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          {!selectedCategory ? (
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center mb-12"
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Tất Cả Danh Mục
                </h2>
                <p className="text-gray-600">
                  Chọn một danh mục để xem sản phẩm
                </p>
              </motion.div>

              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {categories.map((category, index) => (
                  <motion.button
                    key={category.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    onClick={() => setSelectedCategory(category.slug)}
                    className="group relative aspect-square overflow-hidden rounded-lg bg-gray-100 hover:shadow-xl transition-all duration-300"
                  >
                    <img
                      src={category.image || '/images/collections/fashion-designer-featured-collection-1.jpg'}
                      alt={category.name}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-colors duration-300"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <h3 className="text-white text-xl font-bold text-center px-4">
                        {category.name}
                      </h3>
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>
          ) : (
            <ProductGrid
              filters={{ category: selectedCategory }}
              title={`Danh mục: ${categories.find(c => c.slug === selectedCategory)?.name || selectedCategory}`}
              showPagination={true}
              limit={12}
            />
          )}
        </div>
      </section>
    </main>
  );
}
