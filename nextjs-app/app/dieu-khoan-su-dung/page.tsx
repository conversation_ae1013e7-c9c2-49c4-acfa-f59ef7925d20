'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { FileText, Users, ShoppingCart, CreditCard, Truck, AlertCircle, Phone, Mail } from 'lucide-react';

export default function DieuKhoanSuDungPage() {
  const terms = [
    {
      icon: <Users className="w-6 h-6" />,
      title: "Tài khoản người dùng",
      description: "Quy định về đăng ký và sử dụng tài khoản"
    },
    {
      icon: <ShoppingCart className="w-6 h-6" />,
      title: "Mua hàng & Đặt hàng",
      description: "Điều khoản về quy trình mua hàng và đặt hàng"
    },
    {
      icon: <CreditCard className="w-6 h-6" />,
      title: "Thanh toán",
      description: "<PERSON><PERSON><PERSON> phương thức và điều kiện thanh toán"
    },
    {
      icon: <Truck className="w-6 h-6" />,
      title: "<PERSON>iao hàng",
      description: "<PERSON><PERSON><PERSON> sách và quy định về giao hàng"
    }
  ];

  const userResponsibilities = [
    "Cung cấp thông tin chính xác và đầy đủ khi đăng ký",
    "Bảo mật thông tin tài khoản và mật khẩu",
    "Không sử dụng website cho mục đích bất hợp pháp",
    "Tuân thủ các quy định về sở hữu trí tuệ",
    "Không spam hoặc gửi nội dung có hại",
    "Thông báo ngay khi phát hiện vi phạm bảo mật"
  ];

  const prohibitedActivities = [
    "Sử dụng robot, spider hoặc công cụ tự động",
    "Tấn công, hack hoặc phá hoại hệ thống",
    "Sao chép, phân phối nội dung trái phép",
    "Đăng tải virus, malware hoặc mã độc",
    "Mạo danh người khác hoặc tổ chức",
    "Bán lại sản phẩm mà không có sự đồng ý"
  ];

  const orderTerms = [
    {
      title: "Xác nhận đơn hàng",
      content: "Đơn hàng chỉ được xác nhận sau khi thanh toán thành công và có email xác nhận từ hệ thống."
    },
    {
      title: "Giá cả và khuyến mãi",
      content: "Giá sản phẩm có thể thay đổi mà không cần báo trước. Khuyến mãi áp dụng theo điều kiện cụ thể."
    },
    {
      title: "Tình trạng hàng hóa",
      content: "Chúng tôi có quyền từ chối đơn hàng nếu sản phẩm hết hàng hoặc có vấn đề về chất lượng."
    },
    {
      title: "Hủy đơn hàng",
      content: "Khách hàng có thể hủy đơn hàng trước khi hàng được giao cho đơn vị vận chuyển."
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-50 via-white to-indigo-50 py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full mb-6">
              <FileText className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Điều Khoản Sử Dụng
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Vui lòng đọc kỹ các điều khoản và điều kiện sử dụng website và dịch vụ của chúng tôi.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Terms Overview */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Tổng Quan Điều Khoản
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Các điều khoản chính áp dụng khi bạn sử dụng dịch vụ của chúng tôi
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {terms.map((term, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center p-6 bg-indigo-50 rounded-lg hover:shadow-lg transition-shadow"
              >
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="text-indigo-600">
                    {term.icon}
                  </div>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">{term.title}</h3>
                <p className="text-gray-600 text-sm">{term.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* User Responsibilities */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Responsibilities */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  Trách Nhiệm Người Dùng
                </h2>
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <Users className="w-6 h-6 text-green-600 mr-2" />
                    <h3 className="font-bold text-green-900">Bạn cam kết sẽ:</h3>
                  </div>
                  <ul className="space-y-3">
                    {userResponsibilities.map((responsibility, index) => (
                      <li key={index} className="flex items-start">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                        <span className="text-green-800">{responsibility}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>

              {/* Prohibited Activities */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  Hoạt Động Bị Cấm
                </h2>
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <AlertCircle className="w-6 h-6 text-red-600 mr-2" />
                    <h3 className="font-bold text-red-900">Nghiêm cấm các hành vi:</h3>
                  </div>
                  <ul className="space-y-3">
                    {prohibitedActivities.map((activity, index) => (
                      <li key={index} className="flex items-start">
                        <div className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                        <span className="text-red-800">{activity}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Order Terms */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Điều Khoản Mua Hàng
              </h2>
              <p className="text-gray-600">
                Các quy định cụ thể về quy trình mua hàng và đặt hàng
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {orderTerms.map((term, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-indigo-50 p-6 rounded-lg"
                >
                  <h3 className="font-bold text-indigo-900 mb-3 text-lg">{term.title}</h3>
                  <p className="text-indigo-800">{term.content}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Intellectual Property */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Sở Hữu Trí Tuệ
              </h2>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="font-bold text-gray-900 mb-4">Quyền của chúng tôi</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• Tất cả nội dung trên website</li>
                    <li>• Logo, thương hiệu, nhãn hiệu</li>
                    <li>• Hình ảnh sản phẩm và mô tả</li>
                    <li>• Thiết kế giao diện website</li>
                    <li>• Mã nguồn và công nghệ</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-bold text-gray-900 mb-4">Quyền của bạn</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• Sử dụng website cho mục đích cá nhân</li>
                    <li>• Chia sẻ link sản phẩm</li>
                    <li>• Đánh giá và bình luận</li>
                    <li>• Tải hình ảnh sản phẩm để tham khảo</li>
                    <li>• Liên hệ hỗ trợ khi cần thiết</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Limitation of Liability */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Giới Hạn Trách Nhiệm
              </h2>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
              <div className="flex items-center mb-4">
                <AlertCircle className="w-6 h-6 text-yellow-600 mr-2" />
                <h3 className="font-bold text-yellow-900">Lưu ý quan trọng</h3>
              </div>
              <div className="text-yellow-800 space-y-2">
                <p>• Chúng tôi không chịu trách nhiệm về thiệt hại gián tiếp hoặc hậu quả</p>
                <p>• Trách nhiệm tối đa không vượt quá giá trị đơn hàng</p>
                <p>• Không đảm bảo website hoạt động liên tục 100%</p>
                <p>• Không chịu trách nhiệm về nội dung từ bên thứ ba</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg text-center border"
              >
                <Phone className="w-8 h-8 text-indigo-600 mx-auto mb-4" />
                <h3 className="font-bold text-gray-900 mb-2">Hỗ Trợ Pháp Lý</h3>
                <p className="text-gray-600 mb-4">Tư vấn về điều khoản</p>
                <a href="tel:0932935085" className="text-indigo-600 font-bold text-lg">
                  0932.935.085
                </a>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg text-center border"
              >
                <Mail className="w-8 h-8 text-indigo-600 mx-auto mb-4" />
                <h3 className="font-bold text-gray-900 mb-2">Email Pháp Lý</h3>
                <p className="text-gray-600 mb-4">Gửi câu hỏi pháp lý</p>
                <a href="mailto:<EMAIL>" className="text-indigo-600 font-bold text-lg">
                  <EMAIL>
                </a>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
