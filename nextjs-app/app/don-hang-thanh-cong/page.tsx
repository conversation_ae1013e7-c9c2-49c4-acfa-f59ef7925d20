'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, ShoppingBag, Clock, Package, Home, Search, Copy } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useToast } from '../../src/components/ui/Toast';

function DonHangThanhCongContent() {
  const searchParams = useSearchParams();
  const orderNumber = searchParams.get('orderNumber');
  const [isVisible, setIsVisible] = useState(false);
  const { showToast } = useToast();

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const copyOrderNumber = async () => {
    if (orderNumber) {
      try {
        await navigator.clipboard.writeText(orderNumber);
        showToast('Đã sao chép mã đơn hàng!', 'success');
      } catch (error) {
        console.error('Failed to copy order number:', error);
        showToast('Không thể sao chép mã đơn hàng', 'error');
      }
    }
  };

  if (!orderNumber) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ShoppingBag className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Không tìm thấy thông tin đơn hàng
          </h2>
          <Link
            href="/"
            className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
          >
            Về trang chủ
          </Link>
        </div>
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: isVisible ? 1 : 0, scale: isVisible ? 1 : 0.8 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="max-w-2xl mx-auto text-center"
        >
          {/* Success Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5, type: "spring", stiffness: 150 }}
            className="mb-8"
          >
            <div className="inline-flex items-center justify-center w-24 h-24 bg-green-100 rounded-full mb-6">
              <CheckCircle className="w-12 h-12 text-green-500" />
            </div>
          </motion.div>

          {/* Success Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            className="mb-8"
          >
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Đặt hàng thành công!
            </h1>
            <p className="text-xl text-gray-600 mb-2">
              Cảm ơn bạn đã mua sắm tại cửa hàng của chúng tôi
            </p>
            <div className="space-y-4">
              <div className="flex items-center justify-center space-x-2">
                <span className="text-lg text-gray-500">Mã đơn hàng:</span>
                <span className="font-semibold text-gray-900 font-mono text-lg">{orderNumber}</span>
                <button
                  onClick={copyOrderNumber}
                  className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Sao chép mã đơn hàng"
                >
                  <Copy className="w-4 h-4" />
                </button>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link
                  href={`/tra-cuu-don-hang?orderNumber=${orderNumber}`}
                  className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  <Search className="w-4 h-4 mr-2" />
                  Tra cứu đơn hàng
                </Link>
                <Link
                  href="/tra-cuu-don-hang"
                  className="inline-flex items-center justify-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                >
                  <Package className="w-4 h-4 mr-2" />
                  Tra cứu đơn hàng khác
                </Link>
              </div>
            </div>
          </motion.div>

          {/* Order Details Card */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="bg-white rounded-lg shadow-sm p-8 mb-8"
          >
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Thông tin đơn hàng
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <Clock className="w-6 h-6 text-orange-500 mt-1" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">Xử lý đơn hàng</h3>
                  <p className="text-sm text-gray-600">
                    Chúng tôi sẽ xử lý đơn hàng của bạn trong vòng 24h
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <Package className="w-6 h-6 text-blue-500 mt-1" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">Giao hàng</h3>
                  <p className="text-sm text-gray-600">
                    Phí vận chuyển sẽ được báo giá qua điện thoại
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <CheckCircle className="w-6 h-6 text-green-500 mt-1" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">Thanh toán</h3>
                  <p className="text-sm text-gray-600">
                    Thanh toán khi nhận hàng (COD)
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Next Steps */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="bg-orange-50 rounded-lg p-6 mb-8"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              Tiếp theo sẽ diễn ra gì?
            </h3>
            <div className="space-y-3 text-left">
              <div className="flex items-center space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</span>
                <p className="text-gray-700">Chúng tôi sẽ liên hệ với bạn để xác nhận đơn hàng và báo giá phí vận chuyển</p>
              </div>
              <div className="flex items-center space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium">2</span>
                <p className="text-gray-700">Đóng gói và gửi hàng đến địa chỉ của bạn</p>
              </div>
              <div className="flex items-center space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</span>
                <p className="text-gray-700">Bạn thanh toán khi nhận hàng và kiểm tra sản phẩm</p>
              </div>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link
              href="/"
              className="bg-orange-500 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors flex items-center justify-center"
            >
              <Home className="w-5 h-5 mr-2" />
              Về trang chủ
            </Link>
            
            <Link
              href="/san-pham"
              className="bg-gray-100 text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors flex items-center justify-center"
            >
              <ShoppingBag className="w-5 h-5 mr-2" />
              Tiếp tục mua sắm
            </Link>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2, duration: 0.6 }}
            className="mt-12 pt-8 border-t border-gray-200"
          >
            <p className="text-gray-600 mb-2">
              Nếu bạn có bất kỳ câu hỏi nào về đơn hàng, vui lòng liên hệ với chúng tôi:
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center text-sm">
              <a
                href="tel:0932935085"
                className="text-orange-500 hover:text-orange-600 font-medium"
              >
                📞 0932935085
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-orange-500 hover:text-orange-600 font-medium"
              >
                ✉️ <EMAIL>
              </a>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  );
}

export default function DonHangThanhCongPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    }>
      <DonHangThanhCongContent />
    </Suspense>
  );
}