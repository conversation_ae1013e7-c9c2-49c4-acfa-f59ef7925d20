'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import { ShoppingBag, Trash2, Plus, Minus, ArrowLeft, Package, AlertTriangle } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useCart } from '../../src/contexts/CartContext';
import ProductAttributeTags from '../../src/components/ui/ProductAttributeTags';
import CartItemValidationBadge from '../../src/components/cart/CartItemValidationBadge';

export default function GioHangPage() {
  const { items, removeItem, updateQuantity, updateItemSize, updateItemColor, clearCart, total, itemCount, loading, addItem, refreshVariantData } = useCart();
  const [changingSizeFor, setChangingSizeFor] = useState<string | null>(null);
  const [availableSizes, setAvailableSizes] = useState<{[key: string]: any[]}>({});
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const handleQuantityUpdate = async (itemId: string, newQuantity: number) => {
    try {
      await updateQuantity(itemId, newQuantity);
    } catch (error) {
      console.error('Error updating quantity:', error);
    }
  };

  const handleRemoveItem = async (itemId: string) => {
    try {
      await removeItem(itemId);
    } catch (error) {
      console.error('Error removing item:', error);
    }
  };

  const handleClearCart = async () => {
    if (window.confirm('Bạn có chắc chắn muốn xóa tất cả sản phẩm trong giỏ hàng?')) {
      try {
        await clearCart();
      } catch (error) {
        console.error('Error clearing cart:', error);
      }
    }
  };

  // Fetch available sizes for a product
  const fetchProductSizes = async (productId: string) => {
    try {
      const response = await fetch(`/api/products/by-id/${productId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.variants) {
          setAvailableSizes(prev => ({
            ...prev,
            [productId]: result.data.variants
          }));
        }
      }
    } catch (error) {
      console.error('Error fetching product sizes:', error);
    }
  };

  // Handle size change
  const handleSizeChange = async (item: any, newVariantId: string) => {
    try {
      // Remove current item
      await removeItem(item.productId);

      // Add item with new size
      await addItem(item.productId, item.quantity, newVariantId);

      setChangingSizeFor(null);
    } catch (error) {
      console.error('Error changing size:', error);
    }
  };

  // Get size display name
  const getSizeDisplayName = (variant: any, variantId?: string) => {
    if (!variant && !variantId) return null;

    if (variant) {
      const attributes = variant.attributes || {};
      return attributes.size || variant.name;
    }

    // If no variant object but we have variantId, return null to trigger data fetch
    return null;
  };

  // Simple validation function for manual calls
  const validateCartItems = () => {
    const errors: string[] = [];

    items.forEach((item) => {
      const product = item.product;

      // Check if product has sizes but no size selected
      if (product.sizes && product.sizes.length > 0) {
        if (!item.variantId || !product.sizes.includes(item.variantId)) {
          errors.push(`Sản phẩm "${product.name}" chưa chọn kích thước hợp lệ`);
        }
      }

      // Check if product has colors but no color selected
      if (product.colors && product.colors.length > 0) {
        if (!item.selectedColor || !product.colors.includes(item.selectedColor)) {
          errors.push(`Sản phẩm "${product.name}" chưa chọn màu sắc hợp lệ`);
        }
      }
    });

    return errors.length === 0;
  };

  // Check if cart is ready for checkout
  const isCartReadyForCheckout = useMemo(() => {
    if (items.length === 0) return false;

    // Calculate validation without calling validateCartItems to avoid loops
    const hasErrors = items.some(item => {
      const product = item.product;

      // Check if product has sizes but no size selected
      if (product.sizes && product.sizes.length > 0) {
        if (!item.variantId || !product.sizes.includes(item.variantId)) {
          return true;
        }
      }

      // Check if product has colors but no color selected
      if (product.colors && product.colors.length > 0) {
        if (!item.selectedColor || !product.colors.includes(item.selectedColor)) {
          return true;
        }
      }

      return false;
    });

    return !hasErrors;
  }, [items]);

  // Handle size change for cart item
  const handleItemSizeChange = async (itemId: string, newSize: string) => {
    try {
      await updateItemSize(itemId, newSize);
      // Validation will be handled by useEffect
    } catch (error) {
      console.error('Error updating item size:', error);
    }
  };

  // Handle color change for cart item
  const handleItemColorChange = async (itemId: string, newColor: string) => {
    try {
      await updateItemColor(itemId, newColor);
      // Validation will be handled by useEffect
    } catch (error) {
      console.error('Error updating item color:', error);
    }
  };

  const getProductImage = (images: string | string[]) => {
    try {
      const imageArray = Array.isArray(images)
        ? images
        : (images ? JSON.parse(images) : []);
      return imageArray.length > 0 ? imageArray[0] : '/images/placeholder.svg';
    } catch {
      return '/images/placeholder.svg';
    }
  };

  const shippingThreshold = 500000; // 500,000 VND
  const shippingCost = total >= shippingThreshold ? 0 : 30000;
  const finalTotal = total + shippingCost;

  // Refresh variant data on component mount to ensure size info is available
  useEffect(() => {
    if (items.length > 0) {
      // Check if any items have variantId but no variant data
      const needsRefresh = items.some(item => item.variantId && !item.variant);
      if (needsRefresh) {
        refreshVariantData();
      }
    }
  }, [items.length]); // Only depend on items.length

  // Separate useEffect for validation to avoid infinite loops
  useEffect(() => {
    if (items.length > 0) {
      const errors: string[] = [];

      items.forEach((item) => {
        const product = item.product;

        // Check if product has sizes but no size selected
        if (product.sizes && product.sizes.length > 0) {
          if (!item.variantId || !product.sizes.includes(item.variantId)) {
            errors.push(`Sản phẩm "${product.name}" chưa chọn kích thước hợp lệ`);
          }
        }

        // Check if product has colors but no color selected
        if (product.colors && product.colors.length > 0) {
          if (!item.selectedColor || !product.colors.includes(item.selectedColor)) {
            errors.push(`Sản phẩm "${product.name}" chưa chọn màu sắc hợp lệ`);
          }
        }
      });

      setValidationErrors(errors);
    } else {
      setValidationErrors([]);
    }
  }, [items]); // Only depend on items



  return (
    <main className="min-h-screen bg-gray-50">
      {/* Clean Header Section */}
      <section className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors text-sm"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Tiếp tục mua sắm
            </Link>
            <div className="flex items-center gap-3">
              <div className="bg-blue-50 text-blue-700 px-3 py-1.5 rounded-lg text-sm font-medium border border-blue-200">
                <ShoppingBag className="w-4 h-4 inline mr-2" />
                {itemCount} sản phẩm
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3 mb-2">
            <ShoppingBag className="w-7 h-7 text-gray-700" />
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
              Giỏ hàng
            </h1>
          </div>
          <p className="text-gray-600 text-sm md:text-base">
            {itemCount > 0
              ? `Bạn có ${itemCount} sản phẩm trong giỏ hàng tại Thinluong`
              : 'Giỏ hàng của bạn đang trống - Hãy khám phá các sản phẩm tại Thinluong!'
            }
          </p>
        </div>
      </section>

      {/* Cart Content */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          {items.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-16 md:py-24"
            >
              <div className="w-20 h-20 md:w-24 md:h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-6">
                <ShoppingBag className="w-10 h-10 md:w-12 md:h-12 text-gray-400" />
              </div>

              <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-3">
                Giỏ hàng trống
              </h2>
              <p className="text-gray-600 mb-8 text-sm md:text-base max-w-md mx-auto">
                Chưa có sản phẩm nào trong giỏ hàng. Hãy khám phá các sản phẩm tuyệt vời tại Thinluong!
              </p>

              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link
                  href="/"
                  className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  <ShoppingBag className="w-4 h-4 mr-2" />
                  Khám phá sản phẩm
                </Link>
                <Link
                  href="/danh-muc"
                  className="inline-flex items-center justify-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                >
                  Xem danh mục
                </Link>
              </div>
            </motion.div>
          ) : (
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              {/* Cart Items */}
              <div className="xl:col-span-2">
                <div className="bg-white border border-gray-200 rounded-xl overflow-hidden">
                  <div className="p-4 md:p-6 border-b border-gray-200 bg-gray-50">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                      <h2 className="text-lg md:text-xl font-bold text-gray-900">
                        Sản phẩm ({itemCount})
                      </h2>
                      {items.length > 0 && (
                        <button
                          onClick={handleClearCart}
                          className="text-red-600 hover:text-red-700 text-sm font-medium transition-colors self-start sm:self-auto"
                          disabled={loading}
                        >
                          Xóa tất cả
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="divide-y divide-gray-200">
                    {items.map((item, index) => (
                      <motion.div
                        key={item.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="p-4 md:p-6 hover:bg-gray-50 transition-colors"
                      >
                        {/* Mobile Layout (< md) */}
                        <div className="md:hidden">
                          <div className="flex gap-3 mb-4">
                            <div className="flex-shrink-0">
                              <Image
                                src={getProductImage(item.product.images)}
                                alt={item.product.name}
                                width={80}
                                height={80}
                                className="rounded-lg object-cover border border-gray-200"
                              />
                            </div>

                            <div className="flex-1 min-w-0">
                              <Link
                                href={`/san-pham/${item.product.slug}`}
                                className="block text-sm font-semibold text-gray-900 hover:text-blue-600 transition-colors mb-1 line-clamp-2"
                              >
                                {item.product.name}
                              </Link>
                              <div className="flex items-center justify-between mb-2">
                                <p className="text-xs text-gray-500">
                                  {item.product.category?.name}
                                </p>
                                <CartItemValidationBadge
                                  hasRequiredSize={!!(item.product.sizes && item.product.sizes.length > 0)}
                                  hasRequiredColor={!!(item.product.colors && item.product.colors.length > 0)}
                                  selectedSize={item.variantId}
                                  selectedColor={item.selectedColor}
                                  compact={true}
                                />
                              </div>

                              {/* Size & Color Display Mobile */}
                              <div className="mb-3">
                                <ProductAttributeTags
                                  sizes={item.product.sizes || []}
                                  colors={item.product.colors || []}
                                  selectedSize={item.variantId || undefined}
                                  selectedColor={item.selectedColor || undefined}
                                  onSizeChange={(size: string) => handleItemSizeChange(item.id, size)}
                                  onColorChange={(color: string) => handleItemColorChange(item.id, color)}
                                  disabled={loading}
                                  showLabels={false}
                                  showValidationErrors={true}
                                />
                              </div>

                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="text-base font-bold text-gray-900">
                                    {(item.product.price || 0).toLocaleString('vi-VN')}đ
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    Tổng: {((item.product.price || 0) * item.quantity).toLocaleString('vi-VN')}đ
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Mobile Quantity Controls */}
                          <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                            <div className="flex items-center bg-gray-100 border border-gray-200 rounded-lg">
                              <button
                                onClick={() => handleQuantityUpdate(item.id, item.quantity - 1)}
                                className="w-9 h-9 flex items-center justify-center hover:bg-gray-200 transition-colors rounded-l-lg"
                                disabled={loading || item.quantity <= 1}
                              >
                                <Minus className="w-4 h-4 text-gray-600" />
                              </button>
                              <span className="px-3 py-2 min-w-[50px] text-center font-medium text-sm">
                                {item.quantity}
                              </span>
                              <button
                                onClick={() => handleQuantityUpdate(item.id, item.quantity + 1)}
                                className="w-9 h-9 flex items-center justify-center hover:bg-gray-200 transition-colors rounded-r-lg"
                                disabled={loading}
                              >
                                <Plus className="w-4 h-4 text-gray-600" />
                              </button>
                            </div>

                            <button
                              onClick={() => handleRemoveItem(item.id)}
                              className="w-9 h-9 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors border border-red-200"
                              disabled={loading}
                              title="Xóa khỏi giỏ hàng"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>

                        {/* Desktop Layout (>= md) */}
                        <div className="hidden md:flex md:items-center md:space-x-6">
                          <div className="flex-shrink-0">
                            <div className="relative">
                              <Image
                                src={getProductImage(item.product.images)}
                                alt={item.product.name}
                                width={120}
                                height={120}
                                className="rounded-lg object-cover border border-gray-200"
                              />
                            </div>
                          </div>

                          <div className="flex-1 min-w-0">
                            <Link
                              href={`/san-pham/${item.product.slug}`}
                              className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors block mb-2"
                            >
                              {item.product.name}
                            </Link>
                            <div className="space-y-3 mb-4">
                              <div className="flex items-center justify-between">
                                <p className="text-sm text-gray-500 font-medium">
                                  {item.product.category?.name}
                                </p>
                                <CartItemValidationBadge
                                  hasRequiredSize={!!(item.product.sizes && item.product.sizes.length > 0)}
                                  hasRequiredColor={!!(item.product.colors && item.product.colors.length > 0)}
                                  selectedSize={item.variantId}
                                  selectedColor={item.selectedColor}
                                  compact={false}
                                />
                              </div>

                              {/* Size & Color Display Desktop */}
                              <div className="mb-4">
                                <ProductAttributeTags
                                  sizes={item.product.sizes || []}
                                  colors={item.product.colors || []}
                                  selectedSize={item.variantId || undefined}
                                  selectedColor={item.selectedColor || undefined}
                                  onSizeChange={(size: string) => handleItemSizeChange(item.id, size)}
                                  onColorChange={(color: string) => handleItemColorChange(item.id, color)}
                                  disabled={loading}
                                  showLabels={true}
                                  showValidationErrors={true}
                                />
                              </div>
                            </div>
                            <div className="flex items-center justify-between mb-4">
                              <div>
                                <p className="text-xl font-bold text-gray-900">
                                  {(item.product.price || 0).toLocaleString('vi-VN')}đ
                                </p>
                                <p className="text-sm text-gray-500">
                                  Tổng: {((item.product.price || 0) * item.quantity).toLocaleString('vi-VN')}đ
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center space-x-4">
                            {/* Desktop Quantity Controls */}
                            <div className="flex items-center bg-gray-100 border border-gray-200 rounded-lg">
                              <button
                                onClick={() => handleQuantityUpdate(item.id, item.quantity - 1)}
                                className="w-10 h-10 flex items-center justify-center hover:bg-gray-200 transition-colors rounded-l-lg"
                                disabled={loading || item.quantity <= 1}
                              >
                                <Minus className="w-4 h-4 text-gray-600" />
                              </button>
                              <span className="px-4 py-2 min-w-[60px] text-center font-medium">
                                {item.quantity}
                              </span>
                              <button
                                onClick={() => handleQuantityUpdate(item.id, item.quantity + 1)}
                                className="w-10 h-10 flex items-center justify-center hover:bg-gray-200 transition-colors rounded-r-lg"
                                disabled={loading}
                              >
                                <Plus className="w-4 h-4 text-gray-600" />
                              </button>
                            </div>

                            {/* Remove Button */}
                            <button
                              onClick={() => handleRemoveItem(item.id)}
                              className="w-10 h-10 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors border border-red-200"
                              disabled={loading}
                              title="Xóa khỏi giỏ hàng"
                            >
                              <Trash2 className="w-5 h-5" />
                            </button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Order Summary */}
              <div className="xl:col-span-1">
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6 }}
                  className="bg-white border border-gray-200 rounded-xl p-4 md:p-6 sticky top-8"
                >
                  <div className="flex items-center mb-6">
                    <Package className="w-6 h-6 text-gray-700 mr-3" />
                    <h3 className="text-lg md:text-xl font-bold text-gray-900">
                      Tóm tắt đơn hàng
                    </h3>
                  </div>

                  {/* Order Items Summary */}
                  <div className="mb-6 pb-4 border-b border-gray-200">
                    <h4 className="text-sm font-semibold text-gray-700 mb-3">
                      Sản phẩm đã chọn:
                    </h4>
                    <div className="space-y-3">
                      {items.map((item) => (
                        <div key={item.id} className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                          <div className="flex justify-between items-start">
                            <div className="flex-1 min-w-0 pr-2">
                              <span className="text-gray-900 font-medium text-sm block line-clamp-2">
                                {item.product.name}
                              </span>
                              <div className="text-xs text-gray-600 mt-1 space-y-1">
                                {item.variantId && (
                                  <div>Size: <span className="font-medium text-blue-600">{item.variantId}</span></div>
                                )}
                                {item.selectedColor && (
                                  <div>Màu: <span className="font-medium text-green-600">{item.selectedColor}</span></div>
                                )}
                              </div>
                              <span className="text-gray-500 text-xs block mt-1">
                                Số lượng: {item.quantity}
                              </span>
                            </div>
                            <span className="font-bold text-gray-900 text-sm whitespace-nowrap">
                              {((item.product.price || 0) * item.quantity).toLocaleString('vi-VN')}đ
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-4 mb-6">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 font-medium">Tạm tính:</span>
                      <span className="font-bold text-lg">
                        {total.toLocaleString('vi-VN')}đ
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 font-medium">Phí vận chuyển:</span>
                      <span className="font-bold text-lg">
                        {shippingCost === 0 ? (
                          <span className="text-green-600">Miễn phí</span>
                        ) : (
                          `${shippingCost.toLocaleString('vi-VN')}đ`
                        )}
                      </span>
                    </div>

                    {total < shippingThreshold && (
                      <div className="text-sm text-blue-700 bg-blue-50 p-3 rounded-lg border border-blue-200">
                        Mua thêm <span className="font-bold">{(shippingThreshold - total).toLocaleString('vi-VN')}đ</span> để được miễn phí vận chuyển!
                      </div>
                    )}

                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-xl font-bold text-gray-900">Tổng cộng:</span>
                        <span className="text-2xl font-bold text-gray-900">
                          {finalTotal.toLocaleString('vi-VN')}đ
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Validation Warnings */}
                  {validationErrors.length > 0 && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-start">
                        <AlertTriangle className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                          <h4 className="text-sm font-semibold text-red-800 mb-2">
                            Cần hoàn thiện thông tin sản phẩm
                          </h4>
                          <ul className="text-sm text-red-700 space-y-1">
                            {validationErrors.map((error, index) => (
                              <li key={index} className="flex items-start">
                                <span className="text-red-500 mr-2">•</span>
                                <span>{error}</span>
                              </li>
                            ))}
                          </ul>
                          <p className="text-xs text-red-600 mt-2">
                            Vui lòng chọn size và màu sắc cho tất cả sản phẩm để tiếp tục thanh toán
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Success message when cart is ready */}
                  {validationErrors.length === 0 && items.length > 0 && (
                    <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-sm font-medium text-green-800">
                          Giỏ hàng đã sẵn sàng để thanh toán!
                        </span>
                      </div>
                    </div>
                  )}

                  <div className="space-y-4">
                    <button
                      onClick={() => {
                        if (!isCartReadyForCheckout) {
                          // Show detailed validation errors
                          const errorMessage = validationErrors.length > 0
                            ? `Vui lòng hoàn thiện thông tin:\n\n${validationErrors.join('\n')}`
                            : 'Giỏ hàng trống hoặc có lỗi. Vui lòng kiểm tra lại.';

                          alert(errorMessage);
                          return;
                        }

                        // If validation passes, navigate to checkout
                        try {
                          // Use Next.js router for better navigation
                          window.location.href = '/thanh-toan';
                        } catch (error) {
                          console.error('Navigation error:', error);
                          // Fallback navigation
                          window.open('/thanh-toan', '_self');
                        }
                      }}
                      className={`w-full py-3 px-6 rounded-lg font-semibold transition-all text-center block ${
                        !isCartReadyForCheckout
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-blue-600 text-white hover:bg-blue-700 cursor-pointer'
                      }`}
                      disabled={!isCartReadyForCheckout}
                    >
                      {!isCartReadyForCheckout ? (
                        <div className="flex items-center justify-center">
                          <AlertTriangle className="w-4 h-4 mr-2" />
                          Hoàn thiện thông tin
                        </div>
                      ) : (
                        'Tiến hành thanh toán'
                      )}
                    </button>

                    <Link
                      href="/"
                      className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 px-6 rounded-lg font-semibold text-center block transition-all"
                    >
                      Tiếp tục mua sắm
                    </Link>
                  </div>
                  
                  {/* Security & Support */}
                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <h4 className="text-sm font-semibold text-gray-700 mb-4">
                      Cam kết của Thinluong Store
                    </h4>
                    <div className="space-y-3 text-sm">
                      <div className="flex items-center p-3 bg-green-50 rounded-lg border border-green-200">
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                          <span className="text-white text-xs">🔒</span>
                        </div>
                        <span className="text-gray-700 font-medium">Thanh toán an toàn 100%</span>
                      </div>
                      <div className="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                          <span className="text-white text-xs">🔄</span>
                        </div>
                        <span className="text-gray-700 font-medium">Đổi trả miễn phí 7 ngày</span>
                      </div>
                      <div className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                        <div className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center mr-3">
                          <span className="text-white text-xs">💬</span>
                        </div>
                        <span className="text-gray-700 font-medium">Hỗ trợ Thinluong 24/7</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          )}
        </div>
      </section>
    </main>
  );
} 