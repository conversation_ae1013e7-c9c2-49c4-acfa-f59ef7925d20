@tailwind base;
@tailwind components;
@tailwind utilities;

/* Arial font for entire website */
* {
  font-family: Arial, Helvetica, sans-serif !important;
}

body {
  font-family: Arial, Helvetica, sans-serif !important;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

:root {
  --background: #ffffff;
  --foreground: #000000;
  --primary: #f97316; /* orange-500 */
  --ast-global-color-0: #000000;
  --ast-global-color-1: #000000;
  --ast-global-color-2: #000000;
  --ast-global-color-3: #1f2937; /* gray-800 */
  --ast-global-color-4: #ffffff;
  --ast-global-color-5: #ffffff;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), 'Arial', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.65em;
}

/* Font utility classes */
.font-inter {
  font-family: var(--font-inter), 'Arial', sans-serif;
}

.font-jakarta {
  font-family: var(--font-jakarta), sans-serif;
}

/* Admin panel styling */
.admin-layout {
  font-family: var(--font-inter), 'Arial', sans-serif;
}

.admin-heading {
  font-family: var(--font-inter), 'Arial', sans-serif;
  font-weight: 700;
  color: var(--ast-global-color-3);
}

/* Custom scrollbar for admin panel */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563eb, #7c3aed);
}

/* Heading styles - using Inter for all headings */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-inter), 'Arial', sans-serif;
  font-weight: 700;
  line-height: 1.2em;
  color: var(--ast-global-color-3);
}

/* Main heading - responsive sizes */
.heading-main {
  font-size: 2.25rem; /* text-4xl */
  font-weight: 700;
  color: #1f2937; /* text-gray-800 */
}

@media (min-width: 640px) {
  .heading-main {
    font-size: 3rem; /* sm:text-5xl */
  }
}

@media (min-width: 768px) {
  .heading-main {
    font-size: 3.75rem; /* md:text-6xl */
  }
}

/* Subheading - responsive sizes */
.subheading {
  font-size: 1.5rem; /* text-2xl */
  font-weight: 500;
  color: #1f2937; /* text-gray-800 */
}

@media (min-width: 640px) {
  .subheading {
    font-size: 1.875rem; /* sm:text-3xl */
  }
}

@media (min-width: 768px) {
  .subheading {
    font-size: 2.25rem; /* md:text-4xl */
  }
}

/* Default heading sizes for backward compatibility */
h1 {
  font-size: 2.25rem; /* text-4xl */
  font-weight: 700;
  color: #111827; /* text-gray-900 */
}

h2 {
  font-size: 1.875rem; /* text-3xl */
  font-weight: 700;
  color: #1f2937; /* text-gray-800 */
}

h3 {
  font-size: 1.5rem; /* text-2xl */
  font-weight: 600;
  color: #1f2937; /* text-gray-800 */
}

h4 {
  font-size: 1.25rem; /* text-xl */
  font-weight: 600;
  color: #374151; /* text-gray-700 */
}

h5 {
  font-size: 1.125rem; /* text-lg */
  font-weight: 600;
  color: #374151; /* text-gray-700 */
}

h6 {
  font-size: 1rem; /* text-base */
  font-weight: 600;
  color: #4b5563; /* text-gray-600 */
}

/* Responsive heading adjustments */
@media (max-width: 768px) {
  h1 {
    font-size: 1.875rem; /* text-3xl on mobile */
  }
  h2 {
    font-size: 1.5rem; /* text-2xl on mobile */
  }
  h3 {
    font-size: 1.25rem; /* text-xl on mobile */
  }
}

/* Highlight text utilities */
.text-highlight {
  color: #f97316; /* text-orange-500 */
}

.text-primary-highlight {
  color: var(--primary);
}

/* Utility classes for consistent heading styles */
.heading-xl {
  font-size: 2.25rem; /* text-4xl */
  font-weight: 700;
  color: #111827; /* text-gray-900 */
  line-height: 1.2;
}

@media (min-width: 640px) {
  .heading-xl {
    font-size: 3rem; /* sm:text-5xl */
  }
}

@media (min-width: 768px) {
  .heading-xl {
    font-size: 3.75rem; /* md:text-6xl */
  }
}

.heading-lg {
  font-size: 1.5rem; /* text-2xl */
  font-weight: 500;
  color: #1f2937; /* text-gray-800 */
  line-height: 1.3;
}

@media (min-width: 640px) {
  .heading-lg {
    font-size: 1.875rem; /* sm:text-3xl */
  }
}

@media (min-width: 768px) {
  .heading-lg {
    font-size: 2.25rem; /* md:text-4xl */
  }
}

.heading-md {
  font-size: 1.875rem; /* text-3xl */
  font-weight: 700;
  color: #1f2937; /* text-gray-800 */
  line-height: 1.25;
}

.heading-sm {
  font-size: 1.5rem; /* text-2xl */
  font-weight: 700;
  color: #1f2937; /* text-gray-800 */
  line-height: 1.25;
}

/* Utility classes for consistent heading styles */
.heading-section {
  @apply text-3xl md:text-4xl font-bold text-gray-900 mb-6;
}

.text-gradient {
  @apply bg-gradient-to-r from-orange-500 to-red-600 bg-clip-text text-transparent;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Backdrop blur utilities */
.backdrop-blur-xs {
  backdrop-filter: blur(2px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(8px);
}

/* Glass morphism effect */
.glass {
  @apply bg-white/10 backdrop-blur-md border border-white/20;
}

.glass-dark {
  @apply bg-black/10 backdrop-blur-md border border-black/20;
}

/* Scrollbar utilities */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* FORCE HIDE BREADCRUMB ON MOBILE/TABLET - IMPORTANT! */
@media (max-width: 1023px) {
  .breadcrumb-container {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
  }

  /* Target any breadcrumb with these classes */
  .hidden.lg\:block {
    display: none !important;
  }

  /* Force hide any breadcrumb navigation */
  nav[aria-label="Breadcrumb"] {
    display: none !important;
  }
}

@media (min-width: 1024px) {
  .breadcrumb-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
  }
}

/* Mobile Product Card Touch Improvements */
@media (max-width: 767px) {
  .elegant-product-card {
    /* Add touch feedback */
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    cursor: pointer;
  }

  .elegant-product-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  /* Ensure mobile clickable area works */
  .elegant-product-card > a[aria-label*="View"] {
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
}
