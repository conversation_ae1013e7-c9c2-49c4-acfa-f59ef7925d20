'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  ShoppingCart, 
  Search, 
  Eye, 
  Heart, 
  Package, 
  CreditCard, 
  Truck, 
  CheckCircle, 
  Phone, 
  Mail, 
  Star,
  Shield,
  Clock,
  AlertCircle,
  Filter,
  Gift
} from 'lucide-react';
import Link from 'next/link';

export default function HuongDanMuaHangPage() {
  const shoppingSteps = [
    {
      step: "01",
      icon: <Search className="w-8 h-8" />,
      title: "Tìm kiếm sản phẩm",
      description: "Sử dụng thanh tìm kiếm hoặc duyệt theo danh mục để tìm sản phẩm yêu thích",
      details: [
        "Gõ từ khóa vào ô tìm kiếm ở đầu trang",
        "Chọn danh mục sản phẩm từ menu",
        "Sử dụng bộ lọc theo giá, size, màu sắc",
        "Xem sản phẩm bán chạy và được yêu thích"
      ],
      tips: "💡 Sử dụng từ khóa cụ thể như 'áo sơ mi trắng' để tìm chính xác hơn"
    },
    {
      step: "02",
      icon: <Eye className="w-8 h-8" />,
      title: "Xem chi tiết sản phẩm",
      description: "Đọc thông tin chi tiết, xem hình ảnh và đánh giá của khách hàng khác",
      details: [
        "Xem hình ảnh sản phẩm từ nhiều góc độ",
        "Đọc mô tả chi tiết về chất liệu, thiết kế",
        "Kiểm tra bảng size và hướng dẫn chọn size",
        "Đọc đánh giá và nhận xét từ khách hàng"
      ],
      tips: "💡 Lưu sản phẩm vào danh sách yêu thích để so sánh và mua sau"
    },
    {
      step: "03",
      icon: <Package className="w-8 h-8" />,
      title: "Chọn tùy chọn sản phẩm",
      description: "Chọn size, màu sắc và số lượng phù hợp trước khi thêm vào giỏ hàng",
      details: [
        "Chọn size dựa theo bảng hướng dẫn",
        "Chọn màu sắc yêu thích (nếu có)",
        "Điều chỉnh số lượng mong muốn",
        "Kiểm tra tình trạng còn hàng"
      ],
      tips: "💡 Tham khảo bảng size kỹ để tránh đổi trả không cần thiết"
    },
    {
      step: "04",
      icon: <ShoppingCart className="w-8 h-8" />,
      title: "Thêm vào giỏ hàng",
      description: "Thêm sản phẩm vào giỏ hàng và tiếp tục mua sắm hoặc thanh toán",
      details: [
        "Nhấn nút 'Thêm vào giỏ hàng'",
        "Kiểm tra giỏ hàng ở góc phải màn hình",
        "Tiếp tục mua sắm hoặc thanh toán ngay",
        "Áp dụng mã giảm giá nếu có"
      ],
      tips: "💡 Mua từ 500.000đ để được miễn phí vận chuyển"
    },
    {
      step: "05",
      icon: <CreditCard className="w-8 h-8" />,
      title: "Thanh toán",
      description: "Điền thông tin giao hàng và thanh toán khi nhận hàng (COD)",
      details: [
        "Nhập đầy đủ thông tin giao hàng",
        "Chọn phương thức 'Thanh toán khi nhận hàng'",
        "Kiểm tra lại đơn hàng và tổng tiền",
        "Xác nhận đặt hàng"
      ],
      tips: "💡 Hiện tại chúng tôi chỉ hỗ trợ thanh toán khi nhận hàng (COD)"
    },
    {
      step: "06",
      icon: <Truck className="w-8 h-8" />,
      title: "Nhận hàng và thanh toán",
      description: "Theo dõi đơn hàng và thanh toán khi nhận hàng tại địa chỉ",
      details: [
        "Nhận email/SMS xác nhận đơn hàng",
        "Theo dõi tình trạng giao hàng",
        "Kiểm tra sản phẩm khi nhận hàng",
        "Thanh toán bằng tiền mặt cho shipper"
      ],
      tips: "💡 Kiểm tra kỹ sản phẩm trước khi thanh toán và nhận hàng"
    }
  ];

  const tips = [
    {
      icon: <Heart className="w-6 h-6" />,
      title: "Sử dụng danh sách yêu thích",
      description: "Lưu sản phẩm yêu thích để mua sau hoặc so sánh giá",
      color: "bg-red-100 text-red-600"
    },
    {
      icon: <Filter className="w-6 h-6" />,
      title: "Sử dụng bộ lọc thông minh",
      description: "Lọc theo giá, size, màu sắc để tìm sản phẩm phù hợp",
      color: "bg-blue-100 text-blue-600"
    },
    {
      icon: <Star className="w-6 h-6" />,
      title: "Đọc đánh giá khách hàng",
      description: "Tham khảo đánh giá và hình ảnh thực tế từ khách hàng",
      color: "bg-yellow-100 text-yellow-600"
    },
    {
      icon: <Gift className="w-6 h-6" />,
      title: "Theo dõi khuyến mãi",
      description: "Đăng ký nhận thông báo để không bỏ lỡ ưu đãi đặc biệt",
      color: "bg-purple-100 text-purple-600"
    }
  ];

  const paymentInfo = {
    title: "Thanh toán khi nhận hàng (COD)",
    description: "Hiện tại chúng tôi chỉ hỗ trợ phương thức thanh toán khi nhận hàng",
    features: [
      {
        icon: <Shield className="w-5 h-5" />,
        text: "An toàn và tiện lợi"
      },
      {
        icon: <CheckCircle className="w-5 h-5" />,
        text: "Kiểm tra hàng trước khi thanh toán"
      },
      {
        icon: <Truck className="w-5 h-5" />,
        text: "Thanh toán trực tiếp cho shipper"
      },
      {
        icon: <Clock className="w-5 h-5" />,
        text: "Không cần thanh toán trước"
      }
    ],
    note: "Các phương thức thanh toán khác như chuyển khoản, ví điện tử sẽ được cập nhật trong thời gian tới."
  };

  const shippingInfo = [
    {
      area: "Nội thành TP.HCM",
      time: "1-2 ngày làm việc",
      fee: "30.000đ (Miễn phí từ 500.000đ)"
    },
    {
      area: "Các tỉnh thành khác",
      time: "2-5 ngày làm việc", 
      fee: "30.000đ (Miễn phí từ 500.000đ)"
    }
  ];

  const faqs = [
    {
      question: "Làm sao để kiểm tra tình trạng đơn hàng?",
      answer: "Bạn sẽ nhận được email xác nhận với mã đơn hàng ngay sau khi đặt hàng thành công. Liên hệ hotline 0932.935.085 hoặc sử dụng tính năng tra cứu đơn hàng trên website để kiểm tra tình trạng giao hàng."
    },
    {
      question: "Tôi có thể đổi size sau khi đặt hàng không?",
      answer: "Bạn có thể liên hệ ngay với chúng tôi sau khi đặt hàng để thay đổi size hoặc hủy đơn hàng. Tuy nhiên, nếu hàng đã được giao cho đơn vị vận chuyển, việc thay đổi sẽ khó khăn hơn."
    },
    {
      question: "Thời gian giao hàng là bao lâu?",
      answer: "Thời gian giao hàng từ 1-5 ngày làm việc tùy theo khu vực. Nội thành TP.HCM thường nhận hàng trong 1-2 ngày, các tỉnh thành khác từ 2-5 ngày làm việc."
    },
    {
      question: "Tôi có thể trả hàng nếu không ưng ý không?",
      answer: "Có, bạn có thể đổi/trả hàng trong vòng 7 ngày kể từ ngày nhận hàng với điều kiện sản phẩm còn nguyên tem mác, chưa qua sử dụng. Chi phí vận chuyển đổi trả sẽ do khách hàng chi trả."
    },
    {
      question: "Có hỗ trợ thanh toán chuyển khoản không?",
      answer: "Hiện tại chúng tôi chỉ hỗ trợ thanh toán khi nhận hàng (COD). Các phương thức thanh toán khác như chuyển khoản ngân hàng, ví điện tử sẽ được cập nhật trong thời gian tới."
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mb-8">
              <ShoppingCart className="w-10 h-10 text-white" />
            </div>
            <div className="inline-flex items-center space-x-2 bg-blue-100 rounded-full px-6 py-2 mb-6">
              <span className="text-sm font-medium text-blue-700">Hướng dẫn chi tiết</span>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Hướng Dẫn Mua Hàng
            </h1>
            <p className="text-lg md:text-xl text-gray-600 leading-relaxed mb-8">
              Hướng dẫn từng bước chi tiết để bạn có trải nghiệm mua sắm tuyệt vời nhất tại Thinluong.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/danh-muc" className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors">
                <ShoppingCart className="w-5 h-5 mr-2" />
                Bắt đầu mua sắm
              </Link>
              <Link href="/tra-cuu-don-hang" className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors">
                <Package className="w-5 h-5 mr-2" />
                Tra cứu đơn hàng
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Shopping Steps */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              6 Bước Mua Hàng Đơn Giản
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Quy trình mua hàng được thiết kế đơn giản và thuận tiện nhất cho khách hàng
            </p>
          </div>

          <div className="max-w-7xl mx-auto">
            {shoppingSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className={`flex flex-col lg:flex-row items-center gap-8 mb-20 ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                }`}
              >
                {/* Step Content */}
                <div className="flex-1 max-w-2xl">
                  <div className="flex items-center mb-6">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-6 shadow-lg">
                      {step.step}
                    </div>
                    <div>
                      <h3 className="text-2xl md:text-3xl font-bold text-gray-900">{step.title}</h3>
                      <p className="text-gray-600 mt-2">{step.description}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    {step.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="flex items-center p-4 bg-gray-50 rounded-lg">
                        <div className="w-3 h-3 bg-blue-500 rounded-full mr-3 flex-shrink-0" />
                        <span className="text-gray-700">{detail}</span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
                    <p className="text-blue-700 text-sm font-medium">{step.tips}</p>
                  </div>
                </div>

                {/* Step Icon */}
                <div className="flex-shrink-0">
                  <div className="w-40 h-40 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center shadow-xl">
                    <div className="text-blue-600">
                      {step.icon}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Payment Information */}
      <section className="py-16 bg-gradient-to-br from-green-50 to-emerald-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Phương Thức Thanh Toán
              </h2>
              <p className="text-gray-600 text-lg">
                Thanh toán an toàn và tiện lợi với phương thức COD
              </p>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl shadow-xl p-8 border border-green-100"
            >
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mr-6">
                  <Truck className="w-8 h-8 text-green-600" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">{paymentInfo.title}</h3>
                  <p className="text-gray-600 mt-1">{paymentInfo.description}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {paymentInfo.features.map((feature, index) => (
                  <div key={index} className="flex items-center p-4 bg-green-50 rounded-lg">
                    <div className="text-green-600 mr-3">
                      {feature.icon}
                    </div>
                    <span className="text-gray-700 font-medium">{feature.text}</span>
                  </div>
                ))}
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertCircle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                  <p className="text-blue-700 text-sm">{paymentInfo.note}</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Shipping Information */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Thông Tin Vận Chuyển
              </h2>
              <p className="text-gray-600 text-lg">
                Giao hàng nhanh chóng trên toàn quốc
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {shippingInfo.map((info, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-gray-50 rounded-lg p-6 border border-gray-200"
                >
                  <h3 className="font-bold text-gray-900 text-lg mb-4">{info.area}</h3>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <Clock className="w-5 h-5 text-gray-500 mr-3" />
                      <span className="text-gray-700">{info.time}</span>
                    </div>
                    <div className="flex items-center">
                      <Truck className="w-5 h-5 text-gray-500 mr-3" />
                      <span className="text-gray-700">{info.fee}</span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <div className="flex items-center mb-3">
                <Gift className="w-6 h-6 text-yellow-600 mr-3" />
                <h3 className="font-bold text-yellow-800">Ưu đãi vận chuyển</h3>
              </div>
              <p className="text-yellow-700">
                🎉 <strong>Miễn phí vận chuyển</strong> cho đơn hàng từ 500.000đ trở lên toàn quốc!
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Shopping Tips */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Mẹo Mua Sắm Thông Minh
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Những mẹo hữu ích giúp bạn có trải nghiệm mua sắm tốt nhất
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {tips.map((tip, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100"
              >
                <div className={`w-16 h-16 ${tip.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  {tip.icon}
                </div>
                <h3 className="font-bold text-gray-900 mb-3 text-center text-lg">{tip.title}</h3>
                <p className="text-gray-600 text-sm text-center leading-relaxed">{tip.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Câu Hỏi Thường Gặp
              </h2>
              <p className="text-gray-600 text-lg">
                Giải đáp những thắc mắc phổ biến khi mua sắm
              </p>
            </div>

            <div className="space-y-6">
              {faqs.map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-gray-50 p-6 rounded-xl border border-gray-200"
                >
                  <h3 className="font-bold text-gray-900 mb-3 text-lg flex items-start">
                    <span className="text-blue-600 mr-2">Q:</span>
                    {faq.question}
                  </h3>
                  <p className="text-gray-700 leading-relaxed pl-6">
                    <span className="text-green-600 font-semibold">A:</span> {faq.answer}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-16 bg-gradient-to-br from-blue-50 to-indigo-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Cần Hỗ Trợ Mua Hàng?
            </h2>
            <p className="text-gray-600 mb-12 text-lg">
              Đội ngũ tư vấn của chúng tôi luôn sẵn sàng hỗ trợ bạn 24/7
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-8 rounded-xl shadow-lg border border-gray-100"
              >
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Phone className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="font-bold text-gray-900 mb-3 text-xl">Hotline Tư Vấn</h3>
                <p className="text-gray-600 mb-6">Gọi ngay để được tư vấn miễn phí</p>
                <a href="tel:0932935085" className="inline-flex items-center text-green-600 font-bold text-2xl hover:text-green-700 transition-colors">
                  <Phone className="w-5 h-5 mr-2" />
                  0932.935.085
                </a>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-8 rounded-xl shadow-lg border border-gray-100"
              >
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Mail className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="font-bold text-gray-900 mb-3 text-xl">Email Hỗ Trợ</h3>
                <p className="text-gray-600 mb-6">Gửi câu hỏi và nhận phản hồi nhanh</p>
                <a href="mailto:<EMAIL>" className="inline-flex items-center text-blue-600 font-bold text-xl hover:text-blue-700 transition-colors">
                  <Mail className="w-5 h-5 mr-2" />
                  <EMAIL>
                </a>
              </motion.div>
            </div>

            <div className="mt-12 flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/lien-he" className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors">
                <Mail className="w-5 h-5 mr-2" />
                Liên hệ trực tiếp
              </Link>
              <Link href="/chinh-sach-doi-tra" className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors">
                <Shield className="w-5 h-5 mr-2" />
                Chính sách đổi trả
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
