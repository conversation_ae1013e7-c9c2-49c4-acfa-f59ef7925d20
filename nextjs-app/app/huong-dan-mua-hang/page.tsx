'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ShoppingCart, Search, Eye, Heart, Package, CreditCard, Truck, CheckCircle, Phone, Mail } from 'lucide-react';

export default function HuongDanMuaHangPage() {
  const shoppingSteps = [
    {
      step: "01",
      icon: <Search className="w-8 h-8" />,
      title: "Tìm kiếm sản phẩm",
      description: "Sử dụng thanh tìm kiếm hoặc duyệt theo danh mục để tìm sản phẩm yêu thích",
      details: [
        "Gõ từ khóa vào ô tìm kiếm",
        "Chọn danh mục sản phẩm",
        "Sử dụng bộ lọc để thu hẹp kết quả",
        "Xem sản phẩm bán chạy"
      ]
    },
    {
      step: "02",
      icon: <Eye className="w-8 h-8" />,
      title: "Xem chi tiết sản phẩm",
      description: "<PERSON><PERSON><PERSON> thông tin chi tiết, xem hình ảnh và đánh giá của khách hàng khác",
      details: [
        "Xem hình ảnh sản phẩm từ nhiều góc",
        "Đọc mô tả chi tiết",
        "Kiểm tra bảng size và chất liệu",
        "Đọc đánh giá từ khách hàng"
      ]
    },
    {
      step: "03",
      icon: <Package className="w-8 h-8" />,
      title: "Chọn size và số lượng",
      description: "Chọn size phù hợp và số lượng mong muốn trước khi thêm vào giỏ hàng",
      details: [
        "Chọn size theo bảng hướng dẫn",
        "Điều chỉnh số lượng",
        "Kiểm tra tình trạng còn hàng",
        "Thêm vào yêu thích nếu cần"
      ]
    },
    {
      step: "04",
      icon: <ShoppingCart className="w-8 h-8" />,
      title: "Thêm vào giỏ hàng",
      description: "Thêm sản phẩm vào giỏ hàng và tiếp tục mua sắm hoặc thanh toán",
      details: [
        "Click 'Thêm vào giỏ hàng'",
        "Kiểm tra giỏ hàng",
        "Tiếp tục mua sắm hoặc thanh toán",
        "Áp dụng mã giảm giá nếu có"
      ]
    },
    {
      step: "05",
      icon: <CreditCard className="w-8 h-8" />,
      title: "Thanh toán",
      description: "Điền thông tin giao hàng và chọn phương thức thanh toán phù hợp",
      details: [
        "Nhập thông tin giao hàng",
        "Chọn phương thức thanh toán",
        "Kiểm tra lại đơn hàng",
        "Xác nhận và thanh toán"
      ]
    },
    {
      step: "06",
      icon: <Truck className="w-8 h-8" />,
      title: "Nhận hàng",
      description: "Theo dõi đơn hàng và nhận hàng tại địa chỉ đã cung cấp",
      details: [
        "Nhận email xác nhận đơn hàng",
        "Theo dõi tình trạng giao hàng",
        "Nhận hàng và kiểm tra",
        "Đánh giá sản phẩm"
      ]
    }
  ];

  const tips = [
    {
      icon: <Heart className="w-6 h-6" />,
      title: "Sử dụng danh sách yêu thích",
      description: "Lưu sản phẩm yêu thích để mua sau hoặc so sánh giá"
    },
    {
      icon: <Package className="w-6 h-6" />,
      title: "Kiểm tra bảng size",
      description: "Luôn tham khảo bảng size để chọn size phù hợp nhất"
    },
    {
      icon: <CreditCard className="w-6 h-6" />,
      title: "Theo dõi khuyến mãi",
      description: "Đăng ký nhận thông báo để không bỏ lỡ ưu đãi"
    },
    {
      icon: <CheckCircle className="w-6 h-6" />,
      title: "Đọc đánh giá",
      description: "Tham khảo đánh giá của khách hàng khác trước khi mua"
    }
  ];

  const paymentMethods = [
    {
      name: "Thanh toán khi nhận hàng (COD)",
      description: "Thanh toán bằng tiền mặt khi nhận hàng",
      fee: "Miễn phí",
      time: "Ngay khi nhận hàng"
    },
    {
      name: "Chuyển khoản ngân hàng",
      description: "Chuyển khoản qua tài khoản ngân hàng",
      fee: "Miễn phí",
      time: "Xử lý trong 24h"
    },
    {
      name: "Ví điện tử",
      description: "Thanh toán qua MoMo, ZaloPay, VNPay",
      fee: "Miễn phí",
      time: "Tức thì"
    },
    {
      name: "Thẻ tín dụng/ghi nợ",
      description: "Visa, Mastercard, JCB",
      fee: "Miễn phí",
      time: "Tức thì"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Elegant Hero Section */}
      <section className="bg-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-6">
              <ShoppingCart className="w-8 h-8 text-gray-600" />
            </div>
            <div className="inline-flex items-center space-x-2 bg-gray-100 rounded-full px-6 py-2 mb-6">
              <span className="text-sm font-medium text-gray-700">Hướng dẫn chi tiết</span>
            </div>
            <h1 className="elegant-title text-4xl md:text-5xl lg:text-6xl mb-6">
              Hướng Dẫn Mua Hàng
            </h1>
            <p className="elegant-body text-lg md:text-xl text-gray-600 leading-relaxed">
              Hướng dẫn chi tiết từng bước để bạn có trải nghiệm mua sắm tuyệt vời nhất tại Thinluong.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Shopping Steps */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              6 Bước Mua Hàng Đơn Giản
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Quy trình mua hàng được thiết kế đơn giản và thuận tiện nhất
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            {shoppingSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className={`flex flex-col lg:flex-row items-center gap-8 mb-16 ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                }`}
              >
                {/* Step Content */}
                <div className="flex-1">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                      {step.step}
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">{step.title}</h3>
                  </div>
                  <p className="text-gray-600 mb-6 text-lg">{step.description}</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {step.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-3" />
                        <span className="text-gray-700">{detail}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Step Icon */}
                <div className="flex-shrink-0">
                  <div className="w-32 h-32 bg-green-100 rounded-full flex items-center justify-center">
                    <div className="text-green-600">
                      {step.icon}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Shopping Tips */}
      <section className="py-16 bg-green-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Mẹo Mua Sắm Thông Minh
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Những mẹo hữu ích giúp bạn có trải nghiệm mua sắm tốt nhất
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {tips.map((tip, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow"
              >
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="text-green-600">
                    {tip.icon}
                  </div>
                </div>
                <h3 className="font-bold text-gray-900 mb-2 text-center">{tip.title}</h3>
                <p className="text-gray-600 text-sm text-center">{tip.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Payment Methods */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Phương Thức Thanh Toán
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Chúng tôi hỗ trợ nhiều phương thức thanh toán tiện lợi và an toàn
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {paymentMethods.map((method, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-gray-50 p-6 rounded-lg border hover:shadow-lg transition-shadow"
              >
                <h3 className="font-bold text-gray-900 mb-2 text-lg">{method.name}</h3>
                <p className="text-gray-600 mb-4">{method.description}</p>
                <div className="flex justify-between items-center">
                  <div>
                    <span className="text-sm text-gray-500">Phí: </span>
                    <span className="font-semibold text-green-600">{method.fee}</span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Thời gian: </span>
                    <span className="font-semibold text-gray-900">{method.time}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Câu Hỏi Thường Gặp
              </h2>
            </div>

            <div className="space-y-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="font-bold text-gray-900 mb-2">Làm sao để kiểm tra tình trạng đơn hàng?</h3>
                <p className="text-gray-600">Bạn sẽ nhận được email xác nhận với mã đơn hàng. Liên hệ hotline để kiểm tra tình trạng giao hàng.</p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="font-bold text-gray-900 mb-2">Tôi có thể đổi size sau khi đặt hàng không?</h3>
                <p className="text-gray-600">Bạn có thể liên hệ ngay sau khi đặt hàng để thay đổi size. Sau khi hàng được giao cho đơn vị vận chuyển, việc thay đổi sẽ khó khăn hơn.</p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="font-bold text-gray-900 mb-2">Thời gian giao hàng là bao lâu?</h3>
                <p className="text-gray-600">Thời gian giao hàng từ 2-5 ngày làm việc tùy theo khu vực. Nội thành TP.HCM thường nhận hàng trong 1-2 ngày.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-16 bg-green-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              Cần Hỗ Trợ Mua Hàng?
            </h2>
            <p className="text-gray-600 mb-8">
              Đội ngũ tư vấn của chúng tôi luôn sẵn sàng hỗ trợ bạn
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <Phone className="w-8 h-8 text-green-600 mx-auto mb-4" />
                <h3 className="font-bold text-gray-900 mb-2">Hotline Tư Vấn</h3>
                <p className="text-gray-600 mb-4">Gọi ngay để được tư vấn</p>
                <a href="tel:0932935085" className="text-green-600 font-bold text-lg">
                  0932.935.085
                </a>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <Mail className="w-8 h-8 text-green-600 mx-auto mb-4" />
                <h3 className="font-bold text-gray-900 mb-2">Email Hỗ Trợ</h3>
                <p className="text-gray-600 mb-4">Gửi câu hỏi cho chúng tôi</p>
                <a href="mailto:<EMAIL>" className="text-green-600 font-bold text-lg">
                  <EMAIL>
                </a>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
