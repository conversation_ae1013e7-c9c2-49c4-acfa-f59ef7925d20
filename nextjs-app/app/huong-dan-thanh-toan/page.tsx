'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { CreditCard, Smartphone, Building, Truck, Shield, Clock, CheckCircle, AlertTriangle, Phone, Mail } from 'lucide-react';

export default function HuongDanThanhToanPage() {
  const paymentMethods = [
    {
      icon: <Truck className="w-8 h-8" />,
      title: "Thanh toán khi nhận hàng (COD)",
      description: "Thanh toán bằng tiền mặt khi nhận hàng",
      fee: "Miễn phí",
      time: "Ngay khi nhận hàng",
      color: "bg-green-100 text-green-600",
      steps: [
        "Chọn phương thức COD khi thanh toán",
        "Xác nhận đơn hàng và địa chỉ giao hàng",
        "Nhận hàng từ shipper",
        "Kiểm tra sản phẩm và thanh toán"
      ]
    },
    {
      icon: <Building className="w-8 h-8" />,
      title: "Chuyển khoản ngân hàng",
      description: "Chuyển khoản qua tài khoản ngân hàng",
      fee: "<PERSON>ễn phí",
      time: "Xử lý trong 24h",
      color: "bg-blue-100 text-blue-600",
      steps: [
        "Chọn phương thức chuyển khoản",
        "Nhận thông tin tài khoản ngân hàng",
        "Thực hiện chuyển khoản với nội dung đúng",
        "Gửi ảnh chụp biên lai để xác nhận"
      ]
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Ví điện tử",
      description: "MoMo, ZaloPay, VNPay, ShopeePay",
      fee: "Miễn phí",
      time: "Tức thì",
      color: "bg-purple-100 text-purple-600",
      steps: [
        "Chọn ví điện tử muốn sử dụng",
        "Quét mã QR hoặc nhập số điện thoại",
        "Xác nhận thanh toán trên ứng dụng",
        "Nhận xác nhận thanh toán thành công"
      ]
    },
    {
      icon: <CreditCard className="w-8 h-8" />,
      title: "Thẻ tín dụng/ghi nợ",
      description: "Visa, Mastercard, JCB, ATM nội địa",
      fee: "Miễn phí",
      time: "Tức thì",
      color: "bg-orange-100 text-orange-600",
      steps: [
        "Chọn thanh toán bằng thẻ",
        "Nhập thông tin thẻ một cách an toàn",
        "Xác thực OTP từ ngân hàng",
        "Hoàn tất thanh toán"
      ]
    }
  ];

  const bankInfo = {
    bankName: "Ngân hàng TMCP Á Châu (ACB)",
    accountNumber: "*********",
    accountName: "THINLUONG OFFICIAL",
    branch: "Chi nhánh Gò Vấp"
  };

  const securityFeatures = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Mã hóa SSL 256-bit",
      description: "Bảo vệ thông tin thanh toán với công nghệ mã hóa hàng đầu"
    },
    {
      icon: <CheckCircle className="w-6 h-6" />,
      title: "Xác thực 3D Secure",
      description: "Lớp bảo mật bổ sung cho giao dịch thẻ tín dụng"
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: "Theo dõi real-time",
      description: "Giám sát giao dịch 24/7 để phát hiện bất thường"
    },
    {
      icon: <AlertTriangle className="w-6 h-6" />,
      title: "Không lưu thông tin thẻ",
      description: "Chúng tôi không lưu trữ thông tin thẻ của bạn"
    }
  ];

  const paymentTips = [
    "Luôn kiểm tra kỹ thông tin đơn hàng trước khi thanh toán",
    "Đảm bảo kết nối internet ổn định khi thanh toán online",
    "Không chia sẻ thông tin thẻ hoặc OTP với bất kỳ ai",
    "Kiểm tra email xác nhận sau khi thanh toán thành công",
    "Liên hệ ngay nếu gặp vấn đề trong quá trình thanh toán",
    "Chụp ảnh biên lai chuyển khoản để làm bằng chứng"
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-50 via-white to-purple-50 py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full mb-6">
              <CreditCard className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Hướng Dẫn Thanh Toán
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Hướng dẫn chi tiết các phương thức thanh toán an toàn và tiện lợi tại Thinluong.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Payment Methods */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Phương Thức Thanh Toán
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Chúng tôi hỗ trợ nhiều phương thức thanh toán để bạn lựa chọn phù hợp nhất
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {paymentMethods.map((method, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white border rounded-lg p-6 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-center mb-4">
                  <div className={`w-16 h-16 ${method.color} rounded-full flex items-center justify-center mr-4`}>
                    {method.icon}
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 text-lg">{method.title}</h3>
                    <p className="text-gray-600 text-sm">{method.description}</p>
                  </div>
                </div>

                <div className="flex justify-between items-center mb-4 p-3 bg-gray-50 rounded">
                  <div>
                    <span className="text-sm text-gray-500">Phí giao dịch: </span>
                    <span className="font-semibold text-green-600">{method.fee}</span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Thời gian: </span>
                    <span className="font-semibold text-gray-900">{method.time}</span>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Các bước thực hiện:</h4>
                  <ol className="space-y-2">
                    {method.steps.map((step, stepIndex) => (
                      <li key={stepIndex} className="flex items-start">
                        <span className="flex-shrink-0 w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs font-bold text-gray-600 mr-3 mt-0.5">
                          {stepIndex + 1}
                        </span>
                        <span className="text-gray-700 text-sm">{step}</span>
                      </li>
                    ))}
                  </ol>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Bank Information */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Thông Tin Chuyển Khoản
              </h2>
              <p className="text-gray-600">
                Sử dụng thông tin dưới đây để chuyển khoản thanh toán đơn hàng
              </p>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="bg-white p-8 rounded-lg shadow-lg border-l-4 border-blue-500"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-bold text-gray-900 mb-4 text-lg">Thông tin tài khoản</h3>
                  <div className="space-y-3">
                    <div>
                      <span className="text-gray-600">Ngân hàng: </span>
                      <span className="font-semibold text-gray-900">{bankInfo.bankName}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Số tài khoản: </span>
                      <span className="font-semibold text-blue-600 text-lg">{bankInfo.accountNumber}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Chủ tài khoản: </span>
                      <span className="font-semibold text-gray-900">{bankInfo.accountName}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Chi nhánh: </span>
                      <span className="font-semibold text-gray-900">{bankInfo.branch}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-bold text-gray-900 mb-4 text-lg">Nội dung chuyển khoản</h3>
                  <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
                    <p className="text-yellow-800 font-medium mb-2">Định dạng:</p>
                    <code className="text-yellow-900 bg-yellow-100 px-2 py-1 rounded text-sm">
                      [Mã đơn hàng] [Số điện thoại]
                    </code>
                    <p className="text-yellow-700 text-sm mt-2">
                      Ví dụ: DH001 **********
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Security Features */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Bảo Mật Thanh Toán
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Chúng tôi áp dụng các công nghệ bảo mật tiên tiến để bảo vệ thông tin thanh toán của bạn
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {securityFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow"
              >
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="text-purple-600">
                    {feature.icon}
                  </div>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Payment Tips */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Lưu Ý Khi Thanh Toán
              </h2>
              <p className="text-gray-600">
                Những điều quan trọng cần nhớ để đảm bảo giao dịch an toàn
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {paymentTips.map((tip, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="flex items-start"
                  >
                    <div className="w-3 h-3 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                    <span className="text-gray-700">{tip}</span>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Câu Hỏi Thường Gặp
              </h2>
            </div>

            <div className="space-y-6">
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="font-bold text-gray-900 mb-2">Tôi có thể hủy đơn hàng sau khi thanh toán không?</h3>
                <p className="text-gray-600">Bạn có thể hủy đơn hàng trong vòng 2 giờ sau khi thanh toán thành công. Sau thời gian này, đơn hàng sẽ được xử lý và giao cho đơn vị vận chuyển.</p>
              </div>
              
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="font-bold text-gray-900 mb-2">Tại sao giao dịch của tôi bị từ chối?</h3>
                <p className="text-gray-600">Giao dịch có thể bị từ chối do: số dư không đủ, thông tin thẻ sai, hoặc ngân hàng từ chối. Vui lòng kiểm tra lại thông tin hoặc liên hệ ngân hàng.</p>
              </div>
              
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="font-bold text-gray-900 mb-2">Khi nào tôi được hoàn tiền?</h3>
                <p className="text-gray-600">Hoàn tiền sẽ được xử lý trong 3-7 ngày làm việc tùy theo phương thức thanh toán ban đầu và chính sách của ngân hàng.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-16 bg-purple-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              Cần Hỗ Trợ Thanh Toán?
            </h2>
            <p className="text-gray-600 mb-8">
              Đội ngũ hỗ trợ thanh toán của chúng tôi luôn sẵn sàng giúp đỡ bạn
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <Phone className="w-8 h-8 text-purple-600 mx-auto mb-4" />
                <h3 className="font-bold text-gray-900 mb-2">Hotline Thanh Toán</h3>
                <p className="text-gray-600 mb-4">Hỗ trợ 24/7 về thanh toán</p>
                <a href="tel:**********" className="text-purple-600 font-bold text-lg">
                  0932.935.085
                </a>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <Mail className="w-8 h-8 text-purple-600 mx-auto mb-4" />
                <h3 className="font-bold text-gray-900 mb-2">Email Thanh Toán</h3>
                <p className="text-gray-600 mb-4">Gửi câu hỏi về thanh toán</p>
                <a href="mailto:<EMAIL>" className="text-purple-600 font-bold text-lg">
                  <EMAIL>
                </a>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
