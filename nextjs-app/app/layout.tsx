import type { Metadata, Viewport } from "next";
import "./globals.css";
import "../src/styles/k-fashion.css";
import { CartProvider } from "../src/contexts/CartContext";
import { WishlistProvider } from "../src/contexts/WishlistContext";
import { ToastProvider } from "../src/components/ui/Toast";
import ConditionalLayout from "../src/components/layout/ConditionalLayout";

export const viewport: Viewport = {
  themeColor: '#667eea',
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
};

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'),
  title: "Thinluong Official - cheapmoment cùng idol",
  description: "Cửa hàng thời trang uy tín, chất lượng cao với phong cách trẻ trung, hiện đại. Mang đến những sản phẩm tốt nhất cho khách hàng.",
  icons: {
    icon: [
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon.ico', sizes: '32x32' },
    ],
    apple: [
      { url: '/apple-touch-icon.svg', sizes: '180x180', type: 'image/svg+xml' },
    ],
    other: [
      { rel: 'icon', url: '/icon-192x192.svg', sizes: '192x192', type: 'image/svg+xml' },
      { rel: 'icon', url: '/icon-512x512.svg', sizes: '512x512', type: 'image/svg+xml' },
    ],
  },
  manifest: '/site.webmanifest',
  keywords: ['thời trang', 'quần áo', 'thinluong', 'fashion', 'clothing', 'style'],
  authors: [{ name: 'Thinluong Official' }],
  creator: 'Thinluong Official',
  publisher: 'Thinluong Official',
  robots: 'index, follow',
  openGraph: {
    type: 'website',
    locale: 'vi_VN',
    url: 'https://thinluong.com',
    siteName: 'Thinluong Official',
    title: 'Thinluong Official - cheapmoment cùng idol',
    description: 'Cửa hàng thời trang uy tín, chất lượng cao với phong cách trẻ trung, hiện đại',
    images: [
      {
        url: '/icon-512x512.svg',
        width: 512,
        height: 512,
        alt: 'Thinluong Official Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Thinluong Official - cheapmoment cùng idol',
    description: 'Cửa hàng thời trang uy tín, chất lượng cao với phong cách trẻ trung, hiện đại',
    images: ['/icon-512x512.svg'],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="font-sans antialiased" style={{ fontFamily: 'Arial, sans-serif' }}>
        <ToastProvider>
          <CartProvider>
            <WishlistProvider>
              <ConditionalLayout>
                {children}
              </ConditionalLayout>
            </WishlistProvider>
          </CartProvider>
        </ToastProvider>
      </body>
    </html>
  );
}
