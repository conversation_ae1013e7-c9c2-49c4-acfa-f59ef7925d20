import React from 'react';
import Link from 'next/link';
import { ArrowLeft, Search } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Search className="w-8 h-8 text-gray-400" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Không tìm thấy sản phẩm</h1>
          <p className="text-gray-600 mb-6">
            Sản phẩm bạn đang tìm kiếm không tồn tại hoặc đã bị xóa. 
            Vui lòng kiểm tra lại đường dẫn hoặc tìm kiếm sản phẩm kh<PERSON>c.
          </p>
        </div>

        <div className="space-y-3">
          <Link
            href="/san-pham"
            className="w-full inline-flex items-center justify-center px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-medium"
          >
            <Search className="w-5 h-5 mr-2" />
            Tìm sản phẩm khác
          </Link>
          
          <Link
            href="/san-pham"
            className="w-full inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Quay lại trang sản phẩm
          </Link>
          
          <Link
            href="/"
            className="block text-gray-500 hover:text-gray-700 transition-colors text-sm"
          >
            Về trang chủ
          </Link>
        </div>
      </div>
    </div>
  );
} 