'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { 
  ShoppingBag, 
  Heart, 
  Star, 
  ArrowLeft, 
  Share2, 
  Truck, 
  Shield, 
  RotateCcw,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { ProductWithCategoryAndReviews, ApiResponse, ProductWithCategory } from '../../../src/types';
import { useCart } from '../../../src/contexts/CartContext';
import { useWishlist } from '../../../src/contexts/WishlistContext';
import ProductCard from '../../../src/components/ui/ProductCard';

const ProductDetailPage = () => {
  const params = useParams();
  const slug = params.slug as string;
  
  const [product, setProduct] = useState<ProductWithCategoryAndReviews | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<ProductWithCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState<'description' | 'reviews' | 'shipping'>('description');
  
  const { addItem: addToCart, loading: cartLoading } = useCart();
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist();

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/products/${slug}`);
        const result: ApiResponse<ProductWithCategoryAndReviews> = await response.json();
        
        if (result.success && result.data) {
          setProduct(result.data);
          
          // Fetch related products in the same categories
          const categoryIds = result.data.categories?.map(cat => cat.id) ||
                             (result.data.category?.id ? [result.data.category.id] : []);

          if (categoryIds.length > 0) {
            // Use the first category for related products
            const relatedResponse = await fetch(`/api/products?categoryId=${categoryIds[0]}&limit=4&exclude=${result.data.id}`);
            const relatedResult = await relatedResponse.json();
            if (relatedResult.success && relatedResult.data?.products) {
              setRelatedProducts(relatedResult.data.products);
            }
          }
        } else {
          setError(result.error || 'Không tìm thấy sản phẩm');
        }
      } catch (err) {
        setError('Lỗi khi tải thông tin sản phẩm');
        console.error('Error fetching product:', err);
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchProduct();
    }
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Không tìm thấy sản phẩm</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link 
            href="/san-pham"
            className="inline-flex items-center px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Quay lại trang sản phẩm
          </Link>
        </div>
      </div>
    );
  }

  // Handle images - could be array or JSON string
  const images = Array.isArray(product.images)
    ? product.images
    : (product.images ? JSON.parse(product.images) : []);
  const productImages = images.length > 0 ? images : ['/images/placeholder.svg'];

  const reviews = product.reviews || [];
  const averageRating = reviews.length > 0
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
    : 0;
  const reviewCount = product._count?.reviews || 0;
  // Handle multiple categories - show primary category or first category
  const primaryCategory = product.category || product.categories?.[0];
  const categoryName = primaryCategory?.name || 'Chưa phân loại';
  const isInWishlistState = isInWishlist(product.id);

  const handleAddToCart = async () => {
    try {
      await addToCart(product.id, quantity);
    } catch (error) {
      console.error('Error adding to cart:', error);
    }
  };

  const handleWishlistToggle = async () => {
    try {
      if (isInWishlistState) {
        await removeFromWishlist(product.id);
      } else {
        await addToWishlist(product.id);
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
    }
  };

  const nextImage = () => {
    setSelectedImageIndex((prev) => (prev + 1) % productImages.length);
  };

  const prevImage = () => {
    setSelectedImageIndex((prev) => (prev - 1 + productImages.length) % productImages.length);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product.name,
          text: `Xem ${product.name} - ${(product.price || 0).toLocaleString('vi-VN')}đ`,
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link đã được sao chép vào clipboard!');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb - FORCE HIDDEN ON MOBILE/TABLET */}
      <div className="breadcrumb-container hidden lg:block bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4">
          {/* Desktop Breadcrumb Only */}
          <nav className="flex items-center space-x-2 text-sm" aria-label="Breadcrumb">
            <Link
              href="/"
              className="text-gray-500 hover:text-gray-700 transition-all duration-200 py-1.5 px-2 -mx-2 rounded-md hover:bg-gray-50 hover:shadow-sm"
            >
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Trang chủ
              </span>
            </Link>
            <span className="text-gray-400 select-none mx-1">/</span>

            <Link
              href="/san-pham"
              className="text-gray-500 hover:text-gray-700 transition-all duration-200 py-1.5 px-2 -mx-2 rounded-md hover:bg-gray-50 hover:shadow-sm"
            >
              Sản phẩm
            </Link>
            <span className="text-gray-400 select-none mx-1">/</span>

            <Link
              href={`/danh-muc/${primaryCategory?.slug || ''}`}
              className="text-gray-500 hover:text-gray-700 transition-all duration-200 py-1.5 px-2 -mx-2 rounded-md hover:bg-gray-50 hover:shadow-sm max-w-[200px] truncate"
              title={categoryName}
            >
              {categoryName}
            </Link>
            <span className="text-gray-400 select-none mx-1">/</span>

            <span
              className="text-gray-900 font-medium max-w-[300px] lg:max-w-[400px] xl:max-w-[500px] truncate"
              title={product.name}
            >
              {product.name}
            </span>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="relative aspect-square bg-white rounded-lg overflow-hidden">
              <Image
                src={productImages[selectedImageIndex]}
                alt={product.name}
                fill
                className="object-cover"
                priority
                sizes="(max-width: 768px) 100vw, 50vw"
              />
              
              {productImages.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg transition-colors"
                  >
                    <ChevronLeft className="w-5 h-5" />
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg transition-colors"
                  >
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </>
              )}

              {product.isOnSale && (
                <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Giảm giá!
                </div>
              )}
            </div>

            {/* Thumbnail Images */}
            {productImages.length > 1 && (
              <div className="grid grid-cols-4 gap-2">
                {productImages.map((image: string, index: number) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`relative aspect-square bg-white rounded-lg overflow-hidden border-2 transition-colors ${
                      index === selectedImageIndex ? 'border-orange-500' : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <Image
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      fill
                      className="object-cover"
                      sizes="100px"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Information */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
              <p className="text-gray-600">{categoryName}</p>
            </div>

            {/* Rating */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-5 h-5 ${i < Math.floor(averageRating) ? 'fill-current' : 'text-gray-300'}`}
                    />
                  ))}
                </div>
                <span className="ml-2 text-gray-600">
                  {averageRating.toFixed(1)} ({reviewCount} đánh giá)
                </span>
              </div>
            </div>

            {/* Price */}
            <div className="flex items-center space-x-4">
              {product.isOnSale && product.originalPrice ? (
                <>
                  <span className="text-3xl font-bold text-red-600">
                    {(product.price || 0).toLocaleString('vi-VN')}đ
                  </span>
                  <span className="text-xl text-gray-500 line-through">
                    {(product.originalPrice || 0).toLocaleString('vi-VN')}đ
                  </span>
                  <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-sm font-medium">
                    Tiết kiệm {((product.originalPrice - product.price) / product.originalPrice * 100).toFixed(0)}%
                  </span>
                </>
              ) : (
                <span className="text-3xl font-bold text-gray-900">
                  {(product.price || 0).toLocaleString('vi-VN')}đ
                </span>
              )}
            </div>

            {/* Stock Status */}
            <div className="flex items-center space-x-2">
              {(product.stock || 0) > 0 ? (
                <>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-green-600 font-medium">
                    Còn hàng ({product.stock} sản phẩm)
                  </span>
                </>
              ) : (
                <>
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-red-600 font-medium">Hết hàng</span>
                </>
              )}
            </div>

            {/* Quantity and Actions */}
            <div className="space-y-4">
              {(product.stock || 0) > 0 && (
                <div className="flex items-center space-x-4">
                  <label className="text-gray-700 font-medium">Số lượng:</label>
                  <div className="flex items-center border border-gray-300 rounded-lg">
                    <button
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="px-3 py-2 hover:bg-gray-100 transition-colors"
                      disabled={quantity <= 1}
                    >
                      -
                    </button>
                    <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">
                      {quantity}
                    </span>
                    <button
                      onClick={() => setQuantity(Math.min(product.stock || 1, quantity + 1))}
                      className="px-3 py-2 hover:bg-gray-100 transition-colors"
                      disabled={quantity >= (product.stock || 1)}
                    >
                      +
                    </button>
                  </div>
                </div>
              )}

              <div className="flex space-x-4">
                <button
                  onClick={handleAddToCart}
                  disabled={cartLoading || (product.stock || 0) === 0}
                  className={`flex-1 py-3 px-6 rounded-lg font-medium transition-colors ${
                    (product.stock || 0) === 0
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : cartLoading
                      ? 'bg-gray-400 text-white cursor-wait'
                      : 'bg-orange-500 text-white hover:bg-orange-600'
                  }`}
                >
                  {(product.stock || 0) === 0 ? (
                    'Hết hàng'
                  ) : cartLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Đang thêm...
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <ShoppingBag className="w-5 h-5 mr-2" />
                      Thêm vào giỏ hàng
                    </div>
                  )}
                </button>

                <button
                  onClick={handleWishlistToggle}
                  className={`p-3 rounded-lg border-2 transition-colors ${
                    isInWishlistState
                      ? 'border-red-500 bg-red-50 text-red-500'
                      : 'border-gray-300 text-gray-600 hover:border-red-500 hover:text-red-500'
                  }`}
                >
                  <Heart className={`w-6 h-6 ${isInWishlistState ? 'fill-current' : ''}`} />
                </button>

                <button 
                  onClick={handleShare}
                  className="p-3 rounded-lg border-2 border-gray-300 text-gray-600 hover:border-gray-400 transition-colors"
                >
                  <Share2 className="w-6 h-6" />
                </button>
              </div>
            </div>

            {/* Shipping Info */}
            <div className="border-t pt-6 space-y-3">
              <div className="flex items-center space-x-3 text-gray-600">
                <Truck className="w-5 h-5" />
                <span>Miễn phí vận chuyển cho đơn hàng trên 500,000đ</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-600">
                <Shield className="w-5 h-5" />
                <span>Bảo hành chính hãng 12 tháng</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-600">
                <RotateCcw className="w-5 h-5" />
                <span>Đổi trả miễn phí trong 7 ngày</span>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8">
              {[
                { key: 'description', label: 'Mô tả sản phẩm' },
                { key: 'reviews', label: `Đánh giá (${reviewCount})` },
                { key: 'shipping', label: 'Vận chuyển & Đổi trả' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as 'description' | 'reviews' | 'shipping')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.key
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="py-8">
            {activeTab === 'description' && (
              <div className="prose max-w-none">
                <div className="text-gray-700 leading-relaxed">
                  {product.description ? (
                    product.description.split('\n').map((paragraph, index) => (
                      <p key={index} className="mb-4">{paragraph}</p>
                    ))
                  ) : (
                    <p>Chưa có mô tả cho sản phẩm này.</p>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="space-y-6">
                {reviews.length > 0 ? (
                  reviews.map((review) => (
                    <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                          <span className="text-gray-600 font-medium">
                            {(review.user?.name || review.user?.email || 'U').charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="font-medium text-gray-900">
                              {review.user?.name || 'Khách hàng'}
                            </h4>
                            <div className="flex text-yellow-400">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`w-4 h-4 ${i < review.rating ? 'fill-current' : 'text-gray-300'}`}
                                />
                              ))}
                            </div>
                            <span className="text-gray-500 text-sm">
                              {new Date(review.createdAt).toLocaleDateString('vi-VN')}
                            </span>
                          </div>
                          <p className="text-gray-700 ml-4">{review.comment}</p>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-8">
                    Chưa có đánh giá nào cho sản phẩm này.
                  </p>
                )}
              </div>
            )}

            {activeTab === 'shipping' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Chính sách vận chuyển</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li>• Miễn phí vận chuyển cho đơn hàng trên 500,000đ</li>
                    <li>• Giao hàng trong 2-3 ngày làm việc tại nội thành</li>
                    <li>• Giao hàng trong 5-7 ngày làm việc tại tỉnh thành khác</li>
                    <li>• Hỗ trợ giao hàng COD (Thanh toán khi nhận hàng)</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Chính sách đổi trả</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li>• Đổi trả miễn phí trong 7 ngày</li>
                    <li>• Sản phẩm phải còn nguyên tem mác, chưa qua sử dụng</li>
                    <li>• Hoàn tiền 100% nếu sản phẩm lỗi từ nhà sản xuất</li>
                    <li>• Hỗ trợ đổi size miễn phí</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Sản phẩm liên quan</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <ProductCard key={relatedProduct.id} product={relatedProduct} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductDetailPage; 