'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { 
  ShoppingBag, 
  Heart, 
  Star, 
  ArrowLeft, 
  Share2, 
  Truck, 
  Shield, 
  RotateCcw,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { ProductWithCategoryAndReviews, ApiResponse, ProductWithCategory } from '../../../src/types';
import { useCart } from '../../../src/contexts/CartContext';
import { useWishlist } from '../../../src/contexts/WishlistContext';
import ProductCard from '../../../src/components/ui/ProductCard';
import { useToast } from '../../../src/components/ui/Toast';
import ProductAttributeTags from '../../../src/components/ui/ProductAttributeTags';

const ProductDetailPage = () => {
  const params = useParams();
  const slug = params.slug as string;
  
  const [product, setProduct] = useState<ProductWithCategoryAndReviews | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<ProductWithCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState<string | null>(null);
  const [selectedColor, setSelectedColor] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<{size?: boolean; color?: boolean}>({});
  const [activeTab, setActiveTab] = useState<'description' | 'reviews' | 'shipping'>('description');
  
  const [cartLoading, setCartLoading] = useState(false);
  const { addItem: addToCart } = useCart();
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist();
  const { showToast } = useToast();

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/products/${slug}`);
        const result: ApiResponse<ProductWithCategoryAndReviews> = await response.json();
        
        if (result.success && result.data) {
          setProduct(result.data);
          
          // Fetch related products in the same categories
          const categoryIds = result.data.categories?.map(cat => cat.id) ||
                             (result.data.category?.id ? [result.data.category.id] : []);

          if (categoryIds.length > 0) {
            // Use the first category for related products
            const relatedResponse = await fetch(`/api/products?categoryId=${categoryIds[0]}&limit=4&exclude=${result.data.id}`);
            const relatedResult = await relatedResponse.json();
            if (relatedResult.success && relatedResult.data?.products) {
              setRelatedProducts(relatedResult.data.products);
            }
          }
        } else {
          setError(result.error || 'Không tìm thấy sản phẩm');
        }
      } catch (err) {
        setError('Lỗi khi tải thông tin sản phẩm');
        console.error('Error fetching product:', err);
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchProduct();
    }
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Không tìm thấy sản phẩm</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link 
            href="/san-pham"
            className="inline-flex items-center px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Quay lại trang sản phẩm
          </Link>
        </div>
      </div>
    );
  }

  // Handle images - could be array or JSON string
  const images = Array.isArray(product.images)
    ? product.images
    : (product.images ? JSON.parse(product.images) : []);
  const productImages = images.length > 0 ? images : ['/images/placeholder.svg'];

  const reviews = product.reviews || [];
  const averageRating = reviews.length > 0
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
    : 0;
  const reviewCount = product._count?.reviews || 0;
  // Handle multiple categories - show primary category or first category
  const primaryCategory = product.category || product.categories?.[0];
  const categoryName = primaryCategory?.name || 'Chưa phân loại';
  const isInWishlistState = isInWishlist(product.id);

  const handleAddToCart = async () => {
    // Reset validation errors
    setValidationErrors({});

    // Validation for size and color
    const errors: {size?: boolean; color?: boolean} = {};

    // Check if product has sizes and no size is selected
    if ((product.sizes && product.sizes.length > 0) && !selectedSize) {
      errors.size = true;
    }

    // Check if product has colors and no color is selected
    if ((product.colors && product.colors.length > 0) && !selectedColor) {
      errors.color = true;
    }

    // Legacy support for variants
    if (product.variants && product.variants.length > 0 && !selectedSize) {
      errors.size = true;
    }

    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return;
    }

    setCartLoading(true);
    try {
      await addToCart(product.id, quantity, selectedSize, selectedColor);
      showToast(`${product.name} đã được thêm vào giỏ hàng`, 'success', 'cart');
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast('Có lỗi xảy ra khi thêm vào giỏ hàng', 'error');
    } finally {
      setCartLoading(false);
    }
  };

  const handleWishlistToggle = async () => {
    try {
      const isInWishlistState = isInWishlist(product.id);
      if (isInWishlistState) {
        await removeFromWishlist(product.id);
        showToast(`${product.name} đã được xóa khỏi danh sách yêu thích`, 'info', 'heart');
      } else {
        await addToWishlist(product.id);
        showToast(`${product.name} đã được thêm vào danh sách yêu thích`, 'success', 'heart');
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      showToast('Có lỗi xảy ra khi cập nhật danh sách yêu thích', 'error');
    }
  };

  const nextImage = () => {
    setSelectedImageIndex((prev) => (prev + 1) % productImages.length);
  };

  const prevImage = () => {
    setSelectedImageIndex((prev) => (prev - 1 + productImages.length) % productImages.length);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product.name,
          text: `Xem ${product.name} - ${(product.price || 0).toLocaleString('vi-VN')}đ`,
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link đã được sao chép vào clipboard!');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-gray-500 hover:text-gray-700">Trang chủ</Link>
            <span className="text-gray-400">/</span>
            <Link href="/san-pham" className="text-gray-500 hover:text-gray-700">Sản phẩm</Link>
            <span className="text-gray-400">/</span>
            <Link href={`/danh-muc/${primaryCategory?.slug || ''}`} className="text-gray-500 hover:text-gray-700">
              {categoryName}
            </Link>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">{product.name}</span>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="relative aspect-square bg-white rounded-lg overflow-hidden">
              <Image
                src={productImages[selectedImageIndex]}
                alt={product.name}
                fill
                className="object-cover"
                priority
                sizes="(max-width: 768px) 100vw, 50vw"
              />
              
              {productImages.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg transition-colors"
                  >
                    <ChevronLeft className="w-5 h-5" />
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg transition-colors"
                  >
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </>
              )}

              {product.isOnSale && (
                <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Giảm giá!
                </div>
              )}
            </div>

            {/* Thumbnail Images */}
            {productImages.length > 1 && (
              <div className="grid grid-cols-4 gap-2">
                {productImages.map((image: string, index: number) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`relative aspect-square bg-white rounded-lg overflow-hidden border-2 transition-colors ${
                      index === selectedImageIndex ? 'border-orange-500' : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <Image
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      fill
                      className="object-cover"
                      sizes="100px"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Information */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
              <p className="text-gray-600">{categoryName}</p>
            </div>

            {/* Rating */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-5 h-5 ${i < Math.floor(averageRating) ? 'fill-current' : 'text-gray-300'}`}
                    />
                  ))}
                </div>
                <span className="ml-2 text-gray-600">
                  {averageRating.toFixed(1)} ({reviewCount} đánh giá)
                </span>
              </div>
            </div>

            {/* Price */}
            <div className="flex items-center space-x-4">
              {product.isOnSale && product.originalPrice ? (
                <>
                  <span className="text-3xl font-bold text-red-600">
                    {(product.price || 0).toLocaleString('vi-VN')}đ
                  </span>
                  <span className="text-xl text-gray-500 line-through">
                    {(product.originalPrice || 0).toLocaleString('vi-VN')}đ
                  </span>
                  <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-sm font-medium">
                    Tiết kiệm {((product.originalPrice - product.price) / product.originalPrice * 100).toFixed(0)}%
                  </span>
                </>
              ) : (
                <span className="text-3xl font-bold text-gray-900">
                  {(product.price || 0).toLocaleString('vi-VN')}đ
                </span>
              )}
            </div>

            {/* Stock Status */}
            <div className="flex items-center space-x-2">
              {(product.stock || 0) > 0 ? (
                <>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-green-600 font-medium">
                    Còn hàng ({product.stock} sản phẩm)
                  </span>
                </>
              ) : (
                <>
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-red-600 font-medium">Hết hàng</span>
                </>
              )}
            </div>

            {/* Size and Color Selection */}
            <div className="space-y-6">


              {((product.sizes && product.sizes.length > 0) ||
                (product.colors && product.colors.length > 0) ||
                (product.variants && product.variants.length > 0)) && (
                <div>
                  <ProductAttributeTags
                    sizes={product.sizes || []}
                    colors={product.colors || []}
                    selectedSize={selectedSize}
                    selectedColor={selectedColor}
                    onSizeChange={setSelectedSize}
                    onColorChange={setSelectedColor}
                    disabled={(product.stock || 0) === 0}
                    showLabels={true}
                    showValidationErrors={true}
                  />
                </div>
              )}

              {/* Quantity Selection */}
              {(product.stock || 0) > 0 && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <label className="text-gray-700 font-medium">Số lượng:</label>
                    <div className="flex items-center border border-gray-300 rounded-lg">
                      <button
                        onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        className="px-3 py-2 text-gray-600 hover:text-gray-800"
                      >
                        -
                      </button>
                      <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">
                        {quantity}
                      </span>
                      <button
                        onClick={() => setQuantity(Math.min(product.stock || 0, quantity + 1))}
                        className="px-3 py-2 text-gray-600 hover:text-gray-800"
                      >
                        +
                      </button>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-4">
                    <button
                      onClick={handleAddToCart}
                      disabled={cartLoading || (product.stock || 0) === 0}
                      className={`flex-1 py-3 px-6 rounded-lg font-medium transition-colors ${
                        (product.stock || 0) === 0
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : cartLoading
                          ? 'bg-gray-400 text-white cursor-wait'
                          : 'bg-blue-600 text-white hover:bg-blue-700'
                      }`}
                    >
                      {(product.stock || 0) === 0 ? (
                        'Hết hàng'
                      ) : cartLoading ? (
                        <div className="flex items-center justify-center">
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                          Đang thêm...
                        </div>
                      ) : (
                        <div className="flex items-center justify-center">
                          <ShoppingBag className="w-5 h-5 mr-2" />
                          Thêm vào giỏ hàng
                        </div>
                      )}
                    </button>

                    <button
                      onClick={handleWishlistToggle}
                      className={`p-3 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors ${
                        isInWishlistState ? 'text-red-500' : 'text-gray-600'
                      }`}
                    >
                      <Heart className={`w-5 h-5 ${isInWishlistState ? 'fill-current' : ''}`} />
                    </button>

                    <button
                      onClick={handleShare}
                      className="p-3 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50 transition-colors"
                    >
                      <Share2 className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              )}

              {/* Features */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t border-gray-200">
                <div className="flex items-center space-x-3">
                  <Truck className="w-5 h-5 text-blue-600" />
                  <span className="text-sm text-gray-600">Miễn phí vận chuyển</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Shield className="w-5 h-5 text-green-600" />
                  <span className="text-sm text-gray-600">Bảo hành chính hãng</span>
                </div>
                <div className="flex items-center space-x-3">
                  <RotateCcw className="w-5 h-5 text-orange-600" />
                  <span className="text-sm text-gray-600">Đổi trả 30 ngày</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8">
              {[
                { key: 'description', label: 'Mô tả sản phẩm' },
                { key: 'reviews', label: `Đánh giá (${reviewCount})` },
                { key: 'shipping', label: 'Vận chuyển & Đổi trả' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as 'description' | 'reviews' | 'shipping')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.key
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="py-8">
            {activeTab === 'description' && (
              <div className="prose max-w-none">
                <div className="text-gray-700 leading-relaxed">
                  {product.description ? (
                    product.description.split('\n').map((paragraph, index) => (
                      <p key={index} className="mb-4">{paragraph}</p>
                    ))
                  ) : (
                    <p>Chưa có mô tả cho sản phẩm này.</p>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="space-y-6">
                {reviews.length > 0 ? (
                  reviews.map((review) => (
                    <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                          <span className="text-gray-600 font-medium">
                            {(review.user?.name || review.user?.email || 'U').charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="font-medium text-gray-900">
                              {review.user?.name || 'Khách hàng'}
                            </h4>
                            <div className="flex text-yellow-400">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`w-4 h-4 ${i < review.rating ? 'fill-current' : 'text-gray-300'}`}
                                />
                              ))}
                            </div>
                            <span className="text-gray-500 text-sm">
                              {new Date(review.createdAt).toLocaleDateString('vi-VN')}
                            </span>
                          </div>
                          <p className="text-gray-700">{review.comment}</p>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-8">
                    Chưa có đánh giá nào cho sản phẩm này.
                  </p>
                )}
              </div>
            )}

            {activeTab === 'shipping' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Chính sách vận chuyển</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li>• Miễn phí vận chuyển cho đơn hàng trên 500,000đ</li>
                    <li>• Giao hàng trong 2-3 ngày làm việc tại nội thành</li>
                    <li>• Giao hàng trong 5-7 ngày làm việc tại tỉnh thành khác</li>
                    <li>• Hỗ trợ giao hàng COD (Thanh toán khi nhận hàng)</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Chính sách đổi trả</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li>• Đổi trả miễn phí trong 7 ngày</li>
                    <li>• Sản phẩm phải còn nguyên tem mác, chưa qua sử dụng</li>
                    <li>• Hoàn tiền 100% nếu sản phẩm lỗi từ nhà sản xuất</li>
                    <li>• Hỗ trợ đổi size miễn phí</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Sản phẩm liên quan</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <ProductCard key={relatedProduct.id} product={relatedProduct} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductDetailPage; 