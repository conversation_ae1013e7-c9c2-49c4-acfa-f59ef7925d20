/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useEffect, Suspense } from 'react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { Search, Package } from 'lucide-react'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import ProductCard from '@/components/ui/ProductCard'

interface Product {
  id: string
  name: string
  slug: string
  price: number
  originalPrice?: number
  images: string[]
  category: {
    id: string
    name: string
    slug: string
  }
}

interface Category {
  id: string
  name: string
  slug: string
  description: string | null
  productCount: number
}

interface CategoryWithProducts extends Category {
  products: Product[]
}

// Component that uses useSearchParams wrapped in Suspense
function SearchParamsHandler({ onSearchQuery }: { onSearchQuery: (query: string | null) => void }) {
  const searchParams = useSearchParams()
  const searchQuery = searchParams.get('search')

  useEffect(() => {
    onSearchQuery(searchQuery)
  }, [searchQuery, onSearchQuery])

  return null
}

function SanPhamPageContent() {
  const [searchQuery, setSearchQuery] = useState<string | null>(null)
  const [categoriesWithProducts, setCategoriesWithProducts] = useState<CategoryWithProducts[]>([])
  const [searchResults, setSearchResults] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [isSearchMode, setIsSearchMode] = useState(false)

  // Function to fetch search results
  const fetchSearchResults = async (query: string) => {
    try {
      setLoading(true)
      setIsSearchMode(true)

      const response = await fetch(`/api/search?q=${encodeURIComponent(query)}&type=products&limit=50`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setSearchResults(data.data.products || [])
        }
      }
    } catch (error) {
      console.error('Error fetching search results:', error)
      setError('Lỗi khi tìm kiếm')
    } finally {
      setLoading(false)
    }
  }

  // Function to fetch categories and products
  const fetchCategoriesData = async () => {
    try {
      setLoading(true)
      setIsSearchMode(false)

      console.log('Fetching categories...')

      // Fetch all categories
      const categoriesResponse = await fetch('/api/categories?includeChildren=true')
      if (!categoriesResponse.ok) {
        throw new Error('Failed to fetch categories')
      }
      const categoriesData = await categoriesResponse.json()

      if (!categoriesData.success || !Array.isArray(categoriesData.data)) {
        throw new Error('Invalid categories data')
      }

      console.log('Categories:', categoriesData.data)
      const categories: Category[] = categoriesData.data

      // Fetch products for each category
      const categoriesWithProductsData = await Promise.all(
        categories.map(async (category) => {
          try {
            console.log(`Fetching products for category: ${category.slug}`)
            const productsResponse = await fetch(`/api/categories/${category.slug}/products?page=1&limit=10`)
            if (productsResponse.ok) {
              const productsData = await productsResponse.json()
              // The API returns { success: true, data: { products: [...], category: {...}, pagination: {...} } }
              const products = productsData.success && productsData.data?.products ? productsData.data.products : []
              console.log(`Products for ${category.slug}:`, products.length)
              return {
                ...category,
                products
              }
            }
            return {
              ...category,
              products: []
            }
          } catch (error) {
            console.error(`Error fetching products for category ${category.slug}:`, error)
            return {
              ...category,
              products: []
            }
          }
        })
      )

      // Filter out categories with no products
      const validCategories = categoriesWithProductsData.filter(cat => cat.products.length > 0)
      console.log('Valid categories with products:', validCategories.length)
      setCategoriesWithProducts(validCategories)
    } catch (error) {
      console.error('Error fetching data:', error)
      setError('Lỗi khi tải dữ liệu')
    } finally {
      setLoading(false)
    }
  }

  const handleSearchQuery = (query: string | null) => {
    setSearchQuery(query)
  }

  useEffect(() => {
    if (searchQuery && searchQuery.trim().length >= 2) {
      fetchSearchResults(searchQuery.trim())
    } else {
      fetchCategoriesData()
    }
  }, [searchQuery])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Đang tải sản phẩm...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Có lỗi xảy ra</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Thử lại
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <Suspense fallback={null}>
        <SearchParamsHandler onSearchQuery={handleSearchQuery} />
      </Suspense>

      {/* K-Fashion Hero Section */}
      <section className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="elegant-title text-4xl md:text-5xl lg:text-6xl mb-6">
              Bộ Sưu Tập Thời Trang
            </h1>

            <p className="elegant-body text-lg md:text-xl text-gray-600 max-w-2xl mx-auto mb-8">
              Khám phá những xu hướng thời trang mới nhất với thiết kế tinh tế và chất lượng cao.
              Tìm kiếm phong cách riêng của bạn từ bộ sưu tập đa dạng của chúng tôi.
            </p>

            <div className="flex flex-wrap justify-center gap-4">
              <div className="elegant-badge-accent p-4 radius-md">
                {isSearchMode ? searchResults.length : categoriesWithProducts.reduce((total, cat) => total + cat.products.length, 0)} sản phẩm
              </div>
              <div className="elegant-badge">
                {categoriesWithProducts.length} danh mục
              </div>
              <div className="elegant-badge">
                Chất lượng cao
              </div>
            </div>
          </div>
        </div>


      </section>

      <div className="container mx-auto px-4 py-12">
        {/* Search Results */}
        {isSearchMode ? (
          <div>
            {/* Elegant Search Header */}
            <div className="mb-12">
              <div className="elegant-card p-6 md:p-8">
                <div className="flex items-center mb-4">
                  <Search className="w-6 h-6 text-gray-600 mr-3" />
                  <h2 className="elegant-title text-2xl md:text-3xl">
                    Kết quả tìm kiếm
                  </h2>
                </div>
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                  <p className="elegant-body">
                    Từ khóa: <span className="font-semibold">&quot;{searchQuery}&quot;</span>
                  </p>
                  <div className="elegant-badge-accent p-4 radius-md">
                    Tìm thấy {searchResults.length} sản phẩm
                  </div>
                </div>
              </div>
            </div>

            {/* Search Results Grid */}
            {searchResults.length === 0 ? (
              <div className="text-center py-16">
                <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Không tìm thấy sản phẩm nào</h2>
                <p className="text-gray-600 mb-4">Thử tìm kiếm với từ khóa khác hoặc xem tất cả sản phẩm.</p>
                <Link
                  href="/san-pham"
                  className="inline-flex items-center px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                >
                  Xem tất cả sản phẩm
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {searchResults.map((product) => (
                  <div key={product.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="aspect-w-1 aspect-h-1">
                      <img
                        src={product.images[0] || '/images/placeholder.jpg'}
                        alt={product.name}
                        className="w-full h-48 object-cover"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                        {product.name}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">{product.category.name}</p>
                      <div className="flex items-center justify-between">
                        <div>
                          {product.originalPrice && product.originalPrice > product.price ? (
                            <div className="flex items-center space-x-2">
                              <span className="text-lg font-bold text-orange-600">
                                {product.price.toLocaleString('vi-VN')}đ
                              </span>
                              <span className="text-sm text-gray-500 line-through">
                                {product.originalPrice.toLocaleString('vi-VN')}đ
                              </span>
                            </div>
                          ) : (
                            <span className="text-lg font-bold text-gray-900">
                              {product.price.toLocaleString('vi-VN')}đ
                            </span>
                          )}
                        </div>
                        <Link
                          href={`/san-pham/${product.slug}`}
                          className="px-4 py-2 bg-orange-600 text-white text-sm rounded-lg hover:bg-orange-700 transition-colors"
                        >
                          Xem chi tiết
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          /* Categories and Products */
          <div>
            {/* Elegant Categories Header */}
            <div className="text-center mb-16">
              <div className="max-w-4xl mx-auto">
                <h2 className="elegant-title text-3xl md:text-4xl mb-6">
                  Danh Mục Sản Phẩm
                </h2>
                <p className="elegant-body text-lg md:text-xl text-gray-600 max-w-2xl mx-auto mb-8">
                  Khám phá bộ sưu tập đa dạng được phân loại theo từng danh mục,
                  giúp bạn dễ dàng tìm kiếm sản phẩm phù hợp với phong cách của mình.
                </p>

                <div className="flex flex-wrap justify-center gap-4">
                  <div className="elegant-badge-accent p-4 radius-md">
                    {categoriesWithProducts.length} danh mục
                  </div>
                  <div className="elegant-badge">
                    {categoriesWithProducts.reduce((total, cat) => total + cat.products.length, 0)} sản phẩm
                  </div>
                  <div className="elegant-badge">
                    Thiết kế tinh tế
                  </div>
                </div>
              </div>
            </div>

            {/* K-Fashion Categories with Products */}
            {categoriesWithProducts.length === 0 ? (
              <div className="text-center py-20">
                <div className="k-glass rounded-3xl p-12 max-w-md mx-auto border border-white/20">
                  <Package className="w-20 h-20 text-pink-400 mx-auto mb-6" />
                  <h2 className="text-2xl font-bold text-gray-900 mb-4 k-fashion-subtitle">Chưa có sản phẩm K-Fashion nào</h2>
                  <p className="text-gray-600">Các sản phẩm idol Hàn Quốc sẽ được cập nhật sớm.</p>
                </div>
              </div>
            ) : (
              <div className="space-y-20">
                {categoriesWithProducts.map((category) => (
                  <div key={category.id}>
                    {/* Elegant Category Header */}
                    <div className="elegant-card p-6 md:p-8 mb-8">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                        <div>
                          <h2 className="elegant-title text-2xl md:text-3xl mb-2">
                            {category.name}
                          </h2>
                          {category.description && (
                            <p className="elegant-body text-gray-600 mb-2">{category.description}</p>
                          )}
                          <p className="elegant-text-subtle">
                            {category.products.length} sản phẩm
                          </p>
                        </div>
                        <Link
                          href={`/danh-muc/${category.slug}`}
                          className="elegant-button-outline px-6 py-3 min-h-[44px] flex items-center justify-center"
                        >
                          Xem tất cả ({category.productCount || category.products.length})
                        </Link>
                      </div>
                    </div>

                    {/* K-Fashion Products Grid */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
                      {category.products.map((product) => (
                        <div key={product.id}>
                          <ProductCard product={product} />
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default function SanPhamPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Đang tải sản phẩm...</p>
        </div>
      </div>
    }>
      <SanPhamPageContent />
    </Suspense>
  )
}
