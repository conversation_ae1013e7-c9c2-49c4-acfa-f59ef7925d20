'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ShoppingBag, 
  User, 
  CreditCard,
  ArrowLeft,
  Check,
  Loader2
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useCart } from '../../src/contexts/CartContext';
import { useToast } from '../../src/components/ui/Toast';

interface CustomerInfo {
  name: string;
  email: string;
  phone: string;
  address: string;
  ward: string;
  province: string;
}

export default function ThanhToanPage() {
  const router = useRouter();
  const { items, clearCart, total, itemCount } = useCart();
  const { showToast } = useToast();
  
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    email: '',
    phone: '',
    address: '',
    ward: '',
    province: ''
  });
  
  const [paymentMethod, setPaymentMethod] = useState('COD');
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Partial<CustomerInfo>>({});

  // Shipping calculation (same as cart page)
  const shippingThreshold = 500000; // 500,000 VND
  const shippingCost = total >= shippingThreshold ? 0 : 30000;
  const finalTotal = total + shippingCost;

  // Redirect if cart is empty (with delay to allow cart loading)
  useEffect(() => {
    const checkCartAndRedirect = () => {
      if (items.length === 0) {
        // Check localStorage as fallback
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
          try {
            const cartItems = JSON.parse(savedCart);
            if (cartItems.length > 0) {
              return; // Don't redirect if cart exists in localStorage
            }
          } catch (error) {
            console.error('Error parsing cart from localStorage:', error);
          }
        }

        router.push('/gio-hang');
      }
    };

    // Add delay to allow cart context to load
    const timeoutId = setTimeout(checkCartAndRedirect, 1000);

    return () => clearTimeout(timeoutId);
  }, [items.length, router]);

  const validateForm = (): boolean => {
    const newErrors: Partial<CustomerInfo> = {};
    
    if (!customerInfo.name.trim()) {
      newErrors.name = 'Vui lòng nhập họ tên';
    }
    
    if (!customerInfo.email.trim()) {
      newErrors.email = 'Vui lòng nhập email';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customerInfo.email)) {
      newErrors.email = 'Email không hợp lệ';
    }
    
    if (!customerInfo.phone.trim()) {
      newErrors.phone = 'Vui lòng nhập số điện thoại';
    } else if (!/^[0-9+\-\s()]{10,15}$/.test(customerInfo.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Số điện thoại không hợp lệ';
    }
    
    if (!customerInfo.address.trim()) {
      newErrors.address = 'Vui lòng nhập địa chỉ chi tiết';
    }

    if (!customerInfo.ward.trim()) {
      newErrors.ward = 'Vui lòng nhập phường/xã';
    }

    if (!customerInfo.province.trim()) {
      newErrors.province = 'Vui lòng nhập tỉnh/thành phố';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof CustomerInfo, value: string) => {
    setCustomerInfo(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      showToast('Vui lòng kiểm tra lại thông tin', 'error');
      return;
    }

    setIsSubmitting(true);
    
    try {
      const orderData = {
        customerInfo,
        cartItems: items.map(item => ({
          productId: item.productId,
          variantId: item.variantId || null,
          selectedColor: item.selectedColor || null,
          quantity: item.quantity
        })),
        paymentMethod,
        notes
      };

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
      });

      const result = await response.json();

      if (result.success) {
        // Clear cart after successful order
        await clearCart();
        
        showToast('Đặt hàng thành công!', 'success');
        
        // Redirect to order confirmation page
        router.push(`/don-hang-thanh-cong?orderNumber=${result.data.orderNumber}`);
      } else {
        showToast(result.error || 'Có lỗi xảy ra khi đặt hàng', 'error');
      }
    } catch (error) {
      console.error('Error placing order:', error);
      showToast('Có lỗi xảy ra khi đặt hàng', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getProductImage = (images: string | string[]) => {
    try {
      const imageArray = Array.isArray(images)
        ? images
        : (images ? JSON.parse(images) : []);
      return imageArray.length > 0 ? imageArray[0] : '/images/placeholder.svg';
    } catch {
      return '/images/placeholder.svg';
    }
  };

  // Get size display name from variant
  const getSizeDisplayName = (variant: any, variantId?: string) => {
    if (!variant && !variantId) return null;

    if (variant) {
      const attributes = variant.attributes || {};
      return attributes.size || variant.name;
    }

    // If no variant object but we have variantId, return null to trigger data fetch
    return null;
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ShoppingBag className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Giỏ hàng trống
          </h2>
          <p className="text-gray-600 mb-4">
            Thêm sản phẩm vào giỏ hàng để tiến hành thanh toán
          </p>
          <Link
            href="/san-pham"
            className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
          >
            Mua sắm ngay
          </Link>
        </div>
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center space-x-4 mb-6">
            <Link
              href="/gio-hang"
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Quay lại giỏ hàng
            </Link>
          </div>
          
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center mb-4">
              <CreditCard className="w-8 h-8 text-gray-900 mr-3" />
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
                Thanh Toán
              </h1>
            </div>
            <p className="text-gray-600">
              Hoàn tất thông tin để đặt hàng
            </p>
          </motion.div>
        </div>
      </section>

      {/* Checkout Form */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Customer Information */}
              <div className="lg:col-span-2">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6 }}
                  className="bg-white rounded-lg shadow-sm p-6"
                >
                  <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <User className="w-5 h-5 mr-2" />
                    Thông tin giao hàng
                  </h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        Họ và tên *
                      </label>
                      <input
                        type="text"
                        id="name"
                        value={customerInfo.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                          errors.name ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Nguyễn Văn A"
                      />
                      {errors.name && (
                        <p className="mt-1 text-sm text-red-500">{errors.name}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                        Số điện thoại *
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        value={customerInfo.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                          errors.phone ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="0901234567"
                      />
                      {errors.phone && (
                        <p className="mt-1 text-sm text-red-500">{errors.phone}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-6">
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email *
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={customerInfo.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                        errors.email ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="<EMAIL>"
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-red-500">{errors.email}</p>
                    )}
                  </div>
                  
                  <div className="mt-6">
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                      Địa chỉ chi tiết *
                    </label>
                    <input
                      type="text"
                      id="address"
                      value={customerInfo.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                        errors.address ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Số nhà, tên đường (VD: 123 Đường Nguyễn Văn Linh)"
                    />
                    {errors.address && (
                      <p className="mt-1 text-sm text-red-500">{errors.address}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                      <label htmlFor="province" className="block text-sm font-medium text-gray-700 mb-2">
                        Tỉnh/Thành phố *
                      </label>
                      <input
                        type="text"
                        id="province"
                        value={customerInfo.province}
                        onChange={(e) => handleInputChange('province', e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                          errors.province ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="TP. Hồ Chí Minh"
                      />
                      {errors.province && (
                        <p className="mt-1 text-sm text-red-500">{errors.province}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="ward" className="block text-sm font-medium text-gray-700 mb-2">
                        Phường/Xã *
                      </label>
                      <input
                        type="text"
                        id="ward"
                        value={customerInfo.ward}
                        onChange={(e) => handleInputChange('ward', e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                          errors.ward ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Phường Bến Nghé"
                      />
                      {errors.ward && (
                        <p className="mt-1 text-sm text-red-500">{errors.ward}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-6">
                    <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                      Ghi chú đơn hàng (tùy chọn)
                    </label>
                    <textarea
                      id="notes"
                      rows={3}
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                      placeholder="Ghi chú về đơn hàng..."
                    />
                  </div>
                </motion.div>

                {/* Payment Method */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="bg-white rounded-lg shadow-sm p-6 mt-6"
                >
                  <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <CreditCard className="w-5 h-5 mr-2" />
                    Phương thức thanh toán
                  </h2>
                  
                  <div className="space-y-4">
                    <label className="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="COD"
                        checked={paymentMethod === 'COD'}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="mr-4"
                      />
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">Thanh toán khi nhận hàng (COD)</p>
                        <p className="text-sm text-gray-600">Thanh toán bằng tiền mặt khi nhận hàng</p>
                      </div>
                    </label>
                  </div>
                </motion.div>
              </div>

              {/* Order Summary */}
              <div className="lg:col-span-1">
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6 }}
                  className="bg-white rounded-lg shadow-sm p-6 sticky top-8"
                >
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Đơn hàng của bạn
                  </h3>
                  
                  {/* Order Items Summary */}
                  <div className="mb-6 pb-4 border-b border-gray-200">
                    <h4 className="text-sm font-semibold text-gray-700 mb-3">
                      Sản phẩm đã chọn:
                    </h4>
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      {items.map((item) => (
                        <div key={item.id} className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                          <div className="flex justify-between items-start">
                            <div className="flex-1 min-w-0 pr-2">
                              <span className="text-gray-900 font-medium text-sm block line-clamp-2">
                                {item.product.name}
                              </span>
                              <div className="text-xs text-gray-600 mt-1 space-y-1">
                                {item.variantId && (
                                  <div>Size: <span className="font-medium text-blue-600">{getSizeDisplayName(item.variant, item.variantId) || item.variantId}</span></div>
                                )}
                                {item.selectedColor && (
                                  <div>Màu: <span className="font-medium text-green-600">{item.selectedColor}</span></div>
                                )}
                              </div>
                              <span className="text-gray-500 text-xs block mt-1">
                                Số lượng: {item.quantity}
                              </span>
                            </div>
                            <span className="font-bold text-gray-900 text-sm whitespace-nowrap">
                              {((item.product.price || 0) * item.quantity).toLocaleString('vi-VN')}đ
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="space-y-4 mb-6">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 font-medium">Tạm tính:</span>
                      <span className="font-bold text-lg">
                        {total.toLocaleString('vi-VN')}đ
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 font-medium">Phí vận chuyển:</span>
                      <span className="font-bold text-lg">
                        {shippingCost === 0 ? (
                          <span className="text-green-600">Miễn phí</span>
                        ) : (
                          `${shippingCost.toLocaleString('vi-VN')}đ`
                        )}
                      </span>
                    </div>

                    {total < shippingThreshold && (
                      <div className="text-sm text-blue-700 bg-blue-50 p-3 rounded-lg border border-blue-200">
                        Mua thêm <span className="font-bold">{(shippingThreshold - total).toLocaleString('vi-VN')}đ</span> để được miễn phí vận chuyển!
                      </div>
                    )}

                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-xl font-bold text-gray-900">Tổng cộng:</span>
                        <span className="text-2xl font-bold text-gray-900">
                          {finalTotal.toLocaleString('vi-VN')}đ
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Place Order Button */}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-semibold hover:bg-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Đang xử lý...
                      </>
                    ) : (
                      <>
                        <Check className="w-4 h-4 mr-2" />
                        Đặt hàng
                      </>
                    )}
                  </button>
                  
                  {/* Security */}
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                        Thông tin được bảo mật
                      </div>
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                        Hỗ trợ 24/7
                      </div>
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                        Đổi trả miễn phí
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </form>
        </div>
      </section>
    </main>
  );
} 