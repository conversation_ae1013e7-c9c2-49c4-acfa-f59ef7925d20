'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { motion } from 'framer-motion';
import {
  Search,
  Package,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  MapPin,
  Phone,
  Mail,
  Calendar,
  CreditCard,
  AlertCircle
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useToast } from '../../src/components/ui/Toast';
import Spinner from '../../src/components/ui/Spinner';

interface OrderData {
  orderNumber: string;
  status: string;
  statusText: string;
  paymentStatus: string;
  paymentStatusText: string;
  paymentMethod: string;
  paymentMethodText: string;
  subtotal: number;
  shippingFee: number;
  tax: number;
  discount: number;
  total: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
  deliveredAt?: string;
  estimatedDelivery?: string;
  trackingNumber?: string;
  notes?: string;
  cancelReason?: string;
  customer: {
    name: string;
    email: string;
    phone: string;
  };
  shippingAddress: any;
  items: Array<{
    id: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    productSnapshot: any;
    product?: {
      name: string;
      slug: string;
      images: string[];
      price: number;
    };
  }>;
  timeline: Array<{
    status: string;
    statusText: string;
    timestamp: string;
    description: string;
    completed: boolean;
  }>;
}

// Component that uses useSearchParams
function TraCuuDonHangContent() {
  const searchParams = useSearchParams();
  const [orderNumber, setOrderNumber] = useState('');
  const [orderData, setOrderData] = useState<OrderData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { showToast } = useToast();

  // Pre-fill order number from URL params
  useEffect(() => {
    const orderNumberFromUrl = searchParams.get('orderNumber');
    if (orderNumberFromUrl) {
      setOrderNumber(orderNumberFromUrl);
      // Auto-search if order number is provided in URL
      setTimeout(() => {
        const form = document.querySelector('form');
        if (form) {
          form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
        }
      }, 2500); // Delay to allow page loading to complete
    }
  }, [searchParams]);

  const validateOrderNumber = (value: string): boolean => {
    // Order number format: DH + timestamp + random characters
    const orderNumberRegex = /^DH\d{13}[A-Z0-9]{2,4}$/;
    return orderNumberRegex.test(value.trim().toUpperCase());
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!orderNumber.trim()) {
      setError('Vui lòng nhập mã đơn hàng');
      return;
    }

    const formattedOrderNumber = orderNumber.trim().toUpperCase();
    
    if (!validateOrderNumber(formattedOrderNumber)) {
      setError('Mã đơn hàng không đúng định dạng. Mã đơn hàng bắt đầu bằng "DH" theo sau là các số và chữ cái.');
      return;
    }

    setLoading(true);
    setError('');
    setOrderData(null);

    try {
      const response = await fetch(`/api/orders/track/${formattedOrderNumber}`);
      const result = await response.json();

      if (result.success) {
        setOrderData(result.data);
        showToast('Tìm thấy đơn hàng thành công!', 'success');
      } else {
        setError(result.error || 'Không tìm thấy đơn hàng');
        showToast(result.error || 'Không tìm thấy đơn hàng', 'error');
      }
    } catch (error) {
      console.error('Error tracking order:', error);
      setError('Có lỗi xảy ra khi tra cứu đơn hàng. Vui lòng thử lại.');
      showToast('Có lỗi xảy ra khi tra cứu đơn hàng', 'error');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'CONFIRMED':
        return <CheckCircle className="w-5 h-5 text-blue-500" />;
      case 'PROCESSING':
        return <Package className="w-5 h-5 text-orange-500" />;
      case 'SHIPPED':
        return <Truck className="w-5 h-5 text-purple-500" />;
      case 'DELIVERED':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'CANCELLED':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Package className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'CONFIRMED':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'PROCESSING':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'SHIPPED':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'DELIVERED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('vi-VN') + 'đ';
  };

  return (
    <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <section className="bg-white border-b">
          <div className="container mx-auto px-4 py-8">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Tra cứu đơn hàng
              </h1>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Nhập mã đơn hàng để kiểm tra trạng thái và thông tin chi tiết đơn hàng của bạn
              </p>
            </div>
          </div>
        </section>

      {/* Search Form */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow-sm p-6"
            >
              <form onSubmit={handleSearch} className="space-y-4">
                <div>
                  <label htmlFor="orderNumber" className="block text-sm font-medium text-gray-700 mb-2">
                    Mã đơn hàng
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="orderNumber"
                      value={orderNumber}
                      onChange={(e) => {
                        setOrderNumber(e.target.value);
                        setError('');
                      }}
                      placeholder="Nhập mã đơn hàng (VD: DH1234567890123AB)"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-12"
                      disabled={loading}
                    />
                    <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  </div>
                  {error && (
                    <p className="mt-2 text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {error}
                    </p>
                  )}
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {loading ? (
                    <>
                      <Spinner size={20} className="mr-2" />
                      Đang tra cứu...
                    </>
                  ) : (
                    <>
                      <Search className="w-5 h-5 mr-2" />
                      Tra cứu đơn hàng
                    </>
                  )}
                </button>
              </form>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Hướng dẫn tra cứu:</h3>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Mã đơn hàng được gửi qua email sau khi đặt hàng thành công</li>
                  <li>• Mã đơn hàng có định dạng: DH + 13 số + 2-4 ký tự (VD: DH1234567890123AB)</li>
                  <li>• Liên hệ hotline nếu cần hỗ trợ: 1900-xxxx</li>
                </ul>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Order Results */}
      {orderData && (
        <section className="pb-12">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto space-y-6">
              {/* Order Status */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-lg shadow-sm p-6"
              >
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                      Đơn hàng #{orderData.orderNumber}
                    </h2>
                    <p className="text-gray-600">
                      Đặt hàng lúc: {formatDate(orderData.createdAt)}
                    </p>
                  </div>
                  <div className={`px-4 py-2 rounded-full border ${getStatusColor(orderData.status)}`}>
                    <div className="flex items-center">
                      {getStatusIcon(orderData.status)}
                      <span className="ml-2 font-medium">{orderData.statusText}</span>
                    </div>
                  </div>
                </div>

                {/* Order Timeline */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Tiến trình đơn hàng</h3>
                  <div className="space-y-4">
                    {orderData.timeline.map((step, index) => (
                      <div key={index} className="flex items-start">
                        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                          step.completed ? 'bg-green-100' : 'bg-gray-100'
                        }`}>
                          {step.completed ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : (
                            <div className="w-3 h-3 bg-gray-400 rounded-full" />
                          )}
                        </div>
                        <div className="ml-4 flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className={`font-medium ${step.completed ? 'text-gray-900' : 'text-gray-500'}`}>
                              {step.statusText}
                            </h4>
                            <span className="text-sm text-gray-500">
                              {formatDate(step.timestamp)}
                            </span>
                          </div>
                          <p className={`text-sm ${step.completed ? 'text-gray-600' : 'text-gray-400'}`}>
                            {step.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>

              {/* Order Details */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white rounded-lg shadow-sm p-6"
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">Chi tiết đơn hàng</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Customer Info */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Thông tin khách hàng</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center">
                        <span className="text-gray-600 w-20">Họ tên:</span>
                        <span className="text-gray-900">{orderData.customer.name}</span>
                      </div>
                      <div className="flex items-center">
                        <Mail className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-gray-600 w-16">Email:</span>
                        <span className="text-gray-900">{orderData.customer.email}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-gray-600 w-16">SĐT:</span>
                        <span className="text-gray-900">{orderData.customer.phone}</span>
                      </div>
                    </div>
                  </div>

                  {/* Shipping Address */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Địa chỉ giao hàng</h4>
                    <div className="flex items-start">
                      <MapPin className="w-4 h-4 text-gray-400 mr-2 mt-0.5" />
                      <div className="text-sm text-gray-900">
                        <p>{orderData.shippingAddress.fullName}</p>
                        <p>{orderData.shippingAddress.address}</p>
                        <p>
                          {orderData.shippingAddress.ward}, {orderData.shippingAddress.district}
                        </p>
                        <p>{orderData.shippingAddress.province}</p>
                        <p>{orderData.shippingAddress.phone}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Info */}
                <div className="mt-6 pt-6 border-t">
                  <h4 className="font-medium text-gray-900 mb-3">Thông tin thanh toán</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center">
                      <CreditCard className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-gray-600">Phương thức:</span>
                      <span className="ml-2 text-gray-900">{orderData.paymentMethodText}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-gray-600">Trạng thái:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-xs ${
                        orderData.paymentStatus === 'PAID' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {orderData.paymentStatusText}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Tracking Number */}
                {orderData.trackingNumber && (
                  <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center">
                      <Truck className="w-5 h-5 text-blue-600 mr-2" />
                      <span className="text-blue-900 font-medium">Mã vận đơn: </span>
                      <span className="text-blue-700 font-mono">{orderData.trackingNumber}</span>
                    </div>
                  </div>
                )}

                {/* Estimated Delivery */}
                {orderData.estimatedDelivery && orderData.status !== 'DELIVERED' && orderData.status !== 'CANCELLED' && (
                  <div className="mt-4 p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center">
                      <Calendar className="w-5 h-5 text-green-600 mr-2" />
                      <span className="text-green-900 font-medium">Dự kiến giao hàng: </span>
                      <span className="text-green-700">{formatDate(orderData.estimatedDelivery)}</span>
                    </div>
                  </div>
                )}

                {/* Delivered Date */}
                {orderData.deliveredAt && orderData.status === 'DELIVERED' && (
                  <div className="mt-4 p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                      <span className="text-green-900 font-medium">Đã giao hàng lúc: </span>
                      <span className="text-green-700">{formatDate(orderData.deliveredAt)}</span>
                    </div>
                  </div>
                )}

                {/* Cancel Reason */}
                {orderData.cancelReason && orderData.status === 'CANCELLED' && (
                  <div className="mt-4 p-4 bg-red-50 rounded-lg">
                    <div className="flex items-start">
                      <XCircle className="w-5 h-5 text-red-600 mr-2 mt-0.5" />
                      <div>
                        <span className="text-red-900 font-medium">Lý do hủy đơn: </span>
                        <p className="text-red-700 mt-1">{orderData.cancelReason}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Order Notes */}
                {orderData.notes && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-start">
                      <AlertCircle className="w-5 h-5 text-gray-600 mr-2 mt-0.5" />
                      <div>
                        <span className="text-gray-900 font-medium">Ghi chú: </span>
                        <p className="text-gray-700 mt-1">{orderData.notes}</p>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>

              {/* Order Items */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white rounded-lg shadow-sm p-6"
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">Sản phẩm đã đặt</h3>
                
                <div className="space-y-4">
                  {orderData.items.map((item, index) => {
                    // Use productSnapshot for more accurate historical data
                    const productSnapshot = item.productSnapshot ? JSON.parse(JSON.stringify(item.productSnapshot)) : null;
                    const displayImage = productSnapshot?.images?.[0] || item.product?.images?.[0];
                    const displayName = item.productName;

                    return (
                      <div key={index} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                        {displayImage && (
                          <div className="flex-shrink-0">
                            <Image
                              src={displayImage}
                              alt={displayName}
                              width={80}
                              height={80}
                              className="rounded-lg object-cover"
                            />
                          </div>
                        )}

                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {displayName}
                          </h4>
                          <div className="mt-1 text-sm text-gray-600 space-y-1">
                            <p>Số lượng: {item.quantity}</p>
                            <p>Đơn giá: {formatCurrency(item.unitPrice)}</p>

                            {/* Show size and color from productSnapshot if available */}
                            {productSnapshot?.size && (
                              <p>Kích thước: <span className="font-medium">{productSnapshot.size}</span></p>
                            )}
                            {productSnapshot?.color && (
                              <p>Màu sắc: <span className="font-medium">{productSnapshot.color}</span></p>
                            )}

                            {/* Show SKU if available from productSnapshot */}
                            {productSnapshot?.sku && (
                              <p className="text-xs text-gray-500">SKU: {productSnapshot.sku}</p>
                            )}
                          </div>
                        </div>

                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">
                            {formatCurrency(item.totalPrice)}
                          </p>
                          {item.quantity > 1 && (
                            <p className="text-xs text-gray-500">
                              {formatCurrency(item.unitPrice)} x {item.quantity}
                            </p>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Order Summary */}
                <div className="mt-6 pt-6 border-t">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tạm tính:</span>
                      <span className="text-gray-900">{formatCurrency(orderData.subtotal)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Phí vận chuyển:</span>
                      <span className="text-gray-900">{formatCurrency(orderData.shippingFee)}</span>
                    </div>
                    {orderData.discount > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Giảm giá:</span>
                        <span className="text-red-600">-{formatCurrency(orderData.discount)}</span>
                      </div>
                    )}
                    <div className="flex justify-between text-lg font-semibold pt-2 border-t">
                      <span className="text-gray-900">Tổng cộng:</span>
                      <span className="text-gray-900">{formatCurrency(orderData.total)}</span>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Actions */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-center"
              >
                <Link
                  href="/"
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Tiếp tục mua sắm
                </Link>
              </motion.div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
}

// Main page component with Suspense wrapper
export default function TraCuuDonHangPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Spinner size={32} className="mx-auto mb-4" />
          <p className="text-gray-600">Đang tải trang tra cứu...</p>
        </div>
      </div>
    }>
      <TraCuuDonHangContent />
    </Suspense>
  );
}
