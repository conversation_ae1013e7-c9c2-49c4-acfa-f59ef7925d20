'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Heart } from 'lucide-react';
import { useWishlist } from '../../src/contexts/WishlistContext';
import ProductCard from '../../src/components/ui/ProductCard';

export default function YeuThichPage() {
  const { items: wishlistItems } = useWishlist();

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-red-50 to-pink-50 py-20">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center justify-center mb-6">
              <Heart className="w-12 h-12 text-red-500 mr-4" />
              <h1 className="text-5xl md:text-7xl font-bold text-gray-900">
                <PERSON><PERSON><PERSON><PERSON>
              </h1>
            </div>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Nh<PERSON>ng sản phẩm bạn đã lưu để mua sau
            </p>
          </motion.div>
        </div>
      </section>

      {/* Wishlist Items */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          {wishlistItems.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-12"
            >
              <Heart className="w-24 h-24 text-gray-300 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Danh sách yêu thích trống
              </h2>
              <p className="text-gray-600 mb-8">
                Bạn chưa có sản phẩm nào trong danh sách yêu thích
              </p>
              <a
                href="/san-pham"
                className="bg-black text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors"
              >
                Khám Phá Sản Phẩm
              </a>
            </motion.div>
          ) : (
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center mb-12"
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Sản Phẩm Yêu Thích Của Bạn
                </h2>
                <p className="text-gray-600">
                  Bạn có {wishlistItems.length} sản phẩm trong danh sách yêu thích
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {wishlistItems.map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <ProductCard product={item.product} />
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </div>
      </section>
    </main>
  );
}
