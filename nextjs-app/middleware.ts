import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Admin routes protection
  if (pathname.startsWith('/admin')) {
    // Allow access to login page
    if (pathname === '/admin/dang-nhap') {
      return NextResponse.next();
    }

    // Check for admin token
    const token = request.cookies.get('admin-token')?.value;

    console.log('🔍 Middleware Debug:', {
      pathname,
      hasToken: !!token,
      tokenPreview: token ? token.substring(0, 20) + '...' : 'none',
      allCookies: request.cookies.getAll().map(c => c.name)
    });

    if (!token) {
      console.log('❌ No token found, redirecting to login');
      return NextResponse.redirect(new URL('/admin/dang-nhap', request.url));
    }

    // For Edge Runtime, we'll do basic token validation
    // Full JWT verification will be done in API routes
    try {
      // Basic token format check (JWT has 3 parts separated by dots)
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid token format');
      }

      // Try to decode the payload (without verification for now)
      const payload = JSON.parse(atob(parts[1]));

      // Check if token is expired
      if (payload.exp && payload.exp < Date.now() / 1000) {
        throw new Error('Token expired');
      }

      console.log('✅ Token format valid, allowing access');
      return NextResponse.next();
    } catch (error) {
      console.log('❌ Token validation failed:', error);
      // Invalid token, redirect to login
      const response = NextResponse.redirect(new URL('/admin/dang-nhap', request.url));
      response.cookies.delete('admin-token');
      return response;
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/admin/:path*',
  ],
};
