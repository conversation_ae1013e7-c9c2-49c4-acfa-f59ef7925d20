/** @type {import('next').NextConfig} */
const nextConfig={
  reactStrictMode: true,
  output: 'standalone', // Enable standalone output for Docker
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  // Optimize for Vercel deployment
  serverExternalPackages: ['@prisma/client','prisma'],
  // Ensure Prisma Client is bundled correctly
  webpack: (config,{isServer}) => {
    if(isServer) {
      config.externals.push('@prisma/client')
    }
    return config
  },
}

module.exports=nextConfig
