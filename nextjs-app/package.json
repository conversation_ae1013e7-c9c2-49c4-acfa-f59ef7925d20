{"name": "nextjs-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "vercel-postinstall": "node scripts/vercel-postinstall.js", "db:seed": "tsx prisma/seed.ts", "create-admin": "node scripts/create-admin.js", "db:migrate": "npx prisma migrate dev", "db:reset": "npx prisma migrate reset", "vercel-build": "npx prisma generate && next build", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^6.8.2", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "express": "^5.1.0", "framer-motion": "^12.15.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mongodb": "^6.17.0", "mongoose": "^8.15.1", "multer": "^2.0.1", "next": "15.3.3", "node-fetch": "^2.7.0", "prisma": "^6.8.2", "react": "^18.3.1", "react-dom": "^18.3.1", "swr": "^2.3.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.13", "@types/node": "^20", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "tsx": "^4.19.4", "typescript": "^5"}}