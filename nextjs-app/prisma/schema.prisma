// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
    binaryTargets = ["native", "linux-musl-openssl-3.0.x", "linux-musl-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

// Admin Users Model
model AdminUser {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  email       String   @unique
  name        String
  password    String
  role        AdminRole @default(ADMIN)
  permissions String[]
  isActive    Boolean  @default(true)
  lastLogin   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  sessions    AdminSession[]
  logs        AdminLog[]

  @@map("admin_users")
}

enum AdminRole {
  SUPER_ADMIN
  ADMIN
  MODERATOR
}

// Customer Users Model
model User {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  email       String   @unique
  name        String?
  password    String?
  phone       String?
  avatar      String?
  dateOfBirth DateTime?
  gender      Gender?
  isActive    Boolean  @default(true)
  emailVerified Boolean @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  orders      Order[]
  reviews     Review[]
  wishlist    WishlistItem[]
  cart        CartItem[]
  addresses   Address[]

  @@map("users")
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

// Address Model - Updated to 2-level addressing (Province -> Ward)
model Address {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  userId      String  @db.ObjectId
  name        String
  phone       String
  address     String
  ward        String
  district    String? // Optional for backward compatibility
  city        String  // Now represents province/city
  isDefault   Boolean @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user        User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders      Order[]

  @@map("addresses")
}

// Category Model with Hierarchical Structure
model Category {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  slug        String    @unique
  description String?
  image       String?
  parentId    String?   @db.ObjectId
  level       Int       @default(0)
  sortOrder   Int       @default(0)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  children    Category[] @relation("CategoryHierarchy")
  productCategories ProductCategory[]
  products          Product[]         @relation("ProductPrimaryCategory")

  @@map("categories")
}

// Product Model with Variants and Detailed Information
model Product {
  id            String        @id @default(auto()) @map("_id") @db.ObjectId
  originalId    String?       // Store original ID from JSON for SEO
  name          String
  slug          String        @unique
  description   String?
  shortDescription String?
  price         Float
  originalPrice Float?
  isOnSale      Boolean       @default(false)
  stock         Int           @default(0)
  sku           String?       @unique
  barcode       String?
  weight        Float?
  dimensions    ProductDimensions?
  images        String[]      // Array of image URLs
  brand         String?
  tags          String[]
  featured      Boolean       @default(false)
  status        ProductStatus @default(ACTIVE)
  seoTitle      String?
  seoDescription String?
  seoKeywords   String[]
  colors        String[]      // Product colors
  sizes         String[]      // Product sizes
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations - Many-to-Many with Categories
  productCategories ProductCategory[]

  // Direct category relationship for primary category
  categoryId        String?   @db.ObjectId
  category          Category? @relation("ProductPrimaryCategory", fields: [categoryId], references: [id])
  variants      ProductVariant[]
  orderItems    OrderItem[]
  reviews       Review[]
  wishlistItems WishlistItem[]
  cartItems     CartItem[]

  @@map("products")
}

// Many-to-Many Junction Table for Product-Category Relationship
model ProductCategory {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  productId  String   @db.ObjectId
  categoryId String   @db.ObjectId
  createdAt  DateTime @default(now())

  // Relations
  product    Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@unique([productId, categoryId])
  @@map("product_categories")
}

// Product Variants (Size, Color, etc.)
model ProductVariant {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  productId   String  @db.ObjectId
  name        String  // e.g., "Size S - Red"
  sku         String? @unique
  price       Float?  // Override product price if different
  stock       Int     @default(0)
  attributes  Json    // { "size": "S", "color": "Red" }
  images      String[] // Variant-specific images
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  product     Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_variants")
}

// Embedded type for product dimensions
type ProductDimensions {
  length Float
  width  Float
  height Float
  unit   String // cm, inch, etc.
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  DRAFT
  OUT_OF_STOCK
}

// Order Model with Complete Information
model Order {
  id              String        @id @default(auto()) @map("_id") @db.ObjectId
  orderNumber     String        @unique
  userId          String        @db.ObjectId
  status          OrderStatus   @default(PENDING)
  paymentStatus   PaymentStatus @default(PENDING)
  paymentMethod   PaymentMethod?
  subtotal        Float
  shippingFee     Float         @default(0)
  tax             Float         @default(0)
  discount        Float         @default(0)
  total           Float
  currency        String        @default("VND")

  // Shipping Information
  shippingAddressId String?     @db.ObjectId
  shippingAddress   Json?       // Snapshot of address at time of order
  shippingMethod    String?
  trackingNumber    String?
  estimatedDelivery DateTime?
  deliveredAt       DateTime?

  // Additional Information
  notes           String?
  adminNotes      String?
  cancelReason    String?
  refundAmount    Float?
  refundedAt      DateTime?

  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  user            User          @relation(fields: [userId], references: [id])
  shippingAddr    Address?      @relation(fields: [shippingAddressId], references: [id])
  orderItems      OrderItem[]
  orderHistory    OrderHistory[]

  @@map("orders")
}

// Order Items with Detailed Information
model OrderItem {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  orderId     String  @db.ObjectId
  productId   String  @db.ObjectId
  variantId   String? @db.ObjectId
  productName String  // Snapshot at time of order
  variantName String? // Snapshot at time of order
  sku         String?
  quantity    Int
  unitPrice   Float
  totalPrice  Float
  productSnapshot Json // Complete product data at time of order

  // Relations
  order       Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product     Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Order Status History
model OrderHistory {
  id          String      @id @default(auto()) @map("_id") @db.ObjectId
  orderId     String      @db.ObjectId
  status      OrderStatus
  notes       String?
  createdBy   String?     // Admin user ID
  createdAt   DateTime    @default(now())

  // Relations
  order       Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("order_history")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum PaymentMethod {
  COD
  BANK_TRANSFER
  CREDIT_CARD
  E_WALLET
  PAYPAL
}

// Review Model
model Review {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String?  @db.ObjectId // Optional for imported reviews
  productId   String   @db.ObjectId
  rating      Int      // 1-5 stars
  title       String?
  comment     String?
  images      String[] // Review images
  author      String?  // Author name for imported reviews
  reviewTime  String?  // Original review time string
  isVerified  Boolean  @default(false)
  isApproved  Boolean  @default(true) // Auto-approve imported reviews
  helpfulCount Int     @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user        User?    @relation(fields: [userId], references: [id])
  product     Product  @relation(fields: [productId], references: [id])

  @@map("reviews")
}

// Wishlist Model
model WishlistItem {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  productId String   @db.ObjectId
  createdAt DateTime @default(now())

  // Relations
  user      User     @relation(fields: [userId], references: [id])
  product   Product  @relation(fields: [productId], references: [id])

  @@unique([userId, productId])
  @@map("wishlist_items")
}

// Cart Model
model CartItem {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  productId String   @db.ObjectId
  variantId String?  @db.ObjectId
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user      User     @relation(fields: [userId], references: [id])
  product   Product  @relation(fields: [productId], references: [id])

  @@unique([userId, productId, variantId])
  @@map("cart_items")
}

// Newsletter Model
model Newsletter {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  email     String   @unique
  name      String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())

  @@map("newsletter")
}

// Admin Session Model
model AdminSession {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  adminId   String   @db.ObjectId
  token     String   @unique
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Relations
  admin     AdminUser @relation(fields: [adminId], references: [id], onDelete: Cascade)

  @@map("admin_sessions")
}

// Admin Activity Log Model
model AdminLog {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  adminId    String   @db.ObjectId
  action     String   // CREATE, UPDATE, DELETE, LOGIN, LOGOUT, VIEW
  resource   String   // PRODUCT, CATEGORY, ORDER, USER, SETTINGS
  resourceId String?
  details    Json?    // Additional details about the action
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())

  // Relations
  admin      AdminUser @relation(fields: [adminId], references: [id])

  @@map("admin_logs")
}

// System Settings Model
model SystemSettings {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  key         String   @unique
  value       String
  type        SettingType @default(STRING)
  description String?
  category    String?
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("system_settings")
}

// Coupon/Discount Model
model Coupon {
  id              String      @id @default(auto()) @map("_id") @db.ObjectId
  code            String      @unique
  name            String
  description     String?
  type            CouponType
  value           Float       // Percentage or fixed amount
  minOrderAmount  Float?
  maxDiscount     Float?
  usageLimit      Int?
  usedCount       Int         @default(0)
  isActive        Boolean     @default(true)
  startsAt        DateTime?
  expiresAt       DateTime?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("coupons")
}

enum SettingType {
  STRING
  NUMBER
  BOOLEAN
  JSON
  EMAIL
  URL
}

enum CouponType {
  PERCENTAGE
  FIXED_AMOUNT
  FREE_SHIPPING
}

// Additional enums are defined above with their respective models
