import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12);

  const admin = await prisma.adminUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin ThinLuong',
      password: hashedPassword,
      role: 'SUPER_ADMIN',
      permissions: ['ALL'],
      isActive: true,
    },
  });

  console.log('✅ Admin user created:', admin.email);

  // Create categories
  const categories = [
    {
      name: 'Áo Nam',
      slug: 'ao-nam',
      description: '<PERSON>ác loại áo dành cho nam giới',
      level: 0,
      sortOrder: 1,
      isActive: true,
    },
    {
      name: '<PERSON><PERSON>',
      slug: 'ao-nu',
      description: '<PERSON><PERSON><PERSON> loại áo dành cho nữ giới',
      level: 0,
      sortOrder: 2,
      isActive: true,
    },
    {
      name: 'Quần Nam',
      slug: 'quan-nam',
      description: 'Các loại quần dành cho nam giới',
      level: 0,
      sortOrder: 3,
      isActive: true,
    },
    {
      name: 'Quần Nữ',
      slug: 'quan-nu',
      description: 'Các loại quần dành cho nữ giới',
      level: 0,
      sortOrder: 4,
      isActive: true,
    },
  ];

  const createdCategories = [];
  for (const category of categories) {
    const created = await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    });
    createdCategories.push(created);
    console.log('✅ Category created:', created.name);
  }

  // Create sample products
  const products = [
    {
      name: 'Áo Thun Basic Nam',
      slug: 'ao-thun-basic-nam',
      description: 'Áo thun basic chất liệu cotton 100%, thoải mái và bền đẹp',
      shortDescription: 'Áo thun nam basic, thoải mái',
      price: 199000,
      originalPrice: 299000,
      isOnSale: true,
      stock: 50,
      sku: 'ATN001',
      images: ['/images/products/ao-thun-nam-1.jpg'],
      categoryId: createdCategories[0].id, // Áo Nam
      brand: 'ThinLuong',
      tags: ['basic', 'cotton', 'nam'],
      featured: true,
      status: 'ACTIVE',
      seoTitle: 'Áo Thun Basic Nam - ThinLuong Fashion',
      seoDescription: 'Áo thun nam basic chất liệu cotton 100%, thoải mái, phù hợp mọi hoạt động',
      seoKeywords: ['áo thun nam', 'basic', 'cotton'],
    },
    {
      name: 'Áo Thun Nữ Vintage',
      slug: 'ao-thun-nu-vintage',
      description: 'Áo thun nữ phong cách vintage, trendy và thời trang',
      shortDescription: 'Áo thun nữ vintage trendy',
      price: 249000,
      originalPrice: 349000,
      isOnSale: true,
      stock: 40,
      sku: 'ATN002',
      images: ['/images/products/ao-thun-nu-1.jpg'],
      categoryId: createdCategories[1].id, // Áo Nữ
      brand: 'ThinLuong',
      tags: ['vintage', 'nữ', 'trendy'],
      featured: true,
      status: 'ACTIVE',
      seoTitle: 'Áo Thun Nữ Vintage - ThinLuong Fashion',
      seoDescription: 'Áo thun nữ phong cách vintage, trendy và thời trang',
      seoKeywords: ['áo thun nữ', 'vintage', 'thời trang'],
    },
    {
      name: 'Quần Jean Nam Slim Fit',
      slug: 'quan-jean-nam-slim-fit',
      description: 'Quần jean nam slim fit, form dáng đẹp, chất liệu denim cao cấp',
      shortDescription: 'Quần jean nam slim fit',
      price: 599000,
      originalPrice: 799000,
      isOnSale: true,
      stock: 25,
      sku: 'QJN001',
      images: ['/images/products/quan-jean-nam-1.jpg'],
      categoryId: createdCategories[2].id, // Quần Nam
      brand: 'ThinLuong',
      tags: ['jean', 'slim fit', 'nam'],
      featured: true,
      status: 'ACTIVE',
      seoTitle: 'Quần Jean Nam Slim Fit - ThinLuong Fashion',
      seoDescription: 'Quần jean nam slim fit, form dáng đẹp, chất liệu denim cao cấp',
      seoKeywords: ['quần jean nam', 'slim fit', 'denim'],
    },
  ];

  for (const product of products) {
    const created = await prisma.product.upsert({
      where: { slug: product.slug },
      update: {},
      create: product,
    });
    console.log('✅ Product created:', created.name);
  }

  // Create sample customer
  const customerPassword = await bcrypt.hash('customer123', 12);
  const customer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Khách Hàng Mẫu',
      password: customerPassword,
      phone: '0123456789',
      isActive: true,
      emailVerified: true,
    },
  });

  console.log('✅ Customer created:', customer.email);

  // Create system settings
  const settings = [
    { key: 'site_name', value: 'ThinLuong Fashion Store', type: 'STRING', description: 'Tên website' },
    { key: 'site_description', value: 'Cửa hàng thời trang ThinLuong', type: 'STRING', description: 'Mô tả website' },
    { key: 'contact_email', value: '<EMAIL>', type: 'EMAIL', description: 'Email liên hệ' },
    { key: 'contact_phone', value: '0123456789', type: 'STRING', description: 'Số điện thoại liên hệ' },
    { key: 'shipping_fee', value: '30000', type: 'NUMBER', description: 'Phí vận chuyển mặc định' },
    { key: 'free_shipping_threshold', value: '500000', type: 'NUMBER', description: 'Ngưỡng miễn phí vận chuyển' },
  ];

  for (const setting of settings) {
    await prisma.systemSettings.upsert({
      where: { key: setting.key },
      update: {},
      create: setting,
    });
    console.log('✅ Setting created:', setting.key);
  }

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
