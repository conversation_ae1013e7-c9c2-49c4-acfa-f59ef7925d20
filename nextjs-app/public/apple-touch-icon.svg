<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with rounded corners for iOS -->
  <rect width="180" height="180" rx="40" fill="url(#gradient)" />
  
  <!-- Letter T - larger and centered -->
  <path d="M45 50h90v17h-36.5v63h-17V67H45V50z" fill="white" />
  
  <!-- Fashion accent - hanger icon -->
  <path d="M125 35c3 0 5 2 5 5s-2 5-5 5h-5v8c0 1.5-1 2.5-2.5 2.5s-2.5-1-2.5-2.5v-8h-5c-3 0-5-2-5-5s2-5 5-5h15z" fill="white" opacity="0.9" />
  
  <!-- Small decorative elements -->
  <circle cx="60" cy="140" r="3" fill="white" opacity="0.6" />
  <circle cx="120" cy="140" r="3" fill="white" opacity="0.6" />
  <path d="M85 135h10v2h-10v-2z" fill="white" opacity="0.6" />
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
