<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="96" cy="96" r="96" fill="url(#gradient)" />
  
  <!-- Letter T - proportional to size -->
  <path d="M48 54h96v18h-39v67h-18V72H48V54z" fill="white" />
  
  <!-- Fashion accent - hanger icon -->
  <path d="M132 42c3 0 6 3 6 6s-3 6-6 6h-6v9c0 1.5-1.5 3-3 3s-3-1.5-3-3v-9h-6c-3 0-6-3-6-6s3-6 6-6h18z" fill="white" opacity="0.9" />
  
  <!-- Decorative elements -->
  <circle cx="72" cy="150" r="4" fill="white" opacity="0.7" />
  <circle cx="120" cy="150" r="4" fill="white" opacity="0.7" />
  <path d="M88 144h16v3h-16v-3z" fill="white" opacity="0.7" />
  
  <!-- Small fashion icons -->
  <path d="M60 36c0-2 2-4 4-4s4 2 4 4v6h-8v-6z" fill="white" opacity="0.6" />
  <path d="M124 36c0-2 2-4 4-4s4 2 4 4v6h-8v-6z" fill="white" opacity="0.6" />
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
