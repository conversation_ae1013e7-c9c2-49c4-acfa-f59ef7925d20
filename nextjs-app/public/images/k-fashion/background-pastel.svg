<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="gradient-pastel" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#ffeaa7;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#fab1a0;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#fd79a8;stop-opacity:1" />
      </linearGradient>
      <filter id="glow">
        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
        <feMerge> 
          <feMergeNode in="coloredBlur"/>
          <feMergeNode in="SourceGraphic"/>
        </feMerge>
      </filter>
    </defs>
    
    <!-- Background -->
    <rect width="100%" height="100%" fill="url(#gradient-pastel)"/>
    
    <!-- Floating hearts -->
    <g opacity="0.3">
      <path d="M150,100 C150,80 130,60 110,60 C90,60 70,80 70,100 C70,120 110,160 110,160 C110,160 150,120 150,100 Z" fill="white"/>
      <path d="M350,200 C350,180 330,160 310,160 C290,160 270,180 270,200 C270,220 310,260 310,260 C310,260 350,220 350,200 Z" fill="white"/>
      <path d="M550,150 C550,130 530,110 510,110 C490,110 470,130 470,150 C470,170 510,210 510,210 C510,210 550,170 550,150 Z" fill="white"/>
    </g>
    
    <!-- Floating stars -->
    <g opacity="0.4" filter="url(#glow)">
      <polygon points="200,50 210,80 240,80 218,98 228,128 200,110 172,128 182,98 160,80 190,80" fill="white"/>
      <polygon points="600,300 610,330 640,330 618,348 628,378 600,360 572,378 582,348 560,330 590,330" fill="white"/>
      <polygon points="100,400 110,430 140,430 118,448 128,478 100,460 72,478 82,448 60,430 90,430" fill="white"/>
    </g>
    
    <!-- Korean-style text placeholder -->
    <g opacity="0.6">
      <text x="400" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="white">K-FASHION</text>
      <text x="400" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="white" opacity="0.8">Korean Style</text>
    </g>
    
    <!-- Decorative elements -->
    <g opacity="0.2">
      <circle cx="100" cy="300" r="50" fill="white"/>
      <circle cx="700" cy="100" r="30" fill="white"/>
      <circle cx="650" cy="500" r="40" fill="white"/>
    </g>
  </svg>