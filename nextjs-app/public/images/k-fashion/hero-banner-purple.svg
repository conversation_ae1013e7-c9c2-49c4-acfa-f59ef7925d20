<svg width="1200" height="400" viewBox="0 0 1200 400" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="banner-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#a8edea;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#fed6e3;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#d299c2;stop-opacity:1" />
      </linearGradient>
      <filter id="shadow">
        <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
      </filter>
    </defs>
    
    <!-- Background -->
    <rect width="100%" height="100%" fill="url(#banner-gradient)"/>
    
    <!-- Pattern overlay -->
    <g opacity="0.1">
      <circle cx="120" cy="50" r="30" fill="white"/><circle cx="360" cy="50" r="30" fill="white"/><circle cx="600" cy="50" r="30" fill="white"/><circle cx="840" cy="50" r="30" fill="white"/><circle cx="1080" cy="50" r="30" fill="white"/><circle cx="120" cy="150" r="30" fill="white"/><circle cx="360" cy="150" r="30" fill="white"/><circle cx="600" cy="150" r="30" fill="white"/><circle cx="840" cy="150" r="30" fill="white"/><circle cx="1080" cy="150" r="30" fill="white"/><circle cx="120" cy="250" r="30" fill="white"/><circle cx="360" cy="250" r="30" fill="white"/><circle cx="600" cy="250" r="30" fill="white"/><circle cx="840" cy="250" r="30" fill="white"/><circle cx="1080" cy="250" r="30" fill="white"/><circle cx="120" cy="350" r="30" fill="white"/><circle cx="360" cy="350" r="30" fill="white"/><circle cx="600" cy="350" r="30" fill="white"/><circle cx="840" cy="350" r="30" fill="white"/><circle cx="1080" cy="350" r="30" fill="white"/>
    </g>
    
    <!-- Main title -->
    <text x="600" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="64" font-weight="900" fill="white" filter="url(#shadow)">K-FASHION COLLECTION</text>
    <text x="600" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="white" opacity="0.9">✨ Phong Cách Idol Hàn Quốc ✨</text>
    
    <!-- Decorative hearts -->
    <g opacity="0.6">
      <path d="M100,100 C100,80 80,60 60,60 C40,60 20,80 20,100 C20,120 60,160 60,160 C60,160 100,120 100,100 Z" fill="white"/>
      <path d="1120,100 C1120,80 1100,60 1080,60 C1060,60 1040,80 1040,100 C1040,120 1080,160 1080,160 C1080,160 1120,120 1120,100 Z" fill="white"/>
    </g>
  </svg>