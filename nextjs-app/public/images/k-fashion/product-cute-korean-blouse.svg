<svg width="400" height="533" viewBox="0 0 400 533" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="product-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#ff6b9d;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#c44569;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#f8b500;stop-opacity:1" />
      </linearGradient>
    </defs>
    
    <!-- Background -->
    <rect width="100%" height="100%" fill="url(#product-gradient)"/>
    
    <!-- Fashion silhouette -->
    <g opacity="0.3" transform="translate(200, 266.5)">
      <!-- Simple dress/top silhouette -->
      <path d="M-60,-100 L-40,-100 L-30,-80 L-20,-60 L-15,-40 L-10,0 L-15,40 L-20,80 L-30,100 L30,100 L20,80 L15,40 L10,0 L15,-40 L20,-60 L30,-80 L40,-100 L60,-100 L50,-120 L-50,-120 Z" fill="white"/>
    </g>
    
    <!-- Korean text -->
    <text x="200" y="473" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">Cute Korean Blouse</text>
    <text x="200" y="503" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white" opacity="0.8">Korean Style</text>
    
    <!-- Decorative elements -->
    <g opacity="0.4">
      <circle cx="50" cy="50" r="20" fill="white"/>
      <circle cx="350" cy="50" r="15" fill="white"/>
      <circle cx="50" cy="483" r="15" fill="white"/>
      <circle cx="350" cy="483" r="20" fill="white"/>
    </g>
  </svg>