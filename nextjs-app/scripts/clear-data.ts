import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function clearData() {
  console.log('🧹 Clearing existing data...');
  
  try {
    // Delete in correct order to avoid foreign key constraints
    console.log('Deleting reviews...');
    await prisma.review.deleteMany({});
    
    console.log('Deleting product categories...');
    await prisma.productCategory.deleteMany({});
    
    console.log('Deleting product variants...');
    await prisma.productVariant.deleteMany({});
    
    console.log('Deleting cart items...');
    await prisma.cartItem.deleteMany({});
    
    console.log('Deleting wishlist items...');
    await prisma.wishlistItem.deleteMany({});
    
    console.log('Deleting order items...');
    await prisma.orderItem.deleteMany({});
    
    console.log('Deleting products...');
    await prisma.product.deleteMany({});
    
    console.log('Deleting categories...');
    await prisma.category.deleteMany({});
    
    console.log('✅ Data cleared successfully!');
    
  } catch (error) {
    console.error('❌ Error clearing data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

clearData();
