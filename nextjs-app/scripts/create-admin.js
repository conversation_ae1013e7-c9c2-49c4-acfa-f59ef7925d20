const {PrismaClient}=require('@prisma/client');
const bcrypt=require('bcryptjs');

const prisma=new PrismaClient();

async function createAdmin() {
  try {
    const hashedPassword=await bcrypt.hash('admin123',12);

    const admin=await prisma.adminUser.upsert({
      where: {email: '<EMAIL>'},
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Test Admin',
        password: hashedPassword,
        role: 'ADMIN',
        isActive: true,
      },
    });

    console.log('✅ Admin created successfully:',{
      id: admin.id,
      email: admin.email,
      name: admin.name,
      role: admin.role,
    });

    console.log('\n📋 Login credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');

  } catch(error) {
    console.error('❌ Error creating admin:',error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin();
