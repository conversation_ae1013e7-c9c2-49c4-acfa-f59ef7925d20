/**
 * <PERSON><PERSON><PERSON> to create K-Fashion themed placeholder images
 * This script generates SVG placeholders with Korean fashion aesthetics
 */

const fs = require('fs');
const path = require('path');

// Ensure images directory exists
const imagesDir = path.join(__dirname, '../public/images/k-fashion');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// K-Fashion color palettes
const colorPalettes = {
  pink: ['#ff6b9d', '#c44569', '#f8b500'],
  blue: ['#4facfe', '#00f2fe', '#667eea'],
  purple: ['#a8edea', '#fed6e3', '#d299c2'],
  sunset: ['#ff9a9e', '#fecfef', '#fecfef'],
  pastel: ['#ffeaa7', '#fab1a0', '#fd79a8']
};

// Create gradient SVG background
function createGradientSVG(colors, width = 800, height = 600, name = 'k-fashion-bg') {
  const gradientId = `gradient-${name}`;
  
  return `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="${gradientId}" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:${colors[0]};stop-opacity:1" />
        <stop offset="50%" style="stop-color:${colors[1]};stop-opacity:1" />
        <stop offset="100%" style="stop-color:${colors[2]};stop-opacity:1" />
      </linearGradient>
      <filter id="glow">
        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
        <feMerge> 
          <feMergeNode in="coloredBlur"/>
          <feMergeNode in="SourceGraphic"/>
        </feMerge>
      </filter>
    </defs>
    
    <!-- Background -->
    <rect width="100%" height="100%" fill="url(#${gradientId})"/>
    
    <!-- Floating hearts -->
    <g opacity="0.3">
      <path d="M150,100 C150,80 130,60 110,60 C90,60 70,80 70,100 C70,120 110,160 110,160 C110,160 150,120 150,100 Z" fill="white"/>
      <path d="M350,200 C350,180 330,160 310,160 C290,160 270,180 270,200 C270,220 310,260 310,260 C310,260 350,220 350,200 Z" fill="white"/>
      <path d="M550,150 C550,130 530,110 510,110 C490,110 470,130 470,150 C470,170 510,210 510,210 C510,210 550,170 550,150 Z" fill="white"/>
    </g>
    
    <!-- Floating stars -->
    <g opacity="0.4" filter="url(#glow)">
      <polygon points="200,50 210,80 240,80 218,98 228,128 200,110 172,128 182,98 160,80 190,80" fill="white"/>
      <polygon points="600,300 610,330 640,330 618,348 628,378 600,360 572,378 582,348 560,330 590,330" fill="white"/>
      <polygon points="100,400 110,430 140,430 118,448 128,478 100,460 72,478 82,448 60,430 90,430" fill="white"/>
    </g>
    
    <!-- Korean-style text placeholder -->
    <g opacity="0.6">
      <text x="${width/2}" y="${height/2 - 40}" text-anchor="middle" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="white">K-FASHION</text>
      <text x="${width/2}" y="${height/2 + 20}" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="white" opacity="0.8">Korean Style</text>
    </g>
    
    <!-- Decorative elements -->
    <g opacity="0.2">
      <circle cx="100" cy="300" r="50" fill="white"/>
      <circle cx="700" cy="100" r="30" fill="white"/>
      <circle cx="650" cy="500" r="40" fill="white"/>
    </g>
  </svg>`;
}

// Create banner SVG
function createBannerSVG(colors, width = 1200, height = 400, title = 'K-FASHION COLLECTION') {
  const gradientId = `banner-gradient`;
  
  return `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="${gradientId}" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:${colors[0]};stop-opacity:1" />
        <stop offset="50%" style="stop-color:${colors[1]};stop-opacity:1" />
        <stop offset="100%" style="stop-color:${colors[2]};stop-opacity:1" />
      </linearGradient>
      <filter id="shadow">
        <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
      </filter>
    </defs>
    
    <!-- Background -->
    <rect width="100%" height="100%" fill="url(#${gradientId})"/>
    
    <!-- Pattern overlay -->
    <g opacity="0.1">
      ${Array.from({length: 20}, (_, i) => {
        const x = (i % 5) * 240 + 120;
        const y = Math.floor(i / 5) * 100 + 50;
        return `<circle cx="${x}" cy="${y}" r="30" fill="white"/>`;
      }).join('')}
    </g>
    
    <!-- Main title -->
    <text x="${width/2}" y="${height/2 - 20}" text-anchor="middle" font-family="Arial, sans-serif" font-size="64" font-weight="900" fill="white" filter="url(#shadow)">${title}</text>
    <text x="${width/2}" y="${height/2 + 40}" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="white" opacity="0.9">✨ Phong Cách Idol Hàn Quốc ✨</text>
    
    <!-- Decorative hearts -->
    <g opacity="0.6">
      <path d="M100,100 C100,80 80,60 60,60 C40,60 20,80 20,100 C20,120 60,160 60,160 C60,160 100,120 100,100 Z" fill="white"/>
      <path d="${width-80},100 C${width-80},80 ${width-100},60 ${width-120},60 C${width-140},60 ${width-160},80 ${width-160},100 C${width-160},120 ${width-120},160 ${width-120},160 C${width-120},160 ${width-80},120 ${width-80},100 Z" fill="white"/>
    </g>
  </svg>`;
}

// Create product placeholder SVG
function createProductSVG(colors, width = 400, height = 533, productName = 'K-Fashion Item') {
  const gradientId = `product-gradient`;
  
  return `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="${gradientId}" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:${colors[0]};stop-opacity:1" />
        <stop offset="50%" style="stop-color:${colors[1]};stop-opacity:1" />
        <stop offset="100%" style="stop-color:${colors[2]};stop-opacity:1" />
      </linearGradient>
    </defs>
    
    <!-- Background -->
    <rect width="100%" height="100%" fill="url(#${gradientId})"/>
    
    <!-- Fashion silhouette -->
    <g opacity="0.3" transform="translate(${width/2}, ${height/2})">
      <!-- Simple dress/top silhouette -->
      <path d="M-60,-100 L-40,-100 L-30,-80 L-20,-60 L-15,-40 L-10,0 L-15,40 L-20,80 L-30,100 L30,100 L20,80 L15,40 L10,0 L15,-40 L20,-60 L30,-80 L40,-100 L60,-100 L50,-120 L-50,-120 Z" fill="white"/>
    </g>
    
    <!-- Korean text -->
    <text x="${width/2}" y="${height - 60}" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">${productName}</text>
    <text x="${width/2}" y="${height - 30}" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white" opacity="0.8">Korean Style</text>
    
    <!-- Decorative elements -->
    <g opacity="0.4">
      <circle cx="50" cy="50" r="20" fill="white"/>
      <circle cx="${width-50}" cy="50" r="15" fill="white"/>
      <circle cx="50" cy="${height-50}" r="15" fill="white"/>
      <circle cx="${width-50}" cy="${height-50}" r="20" fill="white"/>
    </g>
  </svg>`;
}

// Generate all placeholder images
console.log('🎨 Creating K-Fashion placeholder images...');

// Hero banners
Object.entries(colorPalettes).forEach(([name, colors]) => {
  const bannerSVG = createBannerSVG(colors, 1200, 400, 'K-FASHION COLLECTION');
  fs.writeFileSync(path.join(imagesDir, `hero-banner-${name}.svg`), bannerSVG);
  console.log(`✅ Created hero-banner-${name}.svg`);
});

// Background images
Object.entries(colorPalettes).forEach(([name, colors]) => {
  const bgSVG = createGradientSVG(colors, 800, 600, name);
  fs.writeFileSync(path.join(imagesDir, `background-${name}.svg`), bgSVG);
  console.log(`✅ Created background-${name}.svg`);
});

// Product placeholders
const productNames = [
  'Seoul Street Top',
  'K-Pop Inspired Dress',
  'Idol Style Jacket',
  'Korean Fashion Skirt',
  'Trendy K-Style Pants',
  'Cute Korean Blouse'
];

productNames.forEach((name, index) => {
  const paletteNames = Object.keys(colorPalettes);
  const palette = colorPalettes[paletteNames[index % paletteNames.length]];
  const productSVG = createProductSVG(palette, 400, 533, name);
  const fileName = name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
  fs.writeFileSync(path.join(imagesDir, `product-${fileName}.svg`), productSVG);
  console.log(`✅ Created product-${fileName}.svg`);
});

// Create a main K-Fashion logo
const logoSVG = `<svg width="200" height="80" viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logo-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b9d;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#c44569;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8b500;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <text x="100" y="45" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="900" fill="url(#logo-gradient)">K-FASHION</text>
  <text x="100" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">Korean Style Store</text>
  
  <!-- Decorative hearts -->
  <path d="M20,20 C20,15 15,10 10,10 C5,10 0,15 0,20 C0,25 10,35 10,35 C10,35 20,25 20,20 Z" fill="#ff6b9d"/>
  <path d="M200,20 C200,15 195,10 190,10 C185,10 180,15 180,20 C180,25 190,35 190,35 C190,35 200,25 200,20 Z" fill="#ff6b9d"/>
</svg>`;

fs.writeFileSync(path.join(imagesDir, 'k-fashion-logo.svg'), logoSVG);
console.log('✅ Created k-fashion-logo.svg');

console.log('🎉 All K-Fashion placeholder images created successfully!');
console.log(`📁 Images saved to: ${imagesDir}`);

// Create an index file listing all created images
const imageList = fs.readdirSync(imagesDir);
const indexContent = `# K-Fashion Placeholder Images

Generated on: ${new Date().toISOString()}

## Available Images:

${imageList.map(img => `- ${img}`).join('\n')}

## Usage:

These SVG images can be used as placeholders for:
- Hero banners
- Product images
- Background images
- Logo

All images follow K-Fashion/K-Pop aesthetic with Korean-inspired colors and designs.
`;

fs.writeFileSync(path.join(imagesDir, 'README.md'), indexContent);
console.log('✅ Created README.md with image index');

module.exports = {
  createGradientSVG,
  createBannerSVG,
  createProductSVG,
  colorPalettes
};
