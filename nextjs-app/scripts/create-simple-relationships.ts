import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

async function createSimpleRelationships() {
  console.log('🔗 Creating simple Product-Category relationships...\n');

  try {
    // Step 1: Get basic counts
    console.log('📊 Database Status:');
    const productCount = await prisma.product.count();
    const categoryCount = await prisma.category.count();
    const relationshipCount = await prisma.productCategory.count();
    
    console.log(`   Products: ${productCount}`);
    console.log(`   Categories: ${categoryCount}`);
    console.log(`   Relationships: ${relationshipCount}`);

    if (relationshipCount > 0) {
      console.log('\n✅ Relationships already exist! Testing API...');
      await testAPIs();
      return;
    }

    // Step 2: Read categories JSON to understand structure
    console.log('\n📖 Reading categories JSON...');
    const categoriesPath = path.join(process.cwd(), 'src/data/categories.json');
    const categoriesData = JSON.parse(fs.readFileSync(categoriesPath, 'utf8'));
    
    console.log('📂 Available categories in JSON:');
    categoriesData.forEach((cat: any, index: number) => {
      console.log(`   ${index + 1}. ${cat.name} (${cat.slug})`);
    });

    // Step 3: Get database categories
    console.log('\n🔍 Database categories:');
    const dbCategories = await prisma.category.findMany({
      select: { id: true, name: true, slug: true }
    });
    
    dbCategories.forEach((cat, index) => {
      console.log(`   ${index + 1}. ${cat.name} (${cat.slug})`);
    });

    // Step 4: Create a simple mapping strategy
    console.log('\n🗺️ Creating simple category mapping...');
    
    // Map all products to the first available category for now
    // This ensures we have relationships to test the system
    const firstCategory = dbCategories[0];
    if (!firstCategory) {
      throw new Error('No categories found in database');
    }

    console.log(`📌 Using category: ${firstCategory.name} for all products`);

    // Step 5: Get all product IDs (safely)
    console.log('\n🛍️ Getting product IDs...');
    const productIds = await prisma.product.findMany({
      select: { id: true, name: true },
      take: 10 // Start with just 10 products for testing
    });

    console.log(`✅ Found ${productIds.length} products to process`);

    // Step 6: Create relationships
    console.log('\n🔗 Creating relationships...');
    const relationships = productIds.map(product => ({
      productId: product.id,
      categoryId: firstCategory.id
    }));

    await prisma.productCategory.createMany({
      data: relationships
    });

    console.log(`✅ Created ${relationships.length} relationships`);

    // Step 7: Verify
    const finalCount = await prisma.productCategory.count();
    console.log(`📊 Final relationship count: ${finalCount}`);

    // Step 8: Test APIs
    console.log('\n🧪 Testing APIs...');
    await testAPIs();

    console.log('\n🎉 SUCCESS! Basic relationships created.');
    console.log('💡 Now test the frontend pages:');
    console.log('   🏠 Homepage: http://localhost:3001');
    console.log('   🛍️ Products: http://localhost:3001/san-pham');
    console.log('   📂 Category: http://localhost:3001/danh-muc/' + firstCategory.slug);

  } catch (error) {
    console.error('❌ Failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function testAPIs() {
  try {
    // Test 1: Count products with categories
    const productsWithCategories = await prisma.productCategory.groupBy({
      by: ['productId'],
      _count: { productId: true }
    });
    console.log(`   ✅ Products with categories: ${productsWithCategories.length}`);

    // Test 2: Count categories with products
    const categoriesWithProducts = await prisma.productCategory.groupBy({
      by: ['categoryId'],
      _count: { categoryId: true }
    });
    console.log(`   ✅ Categories with products: ${categoriesWithProducts.length}`);

    // Test 3: Simple product query (without includes to avoid corruption)
    const simpleProducts = await prisma.product.findMany({
      select: { id: true, name: true, slug: true, price: true },
      take: 3
    });
    console.log(`   ✅ Simple products query: ${simpleProducts.length} products`);

    // Test 4: Simple category query
    const simpleCategories = await prisma.category.findMany({
      select: { id: true, name: true, slug: true },
      take: 3
    });
    console.log(`   ✅ Simple categories query: ${simpleCategories.length} categories`);

    // Test 5: Relationship query
    const relationships = await prisma.productCategory.findMany({
      select: { productId: true, categoryId: true },
      take: 3
    });
    console.log(`   ✅ Relationships query: ${relationships.length} relationships`);

  } catch (error) {
    console.error('   ❌ API test failed:', error);
  }
}

createSimpleRelationships();
