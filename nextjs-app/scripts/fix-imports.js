const fs = require('fs');
const path = require('path');

// Files that need to be updated
const filesToUpdate = [
  'app/api/categories/route.ts',
  'app/api/admin/categories/all/route.ts',
  'app/api/admin/categories/[id]/route.ts',
  'app/api/admin/categories/route.ts',
  'app/api/admin/upload/route.ts',
  'app/api/admin/products/route.ts',
  'app/api/admin/dashboard/stats/route.ts',
  'src/lib/auth.ts'
];

function updateImports() {
  console.log('🔧 Fixing Prisma imports...');
  
  filesToUpdate.forEach(filePath => {
    try {
      if (fs.existsSync(filePath)) {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Replace the import statement
        const oldImport = /import prisma from ['"](\.\.\/)*src\/lib\/db['"];?/g;
        const newImport = "import { prisma } from '../../../../../src/lib/prisma';";
        
        // Adjust the path based on file location
        let correctPath;
        const depth = (filePath.match(/\//g) || []).length;
        
        if (filePath.startsWith('app/api/admin/categories/[id]') || filePath.startsWith('app/api/admin/dashboard/stats')) {
          correctPath = "import { prisma } from '../../../../../src/lib/prisma';";
        } else if (filePath.startsWith('app/api/admin/categories/all')) {
          correctPath = "import { prisma } from '../../../../../src/lib/prisma';";
        } else if (filePath.startsWith('app/api/admin/')) {
          correctPath = "import { prisma } from '../../../../src/lib/prisma';";
        } else if (filePath.startsWith('app/api/')) {
          correctPath = "import { prisma } from '../../../src/lib/prisma';";
        } else if (filePath.startsWith('src/lib/')) {
          correctPath = "import { prisma } from './prisma';";
        }
        
        if (content.includes('import prisma from')) {
          content = content.replace(oldImport, correctPath);
          fs.writeFileSync(filePath, content);
          console.log(`✅ Updated: ${filePath}`);
        } else {
          console.log(`ℹ️ No changes needed: ${filePath}`);
        }
      } else {
        console.log(`⚠️ File not found: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ Error updating ${filePath}:`, error.message);
    }
  });
  
  console.log('🎉 Import fixes completed!');
}

updateImports();
