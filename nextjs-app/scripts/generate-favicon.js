// Script to generate favicon.ico from SVG
// You can use online tools or this Node.js script with sharp library

const fs = require('fs');
const path = require('path');

// Instructions for generating favicon.ico:
console.log(`
🎨 FAVICON GENERATION INSTRUCTIONS:

1. **Online Method (Recommended):**
   - Go to https://realfavicongenerator.net/
   - Upload the file: public/favicon.svg
   - Configure settings and download the generated files
   - Replace the files in public/ directory

2. **Manual Method:**
   - Use any SVG to ICO converter online
   - Convert public/favicon.svg to favicon.ico (32x32)
   - Save as public/favicon.ico

3. **Node.js Method (if you have sharp installed):**
   - npm install sharp
   - Run this script: node scripts/generate-favicon.js

📁 Files created:
   ✅ public/favicon.svg (32x32)
   ✅ public/apple-touch-icon.svg (180x180)
   ✅ public/icon-192x192.svg (192x192)
   ✅ public/icon-512x512.svg (512x512)
   ✅ public/site.webmanifest
   ✅ Updated app/layout.tsx with metadata

🎯 Design Features:
   - Letter "T" for Thinluong
   - Fashion hanger icon accent
   - Purple-blue gradient background
   - Responsive sizes for all devices
   - PWA ready with manifest
`);

// If sharp is available, generate ICO
try {
  const sharp = require('sharp');
  
  async function generateFavicon() {
    const svgBuffer = fs.readFileSync(path.join(__dirname, '../public/favicon.svg'));
    
    // Generate favicon.ico (32x32)
    await sharp(svgBuffer)
      .resize(32, 32)
      .png()
      .toFile(path.join(__dirname, '../public/favicon.ico'));
    
    console.log('✅ Generated favicon.ico successfully!');
  }
  
  generateFavicon().catch(console.error);
} catch (error) {
  console.log('ℹ️  Sharp not installed. Use online converter instead.');
}
