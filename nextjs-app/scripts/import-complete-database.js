/**
 * Complete Database Import Script
 * 
 * This script creates a comprehensive sample database with:
 * - Categories (using existing + new ones)
 * - Products with full details
 * - Admin users with proper permissions
 * - Sample users and interactions
 * - Complete workflow data for testing
 */

const {PrismaClient}=require('@prisma/client');
const bcrypt=require('bcryptjs');

const prisma=new PrismaClient();

// Sample data structure
const sampleData={
  categories: [
    // Keep existing categories and add new ones
    {name: '<PERSON><PERSON>',slug: 'ao-polo-nam',description: 'Áo polo nam cao cấp, phong cách lịch lãm',isActive: true},
    {name: '<PERSON><PERSON> Hood<PERSON>',slug: 'ao-hoodie',description: 'Áo hoodie unisex, phong cách trẻ trung',isActive: true},
    {name: 'Quần Short',slug: 'quan-short',description: 'Quần short nam nữ, thoải mái mùa hè',isActive: true},
    {name: '<PERSON><PERSON><PERSON>',slug: 'vay-dam',description: '<PERSON><PERSON><PERSON> đầm nữ các lo<PERSON>, thanh lịch',isActive: true},
    {name: '<PERSON><PERSON>',slug: 'phu-kien',description: 'Phụ kiện thời trang đa dạng',isActive: true},
    {name: 'Giày Dép',slug: 'giay-dep',description: 'Giày dép nam nữ chất lượng cao',isActive: true},
    {name: 'Túi Xách',slug: 'tui-xach',description: 'Túi xách, balo thời trang',isActive: true},
  ],

  adminUsers: [
    {
      email: '<EMAIL>',
      name: 'Admin Chính',
      password: 'admin123',
      role: 'SUPER_ADMIN',
      permissions: ['PRODUCT_CREATE','PRODUCT_UPDATE','PRODUCT_DELETE','CATEGORY_MANAGE','USER_MANAGE','ORDER_MANAGE','ANALYTICS_VIEW']
    },
    {
      email: '<EMAIL>',
      name: 'Quản Lý Sản Phẩm',
      password: 'manager123',
      role: 'ADMIN',
      permissions: ['PRODUCT_CREATE','PRODUCT_UPDATE','PRODUCT_DELETE','CATEGORY_MANAGE','ORDER_MANAGE']
    },
    {
      email: '<EMAIL>',
      name: 'Nhân Viên',
      password: 'staff123',
      role: 'MODERATOR',
      permissions: ['PRODUCT_UPDATE','ORDER_MANAGE']
    }
  ],

  users: [
    {
      email: '<EMAIL>',
      name: 'Nguyễn Văn A',
      password: 'customer123',
      phone: '0901234567',
      address: '123 Nguyễn Huệ, Q1, TP.HCM'
    },
    {
      email: '<EMAIL>',
      name: 'Trần Thị B',
      password: 'customer123',
      phone: '0907654321',
      address: '456 Lê Lợi, Q3, TP.HCM'
    },
    {
      email: '<EMAIL>',
      name: 'Lê Văn C',
      password: 'customer123',
      phone: '0912345678',
      address: '789 Hai Bà Trưng, Q1, TP.HCM'
    }
  ]
};

// Product templates for each category
const productTemplates={
  'ao-nam': [
    {name: 'Áo Sơ Mi Nam Trắng Classic',price: 299000,description: 'Áo sơ mi nam trắng basic, phù hợp đi làm và dự tiệc'},
    {name: 'Áo Thun Nam Cotton Premium',price: 199000,description: 'Áo thun nam 100% cotton, thoáng mát, form regular'},
    {name: 'Áo Khoác Bomber Nam',price: 599000,description: 'Áo khoác bomber nam phong cách streetwear'}
  ],
  'ao-nu': [
    {name: 'Áo Blouse Nữ Elegant',price: 349000,description: 'Áo blouse nữ thanh lịch, phù hợp công sở'},
    {name: 'Áo Thun Nữ Crop Top',price: 159000,description: 'Áo thun nữ crop top trẻ trung, năng động'},
    {name: 'Áo Kiểu Nữ Vintage',price: 279000,description: 'Áo kiểu nữ phong cách vintage, cổ điển'}
  ],
  'quan-nam': [
    {name: 'Quần Jean Nam Slim Fit',price: 449000,description: 'Quần jean nam slim fit, co giãn tốt'},
    {name: 'Quần Kaki Nam Chinos',price: 359000,description: 'Quần kaki nam chinos, phù hợp đi làm'},
    {name: 'Quần Jogger Nam Thể Thao',price: 299000,description: 'Quần jogger nam thoải mái, phù hợp thể thao'}
  ],
  'quan-nu': [
    {name: 'Quần Jean Nữ Skinny',price: 399000,description: 'Quần jean nữ skinny ôm dáng, tôn vóc dáng'},
    {name: 'Quần Culottes Nữ Wide Leg',price: 329000,description: 'Quần culottes nữ ống rộng, thanh lịch'},
    {name: 'Quần Legging Nữ Yoga',price: 199000,description: 'Quần legging nữ co giãn, phù hợp yoga'}
  ],
  'ao-polo-nam': [
    {name: 'Áo Polo Nam Classic Navy',price: 259000,description: 'Áo polo nam màu navy cổ điển, chất liệu pique'},
    {name: 'Áo Polo Nam Stripe Pattern',price: 289000,description: 'Áo polo nam họa tiết sọc, phong cách preppy'}
  ],
  'ao-hoodie': [
    {name: 'Hoodie Unisex Basic Black',price: 459000,description: 'Hoodie unisex màu đen basic, ấm áp'},
    {name: 'Hoodie Zip-up Grey Melange',price: 529000,description: 'Hoodie zip-up màu xám melange, tiện lợi'}
  ],
  'quan-short': [
    {name: 'Quần Short Nam Chino',price: 229000,description: 'Quần short nam chino, thoải mái mùa hè'},
    {name: 'Quần Short Nữ Denim',price: 199000,description: 'Quần short nữ denim, phong cách casual'}
  ],
  'vay-dam': [
    {name: 'Váy Đầm Maxi Hoa Nhí',price: 449000,description: 'Váy đầm maxi họa tiết hoa nhí, nữ tính'},
    {name: 'Đầm Shirt Dress Công Sở',price: 389000,description: 'Đầm shirt dress phù hợp công sở, thanh lịch'}
  ],
  'phu-kien': [
    {name: 'Thắt Lưng Da Nam',price: 299000,description: 'Thắt lưng da nam cao cấp, khóa kim loại'},
    {name: 'Khăn Choàng Nữ Silk',price: 199000,description: 'Khăn choàng nữ chất liệu silk, sang trọng'}
  ],
  'giay-dep': [
    {name: 'Giày Sneaker Nam Trắng',price: 799000,description: 'Giày sneaker nam màu trắng, phong cách thể thao'},
    {name: 'Giày Cao Gót Nữ 5cm',price: 659000,description: 'Giày cao gót nữ 5cm, thanh lịch'}
  ],
  'tui-xach': [
    {name: 'Túi Xách Tay Nữ Da',price: 899000,description: 'Túi xách tay nữ da thật, sang trọng'},
    {name: 'Balo Laptop Nam',price: 599000,description: 'Balo laptop nam chống nước, tiện dụng'}
  ]
};

async function clearDatabase() {
  console.log('🗑️ Clearing existing data...');

  // Delete in correct order to avoid foreign key constraints
  await prisma.adminLog.deleteMany({});
  await prisma.adminSession.deleteMany({});
  await prisma.orderItem.deleteMany({});
  await prisma.order.deleteMany({});
  await prisma.cartItem.deleteMany({});
  await prisma.wishlistItem.deleteMany({});
  await prisma.review.deleteMany({});
  await prisma.product.deleteMany({});
  // Keep existing categories, just clean up products

  console.log('✅ Database cleared (kept existing categories)');
}

async function createAdminUsers() {
  console.log('👤 Creating admin users...');

  for(const adminData of sampleData.adminUsers) {
    const hashedPassword=await bcrypt.hash(adminData.password,10);

    await prisma.adminUser.upsert({
      where: {email: adminData.email},
      update: {
        name: adminData.name,
        password: hashedPassword,
        role: adminData.role,
        permissions: adminData.permissions,
        isActive: true,
      },
      create: {
        email: adminData.email,
        name: adminData.name,
        password: hashedPassword,
        role: adminData.role,
        permissions: adminData.permissions,
        isActive: true,
      },
    });

    console.log(`✅ Created admin: ${adminData.email} (${adminData.role})`);
  }
}

async function createUsers() {
  console.log('👥 Creating sample users...');

  for(const userData of sampleData.users) {
    const hashedPassword=await bcrypt.hash(userData.password,10);

    const user=await prisma.user.upsert({
      where: {email: userData.email},
      update: {
        name: userData.name,
        password: hashedPassword,
        phone: userData.phone,
        isActive: true,
      },
      create: {
        email: userData.email,
        name: userData.name,
        password: hashedPassword,
        phone: userData.phone,
        isActive: true,
      },
    });

    // Create default address for user
    const existingAddress=await prisma.address.findFirst({
      where: {
        userId: user.id,
        isDefault: true
      }
    });

    if(!existingAddress) {
      await prisma.address.create({
        data: {
          userId: user.id,
          name: 'Địa chỉ mặc định',
          phone: userData.phone,
          address: userData.address,
          ward: 'Phường 1',
          district: 'Quận 1',
          city: 'TP.HCM',
          isDefault: true,
        },
      });
    }

    console.log(`✅ Created user: ${userData.email}`);
  }
}

async function createCategories() {
  console.log('📁 Creating/updating categories...');

  for(const categoryData of sampleData.categories) {
    await prisma.category.upsert({
      where: {slug: categoryData.slug},
      update: {
        name: categoryData.name,
        description: categoryData.description,
        isActive: categoryData.isActive,
      },
      create: {
        name: categoryData.name,
        slug: categoryData.slug,
        description: categoryData.description,
        isActive: categoryData.isActive,
      },
    });

    console.log(`✅ Created/updated category: ${categoryData.name}`);
  }
}

async function createProducts() {
  console.log('📦 Creating sample products...');

  // Get all categories
  const categories=await prisma.category.findMany({
    where: {isActive: true}
  });

  let productCount=0;

  for(const category of categories) {
    const templates=productTemplates[category.slug]||[];

    for(let i=0;i<templates.length;i++) {
      const template=templates[i];
      const sku=`${category.slug.toUpperCase()}-${String(i+1).padStart(3,'0')}`;

      const product=await prisma.product.create({
        data: {
          name: template.name,
          slug: template.name.toLowerCase()
            .replace(/[^a-z0-9\s-]/g,'')
            .replace(/\s+/g,'-')
            .replace(/-+/g,'-')+`-${Date.now()}-${i}`,
          description: template.description,
          price: template.price,
          originalPrice: Math.floor(template.price*1.2), // 20% higher original price
          isOnSale: Math.random()>0.6, // 40% chance to be on sale
          stock: Math.floor(Math.random()*50)+10, // Random stock 10-60
          sku: sku,
          categoryId: category.id,
          images: [
            `https://picsum.photos/400/400?random=${productCount+1}`,
            `https://picsum.photos/400/400?random=${productCount+2}`,
            `https://picsum.photos/400/400?random=${productCount+3}`
          ],
          tags: [category.name,'thời trang','chất lượng cao'],
          seoTitle: `${template.name} - Thời Trang Chất Lượng Cao`,
          seoDescription: `${template.description}. Mua ngay với giá ưu đãi tại Thin Luong.`,
          seoKeywords: [template.name.toLowerCase(),category.name.toLowerCase(),'thời trang','chất lượng'],
          status: 'ACTIVE',
          featured: Math.random()>0.7, // 30% chance to be featured
          weight: Math.floor(Math.random()*500)+100, // Random weight 100-600g
          dimensions: {
            length: Math.floor(Math.random()*20)+20,
            width: Math.floor(Math.random()*15)+15,
            height: Math.floor(Math.random()*5)+2,
            unit: 'cm'
          },
        },
      });

      productCount++;
      console.log(`✅ Created product: ${template.name} (${sku})`);
    }
  }

  console.log(`📦 Total products created: ${productCount}`);
}

async function createSampleInteractions() {
  console.log('💬 Creating sample interactions...');

  const users=await prisma.user.findMany({take: 3});
  const products=await prisma.product.findMany({take: 10});

  if(users.length===0||products.length===0) {
    console.log('⚠️ No users or products found for interactions');
    return;
  }

  // Create sample reviews only (skip cart and wishlist for now)
  const reviewTexts=[
    'Sản phẩm rất tốt, chất lượng vượt mong đợi!',
    'Giao hàng nhanh, đóng gói cẩn thận. Sẽ mua lại!',
    'Chất liệu tốt, form dáng đẹp, giá cả hợp lý.',
    'Màu sắc đúng như hình, size chuẩn.',
    'Dịch vụ khách hàng tuyệt vời, sản phẩm chất lượng.'
  ];

  for(let i=0;i<15;i++) {
    const user=users[Math.floor(Math.random()*users.length)];
    const product=products[Math.floor(Math.random()*products.length)];
    const reviewText=reviewTexts[Math.floor(Math.random()*reviewTexts.length)];

    try {
      await prisma.review.create({
        data: {
          userId: user.id,
          productId: product.id,
          rating: Math.floor(Math.random()*2)+4, // 4-5 stars
          title: 'Đánh giá sản phẩm',
          comment: reviewText,
          isApproved: true,
        },
      });
    } catch(error) {
      // Skip if review already exists
      console.log(`⚠️ Skipped duplicate review for product ${product.id}`);
    }
  }

  console.log('✅ Sample interactions created');
}

async function main() {
  try {
    console.log('🚀 Starting complete database import...\n');

    await clearDatabase();
    await createAdminUsers();
    await createUsers();
    await createCategories();
    await createProducts();
    await createSampleInteractions();

    // Final summary
    const summary={
      categories: await prisma.category.count(),
      products: await prisma.product.count(),
      adminUsers: await prisma.adminUser.count(),
      users: await prisma.user.count(),
      reviews: await prisma.review.count(),
      cartItems: await prisma.cartItem.count(),
      wishlistItems: await prisma.wishlistItem.count(),
    };

    console.log('\n🎉 Database import completed successfully!');
    console.log('📊 Summary:');
    console.log(`  - Categories: ${summary.categories}`);
    console.log(`  - Products: ${summary.products}`);
    console.log(`  - Admin Users: ${summary.adminUsers}`);
    console.log(`  - Regular Users: ${summary.users}`);
    console.log(`  - Reviews: ${summary.reviews}`);
    console.log(`  - Cart Items: ${summary.cartItems}`);
    console.log(`  - Wishlist Items: ${summary.wishlistItems}`);

    console.log('\n🔑 Admin Login Credentials:');
    console.log('  - Super Admin: <EMAIL> / admin123');
    console.log('  - Manager: <EMAIL> / manager123');
    console.log('  - Staff: <EMAIL> / staff123');

    console.log('\n👥 Customer Login Credentials:');
    console.log('  - Customer 1: <EMAIL> / customer123');
    console.log('  - Customer 2: <EMAIL> / customer123');
    console.log('  - Customer 3: <EMAIL> / customer123');

    console.log('\n🌐 Access URLs:');
    console.log('  - Admin Panel: http://localhost:3006/admin');
    console.log('  - Customer Site: http://localhost:3006');
    console.log('  - Debug Tool: http://localhost:3006/admin/debug');

  } catch(error) {
    console.error('❌ Error during import:',error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

if(require.main===module) {
  main().catch(console.error);
}

module.exports={main};
