const {PrismaClient}=require('@prisma/client');
const fs=require('fs');
const path=require('path');

const prisma=new PrismaClient();

// Helper function to clean price string
function cleanPrice(priceString) {
  if(!priceString) return 0;

  // Remove ₫ symbol and any non-numeric characters except dots and commas
  let cleaned=priceString.replace(/₫/g,'').trim();

  // Handle price ranges (take the first price)
  if(cleaned.includes(' - ')) {
    cleaned=cleaned.split(' - ')[0];
  }

  // Remove dots (thousands separator) and convert to number
  cleaned=cleaned.replace(/\./g,'');

  return parseInt(cleaned)||0;
}

// Helper function to generate slug
function generateSlug(name) {
  return name
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g,'') // Remove diacritics
    .replace(/[^a-z0-9\s-]/g,'') // Remove special characters
    .replace(/\s+/g,'-') // Replace spaces with hyphens
    .replace(/-+/g,'-') // Replace multiple hyphens with single
    .trim('-'); // Remove leading/trailing hyphens
}

// Helper function to create short description
function createShortDescription(description,name) {
  if(description&&description.trim().length>10) {
    return description.substring(0,150)+'...';
  }
  return `${name} - Sản phẩm chất lượng cao từ Thinluong`;
}

async function importData() {
  try {
    console.log('🚀 Starting data import from JSON files...');

    // Read JSON files
    const categoriesPath=path.join(process.cwd(),'src/data/categories.json');
    const productsPath=path.join(process.cwd(),'src/data/products.json');

    if(!fs.existsSync(categoriesPath)) {
      throw new Error(`Categories file not found: ${categoriesPath}`);
    }
    if(!fs.existsSync(productsPath)) {
      throw new Error(`Products file not found: ${productsPath}`);
    }

    const categoriesData=JSON.parse(fs.readFileSync(categoriesPath,'utf8'));
    const productsData=JSON.parse(fs.readFileSync(productsPath,'utf8'));

    console.log(`📊 Found ${categoriesData.length} categories and ${productsData.length} products`);

    // Step 1: Clear existing data
    console.log('🧹 Clearing existing data...');

    try {
      await prisma.productCategory.deleteMany({});
      console.log('  ✅ Cleared ProductCategory relations');
    } catch(error) {
      console.log('  ⚠️ ProductCategory table might not exist or be empty');
    }

    try {
      await prisma.review.deleteMany({});
      console.log('  ✅ Cleared Reviews');
    } catch(error) {
      console.log('  ⚠️ Reviews table might not exist or be empty');
    }

    try {
      await prisma.product.deleteMany({});
      console.log('  ✅ Cleared Products');
    } catch(error) {
      console.log('  ⚠️ Products table might not exist or be empty');
    }

    try {
      await prisma.category.deleteMany({});
      console.log('  ✅ Cleared Categories');
    } catch(error) {
      console.log('  ⚠️ Categories table might not exist or be empty');
    }

    console.log('✅ Existing data cleared');

    // Step 2: Import Categories
    console.log('📁 Importing categories...');

    const categoryMap=new Map();

    for(let i=0;i<categoriesData.length;i++) {
      const categoryData=categoriesData[i];

      const category=await prisma.category.create({
        data: {
          name: categoryData.name,
          slug: generateSlug(categoryData.name),
          description: `Danh mục ${categoryData.name}`,
          image: null, // Will be updated later if needed
          parentId: null,
          level: 0,
          sortOrder: i+1,
          isActive: true,

        }
      });

      categoryMap.set(categoryData.name,category.id);
      console.log(`  ✅ Created category: ${category.name}`);
    }

    console.log(`✅ Imported ${categoriesData.length} categories`);

    // Step 3: Import Products
    console.log('🛍️ Importing products...');

    let productCount=0;
    let reviewCount=0;

    for(const productData of productsData) {
      try {
        // Clean and prepare product data
        const price=cleanPrice(productData.price);
        const slug=generateSlug(productData.name);

        // Find primary category (first one in categoryIds)
        const primaryCategoryName=productData.categoryIds&&productData.categoryIds.length>0
          ? productData.categoryIds[0]
          :categoriesData[0].name; // Fallback to first category

        const primaryCategoryId=categoryMap.get(primaryCategoryName);

        if(!primaryCategoryId) {
          console.warn(`⚠️ Category not found for product: ${productData.name}, using first category`);
        }

        // Create product
        const product=await prisma.product.create({
          data: {
            name: productData.name,
            slug: slug,
            description: productData.description||`${productData.name} - Sản phẩm chất lượng cao`,
            shortDescription: createShortDescription(productData.description,productData.name),
            price: price,
            originalPrice: price,
            isOnSale: false,
            stock: 100, // Default stock
            sku: `SKU-${Date.now()}-${productCount}`,
            barcode: null,
            weight: null,
            dimensions: null,
            images: productData.images||[],

            brand: 'Thinluong',
            tags: productData.colors||[],
            featured: Math.random()>0.7, // 30% chance to be featured
            status: 'ACTIVE',
            seoTitle: productData.name,
            seoDescription: createShortDescription(productData.description,productData.name),
            seoKeywords: [productData.name.toLowerCase()],
            colors: productData.colors||[],
            sizes: productData.sizes||[],
            categoryId: primaryCategoryId||categoryMap.values().next().value
          }
        });

        // Create ProductCategory relationships for all categories
        if(productData.categoryIds&&productData.categoryIds.length>0) {
          for(const categoryName of productData.categoryIds) {
            const categoryId=categoryMap.get(categoryName);
            if(categoryId) {
              await prisma.productCategory.create({
                data: {
                  productId: product.id,
                  categoryId: categoryId
                }
              });
            }
          }
        }

        // Import reviews if available
        if(productData.reviews&&productData.reviews.length>0) {
          for(const reviewData of productData.reviews.slice(0,5)) { // Limit to 5 reviews per product
            try {
              await prisma.review.create({
                data: {
                  productId: product.id,
                  userId: null, // Anonymous reviews
                  rating: reviewData.stars||5,
                  title: `Đánh giá từ ${reviewData.author}`,
                  comment: reviewData.text? reviewData.text.substring(0,500):'Sản phẩm tốt',
                  images: reviewData.imgs||[],
                  isVerified: false,
                  isApproved: true,
                  helpfulCount: Math.floor(Math.random()*20)
                }
              });
              reviewCount++;
            } catch(reviewError) {
              console.warn(`⚠️ Failed to create review for ${product.name}:`,reviewError.message);
            }
          }
        }

        productCount++;
        console.log(`  ✅ Created product: ${product.name} (${productCount}/${productsData.length})`);

      } catch(productError) {
        console.error(`❌ Failed to create product: ${productData.name}`,productError.message);
      }
    }

    console.log(`✅ Imported ${productCount} products`);
    console.log(`✅ Imported ${reviewCount} reviews`);

    // Step 4: Update category product counts
    console.log('🔄 Updating category product counts...');

    for(const [categoryName,categoryId] of categoryMap) {
      const productCount=await prisma.productCategory.count({
        where: {categoryId: categoryId}
      });

      console.log(`  📊 ${categoryName}: ${productCount} products`);
    }

    // Step 5: Generate summary report
    const finalStats={
      categories: await prisma.category.count(),
      products: await prisma.product.count(),
      reviews: await prisma.review.count(),
      productCategories: await prisma.productCategory.count()
    };

    console.log('\n🎉 Import completed successfully!');
    console.log('📊 Final Statistics:');
    console.log(`  Categories: ${finalStats.categories}`);
    console.log(`  Products: ${finalStats.products}`);
    console.log(`  Reviews: ${finalStats.reviews}`);
    console.log(`  Product-Category Relations: ${finalStats.productCategories}`);

    return finalStats;

  } catch(error) {
    console.error('❌ Import failed:',error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run import if called directly
if(require.main===module) {
  importData()
    .then((stats) => {
      console.log('\n✅ Import process completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Import process failed:',error);
      process.exit(1);
    });
}

module.exports={importData};
