const {PrismaClient}=require('@prisma/client');

async function initializeDatabase() {
  const prisma=new PrismaClient();

  try {
    console.log('🔧 Initializing database...');

    // Test database connection
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Check if admin user exists
    const adminExists=await prisma.adminUser.findFirst({
      where: {
        role: 'ADMIN'
      }
    });

    if(!adminExists&&process.env.ADMIN_EMAIL&&process.env.ADMIN_PASSWORD) {
      console.log('👤 Creating admin user...');

      const bcrypt=require('bcryptjs');
      const hashedPassword=await bcrypt.hash(process.env.ADMIN_PASSWORD,12);

      await prisma.adminUser.create({
        data: {
          email: process.env.ADMIN_EMAIL,
          password: hashedPassword,
          name: 'Admin',
          role: 'ADMIN',
          permissions: ['ALL'],
          isActive: true
        }
      });

      console.log('✅ Admin user created successfully');
    } else {
      console.log('ℹ️ Admin user already exists or credentials not provided');
    }

    console.log('🎉 Database initialization completed');

  } catch(error) {
    console.error('❌ Database initialization failed:',error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if(require.main===module) {
  initializeDatabase();
}

module.exports={initializeDatabase};
