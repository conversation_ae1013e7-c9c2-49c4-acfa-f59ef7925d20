const bcrypt=require('bcryptjs');
const {PrismaClient}=require('@prisma/client');

const prisma=new PrismaClient();

async function seedAdmin() {
  try {
    console.log('🌱 Seeding admin user...');

    // Check if admin already exists
    const existingAdmin=await prisma.adminUser.findUnique({
      where: {email: '<EMAIL>'}
    });

    if(existingAdmin) {
      console.log('✅ Admin user already exists');
      return;
    }

    // Hash password
    const hashedPassword=await bcrypt.hash('admin123',12);

    // Create admin user
    const admin=await prisma.adminUser.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        password: hashedPassword,
        role: 'SUPER_ADMIN',
        permissions: [
          'READ_PRODUCTS',
          'WRITE_PRODUCTS',
          'DELETE_PRODUCTS',
          'READ_ORDERS',
          'WRITE_ORDERS',
          'READ_USERS',
          'WRITE_USERS',
          'READ_CATEGORIES',
          'WRITE_CATEGORIES',
          'READ_ADMIN_LOGS',
          'SYSTEM_SETTINGS'
        ],
        isActive: true,
      }
    });

    console.log('✅ Admin user created successfully:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('👤 ID:',admin.id);

  } catch(error) {
    console.error('❌ Error seeding admin:',error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedAdmin(); 