# Vercel Environment Variables Setup

## Required Environment Variables for Vercel Deployment

### 1. Database Configuration
```
DATABASE_URL="your_mongodb_connection_string"
```
Example: `mongodb+srv://username:<EMAIL>/database_name`

### 2. NextAuth Configuration
```
NEXTAUTH_SECRET="your_random_secret_key"
NEXTAUTH_URL="https://your-domain.vercel.app"
```

### 3. Admin Configuration
```
ADMIN_JWT_SECRET="your_admin_jwt_secret"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your_secure_admin_password"
```

### 4. Optional Configuration
```
NODE_ENV="production"
VERCEL_ENV="production"
```

## How to Set Environment Variables in Vercel

### Method 1: Vercel Dashboard
1. Go to your project in Vercel Dashboard
2. Navigate to Settings → Environment Variables
3. Add each variable with its value
4. Select the appropriate environments (Production, Preview, Development)

### Method 2: Vercel CLI
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Set environment variables
vercel env add DATABASE_URL
vercel env add NEXTAUTH_SECRET
vercel env add NEXTAUTH_URL
vercel env add ADMIN_JWT_SECRET
vercel env add ADMIN_EMAIL
vercel env add ADMIN_PASSWORD
```

### Method 3: .env.local (for local development)
Create `.env.local` file in your project root:
```
DATABASE_URL="your_mongodb_connection_string"
NEXTAUTH_SECRET="your_random_secret_key"
NEXTAUTH_URL="http://localhost:3006"
ADMIN_JWT_SECRET="your_admin_jwt_secret"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your_secure_admin_password"
```

## Deployment Steps

1. **Set Environment Variables** (using one of the methods above)
2. **Push to GitHub** (if using GitHub integration)
3. **Deploy to Vercel**:
   ```bash
   vercel --prod
   ```

## Troubleshooting

### Build Errors
- Ensure all environment variables are set
- Check that DATABASE_URL is accessible from Vercel
- Verify Prisma schema is compatible with your database

### Database Connection Issues
- Make sure your MongoDB cluster allows connections from Vercel IPs
- Check if your database URL includes the correct database name
- Verify authentication credentials

### Admin Access Issues
- Ensure ADMIN_EMAIL and ADMIN_PASSWORD are set
- Check that the admin user is created during deployment
- Verify JWT_SECRET is consistent across deployments
