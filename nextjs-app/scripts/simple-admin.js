const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdmin() {
  try {
    console.log('🔧 Tạo tài khoản Admin...');

    const email = '<EMAIL>';
    const password = 'admin123456';
    const name = 'Admin Thời Trang';

    // Check if admin already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      if (existingUser.role === 'ADMIN') {
        console.log('✅ Admin đã tồn tại:', email);
        return;
      } else {
        // Upgrade to admin
        await prisma.user.update({
          where: { email },
          data: { role: 'ADMIN' }
        });
        console.log('✅ Đã nâng cấp user thành admin:', email);
        return;
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create admin user
    const admin = await prisma.user.create({
      data: {
        email,
        name,
        password: hashedPassword,
        role: 'ADMIN',
        isActive: true,
      }
    });

    console.log('✅ Tạo tài khoản admin thành công!');
    console.log(`   Email: ${admin.email}`);
    console.log(`   Mật khẩu: ${password}`);
    console.log(`   Tên: ${admin.name}`);
    console.log('\n🚀 Truy cập admin panel tại: /admin/dang-nhap');

  } catch (error) {
    console.error('❌ Lỗi tạo admin:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin();
