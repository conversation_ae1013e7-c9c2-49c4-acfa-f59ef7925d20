const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateProductImages() {
  try {
    console.log('🖼️ Updating product images...');

    // Update existing products with correct image paths
    const updates = [
      {
        slug: 'ao-thun-cotton-nam-basic',
        images: ['/images/products/fashion-designer-template-product-img-1-400x533.jpg', '/images/products/fashion-designer-template-product-img-2-400x533.jpg']
      },
      {
        slug: 'ao-so-mi-nu-cong-so',
        images: ['/images/products/fashion-designer-template-product-img-4-400x533.jpg', '/images/products/fashion-designer-template-product-img-5-400x533.jpg']
      },
      {
        slug: 'dam-maxi-hoa-nhi',
        images: ['/images/products/fashion-designer-template-product-img-20-400x533.jpg', '/images/products/fashion-designer-template-product-img-25-400x534.jpg']
      },
      {
        slug: 'chan-vay-xoe-midi',
        images: ['/images/products/fashion-designer-template-product-img-1-400x533.jpg', '/images/products/fashion-designer-template-product-img-4-400x533.jpg']
      },
      {
        slug: 'quan-jean-skinny-nu',
        images: ['/images/products/fashion-designer-template-product-img-2-400x533.jpg', '/images/products/fashion-designer-template-product-img-5-400x533.jpg']
      },
      {
        slug: 'quan-tay-nam-cong-so',
        images: ['/images/products/fashion-designer-template-product-img-20-400x533.jpg', '/images/products/fashion-designer-template-product-img-1-400x533.jpg']
      }
    ];

    for (const update of updates) {
      const product = await prisma.product.findUnique({
        where: { slug: update.slug }
      });

      if (product) {
        await prisma.product.update({
          where: { slug: update.slug },
          data: { images: update.images }
        });
        console.log(`✅ Updated images for: ${update.slug}`);
      } else {
        console.log(`⚠️ Product not found: ${update.slug}`);
      }
    }

    // Also update any products that might have broken image paths
    const allProducts = await prisma.product.findMany({
      select: { id: true, slug: true, images: true }
    });

    for (const product of allProducts) {
      let needsUpdate = false;
      const updatedImages = product.images.map(img => {
        // If image path doesn't exist or is broken, use placeholder
        if (!img || img.includes('ao-thun-nam-1.jpg') || img.includes('ao-so-mi-nu-1.jpg') || 
            img.includes('dam-maxi-1.jpg') || img.includes('chan-vay-1.jpg') || 
            img.includes('quan-jean-nu-1.jpg') || img.includes('quan-tay-nam-1.jpg')) {
          needsUpdate = true;
          return '/images/products/fashion-designer-template-product-img-1-400x533.jpg';
        }
        return img;
      });

      if (needsUpdate) {
        await prisma.product.update({
          where: { id: product.id },
          data: { images: updatedImages }
        });
        console.log(`✅ Fixed broken images for: ${product.slug}`);
      }
    }

    console.log('🎉 Product images updated successfully!');

  } catch (error) {
    console.error('❌ Error updating product images:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateProductImages();
