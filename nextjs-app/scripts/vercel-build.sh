#!/bin/bash

echo "🔧 Starting Vercel build process..."

# Generate Prisma Client
echo "📦 Generating Prisma Client..."
npx prisma generate

# Push database schema (for development/preview deployments)
if [ "$VERCEL_ENV" != "production" ]; then
  echo "🗄️ Pushing database schema..."
  npx prisma db push --accept-data-loss
fi

# Build Next.js application
echo "🏗️ Building Next.js application..."
npm run build

echo "✅ Vercel build completed successfully!"
