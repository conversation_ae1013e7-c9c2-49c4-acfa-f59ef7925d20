#!/usr/bin/env node

/**
 * Vercel Post-Install Script
 * This script runs after npm install on Vercel to ensure <PERSON>risma is properly set up
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Running Vercel post-install script...');

try {
  // Check if we're in Vercel environment
  const isVercel = process.env.VERCEL || process.env.VERCEL_ENV;
  
  if (isVercel) {
    console.log('📦 Detected Vercel environment');
    
    // Generate Prisma Client
    console.log('🔄 Generating Prisma Client...');
    execSync('npx prisma generate', { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log('✅ Prisma Client generated successfully');
    
    // Check if schema.prisma exists
    const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
    if (fs.existsSync(schemaPath)) {
      console.log('✅ Prisma schema found');
    } else {
      console.warn('⚠️ Prisma schema not found at:', schemaPath);
    }
    
    // Check if DATABASE_URL is set
    if (process.env.DATABASE_URL) {
      console.log('✅ DATABASE_URL is configured');
    } else {
      console.warn('⚠️ DATABASE_URL environment variable not set');
    }
    
  } else {
    console.log('ℹ️ Not in Vercel environment, skipping Vercel-specific setup');
  }
  
  console.log('🎉 Post-install script completed successfully');
  
} catch (error) {
  console.error('❌ Post-install script failed:', error.message);
  
  // Don't fail the build for Prisma issues in development
  if (!process.env.VERCEL) {
    console.log('ℹ️ Continuing build despite Prisma setup issues (development mode)');
    process.exit(0);
  }
  
  process.exit(1);
}
