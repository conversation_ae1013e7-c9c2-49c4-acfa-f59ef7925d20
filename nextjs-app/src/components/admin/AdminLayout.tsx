'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LayoutDashboard,
  Package,
  FolderOpen,
  ShoppingCart,
  Users,
  LogOut,
  Menu,
  X,
  Bell,
  Search,
} from 'lucide-react';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import { useAdminNotifications } from '../../hooks/useAdminEvents';
import Logo from '../ui/Logo';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { user, logout, loading } = useAdminAuth();
  const { notifications, removeNotification, clearNotifications } = useAdminNotifications();
  const router = useRouter();
  const pathname = usePathname();

  // Close sidebar on mobile when route changes
  useEffect(() => {
    setSidebarOpen(false);
  }, [pathname]);

  // Handle window resize for responsive behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setSidebarOpen(false); // Reset mobile sidebar state on desktop
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const navigationItems = [
    {
      name: 'Tổng Quan',
      href: '/admin',
      icon: <LayoutDashboard className="w-5 h-5" />,
    },
    {
      name: 'Sản Phẩm',
      href: '/admin/san-pham',
      icon: <Package className="w-5 h-5" />,
    },
    {
      name: 'Danh Mục',
      href: '/admin/danh-muc',
      icon: <FolderOpen className="w-5 h-5" />,
    },
    {
      name: 'Đơn Hàng',
      href: '/admin/don-hang',
      icon: <ShoppingCart className="w-5 h-5" />,
    },
    // Temporarily hidden - Wishlist management
    // {
    //   name: 'Yêu Thích',
    //   href: '/admin/yeu-thich',
    //   icon: <Heart className="w-5 h-5" />,
    //   submenu: [
    //     {
    //       name: 'Phân Tích',
    //       href: '/admin/yeu-thich',
    //     },
    //     {
    //       name: 'Quản Lý',
    //       href: '/admin/yeu-thich/quan-ly',
    //     },
    //   ],
    // },
    {
      name: 'Khách Hàng',
      href: '/admin/khach-hang',
      icon: <Users className="w-5 h-5" />,
    },
    // Temporarily hidden - Settings
    // {
    //   name: 'Cài Đặt',
    //   href: '/admin/cai-dat',
    //   icon: <Settings className="w-5 h-5" />,
    // },
  ];

  useEffect(() => {
    if (!loading && !user) {
      router.push('/admin/dang-nhap');
    }
  }, [user, loading, router]);

  const handleLogout = async () => {
    await logout();
    router.push('/admin/dang-nhap');
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const closeSidebar = () => {
    setSidebarOpen(false);
    setShowNotifications(false);
  };

  // Filter navigation items based on search query
  const filteredNavigationItems = navigationItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-40 lg:hidden"
            onClick={closeSidebar}
          >
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Desktop Sidebar */}
      <div className="hidden lg:block lg:fixed lg:inset-y-0 lg:left-0 lg:z-50 lg:w-64 xl:w-72">
        <div className="flex flex-col h-full bg-white shadow-lg border-r border-gray-200">
          {/* Sidebar Header */}
          <div className="flex items-center h-20 px-6 bg-white border-b border-gray-200">
            <Logo
              variant="admin"
              size="lg"
              href="/admin"
              className="flex items-center"
            />
          </div>

          {/* Search Bar */}
          <div className="p-4 bg-gray-50">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Tìm kiếm..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
            {filteredNavigationItems.map((item) => {
              const isActive = pathname === item.href ||
                             (item.href !== '/admin' && pathname.startsWith(item.href));

              return (
                <div key={item.name}>
                  <Link
                    href={item.href}
                    className={`group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      isActive
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <span className={`mr-3 ${
                      isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                    }`}>
                      {item.icon}
                    </span>
                    {item.name}
                  </Link>

                  {/* Submenu for Wishlist */}
                  {item.submenu && pathname.startsWith(item.href) && (
                    <div className="ml-6 mt-1 space-y-1">
                      {item.submenu.map((subItem) => {
                        const isSubActive = pathname === subItem.href;
                        return (
                          <Link
                            key={subItem.name}
                            href={subItem.href}
                            className={`group flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                              isSubActive
                                ? 'bg-blue-50 text-blue-600 font-medium'
                                : 'text-gray-500 hover:bg-gray-50 hover:text-gray-700'
                            }`}
                          >
                            <span className={`w-1.5 h-1.5 rounded-full mr-3 ${
                              isSubActive ? 'bg-blue-500' : 'bg-gray-300'
                            }`} />
                            {subItem.name}
                          </Link>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}

            {filteredNavigationItems.length === 0 && searchQuery && (
              <div className="text-center py-8 text-gray-500">
                <Search className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">Không tìm thấy chức năng nào</p>
              </div>
            )}
          </nav>

          {/* User Section */}
          <div className="border-t border-gray-200">
            {/* Notifications */}
            <div className="p-4">
              <div className="relative">
                <button
                  onClick={() => setShowNotifications(!showNotifications)}
                  className="w-full flex items-center justify-between p-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  <div className="flex items-center">
                    <Bell className="w-4 h-4 mr-3 text-gray-500" />
                    <span>Thông báo</span>
                  </div>
                  {notifications.length > 0 && (
                    <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                      {notifications.length > 9 ? '9+' : notifications.length}
                    </span>
                  )}
                </button>

                {/* Notifications Dropdown */}
                <AnimatePresence>
                  {showNotifications && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="absolute bottom-full left-0 right-0 mb-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
                    >
                      <div className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="text-sm font-medium text-gray-900">Thông báo</h3>
                          {notifications.length > 0 && (
                            <button
                              onClick={clearNotifications}
                              className="text-xs text-blue-600 hover:text-blue-800"
                            >
                              Xóa tất cả
                            </button>
                          )}
                        </div>

                        <div className="max-h-64 overflow-y-auto">
                          {notifications.length === 0 ? (
                            <div className="text-center py-6 text-gray-500">
                              <Bell className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                              <p className="text-sm">Không có thông báo mới</p>
                            </div>
                          ) : (
                            <div className="space-y-2">
                              {notifications.map((notification) => (
                                <div
                                  key={notification.timestamp}
                                  className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                                  onClick={() => removeNotification(notification.timestamp)}
                                >
                                  <p className="text-sm text-gray-900">{notification.message}</p>
                                  <p className="text-xs text-gray-500 mt-1">
                                    {new Date(notification.timestamp).toLocaleString('vi-VN')}
                                  </p>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* User Profile */}
            <div className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {user.name?.charAt(0) || user.email.charAt(0)}
                    </span>
                  </div>
                </div>
                <div className="ml-3 flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {user.name || user.email}
                  </p>
                  <p className="text-xs text-gray-500 truncate">{user.email}</p>
                </div>
                <button
                  onClick={handleLogout}
                  className="ml-2 p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-md"
                  title="Đăng xuất"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ x: '-100%' }}
            animate={{ x: 0 }}
            exit={{ x: '-100%' }}
            transition={{
              type: 'spring',
              stiffness: 300,
              damping: 30,
            }}
            className="fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-xl lg:hidden flex flex-col"
          >
            {/* Mobile Sidebar Header - Clean Design */}
            <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200 bg-white">
              <Logo
                variant="admin"
                size="md"
                href="/admin"
                className="flex items-center"
              />
              <button
                onClick={closeSidebar}
                className="flex items-center justify-center w-8 h-8 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Mobile Search Bar - Simplified */}
            <div className="p-4 bg-gray-50">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Tìm kiếm..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white"
                />
              </div>
            </div>

            {/* Mobile Navigation - Clean Design */}
            <nav className="flex-1 px-4 py-2 space-y-1 overflow-y-auto">
              {filteredNavigationItems.map((item) => {
                const isActive = pathname === item.href ||
                               (item.href !== '/admin' && pathname.startsWith(item.href));

                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors ${
                      isActive
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    }`}
                    onClick={closeSidebar}
                  >
                    <span className={`mr-3 ${
                      isActive ? 'text-white' : 'text-gray-500 group-hover:text-gray-700'
                    }`}>
                      {item.icon}
                    </span>
                    <span className="flex-1">{item.name}</span>
                    {isActive && (
                      <div className="w-2 h-2 bg-white rounded-full" />
                    )}
                  </Link>
                );
              })}
            </nav>

            {/* Mobile User Profile - Clean Design */}
            <div className="border-t border-gray-200 bg-white p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {user.name?.charAt(0) || user.email.charAt(0)}
                    </span>
                  </div>
                </div>
                <div className="ml-3 flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {user.name || user.email}
                  </p>
                  <p className="text-xs text-gray-500 truncate">{user.email}</p>
                </div>
                <button
                  onClick={handleLogout}
                  className="ml-2 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Đăng xuất"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main content */}
      <div className="lg:pl-64 xl:pl-72">
        {/* Mobile header - Clean and Professional */}
        <div className="lg:hidden sticky top-0 z-30 bg-white shadow-sm">
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-100">
            <button
              onClick={toggleSidebar}
              className="flex items-center justify-center w-10 h-10 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
            >
              <Menu className="w-5 h-5" />
            </button>

            <div className="flex items-center">
              <Logo
                variant="admin"
                size="md"
                className="flex items-center"
              />
            </div>

            {/* Notification button for mobile */}
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative flex items-center justify-center w-10 h-10 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
            >
              <Bell className="w-5 h-5" />
              {notifications.length > 0 && (
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              )}
            </button>
          </div>
        </div>

        {/* Page content */}
        <main className="min-h-screen bg-gray-50">
          <div className="px-4 py-4 sm:px-6 lg:px-8 lg:py-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
