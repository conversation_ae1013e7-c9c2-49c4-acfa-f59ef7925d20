'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, X, Edit2, Check, AlertCircle } from 'lucide-react';

interface ArrayFieldManagerProps {
  label: string;
  values: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
  maxItems?: number;
  required?: boolean;
  error?: string;
  colorScheme?: 'blue' | 'purple' | 'green' | 'yellow' | 'red';
}

// Note: This component allows editing existing values and adding new ones.
// It does NOT prevent form submission if no new values are added.
// Existing values are always preserved and can be submitted as-is.

export default function ArrayFieldManager({
  label,
  values,
  onChange,
  placeholder = 'Nhập giá trị...',
  maxItems = 20,
  required = false,
  error,
  colorScheme = 'blue'
}: ArrayFieldManagerProps) {
  const [newValue, setNewValue] = useState('');
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editValue, setEditValue] = useState('');

  const colorClasses = {
    blue: 'bg-blue-100 text-blue-800 border-blue-200',
    purple: 'bg-purple-100 text-purple-800 border-purple-200',
    green: 'bg-green-100 text-green-800 border-green-200',
    yellow: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    red: 'bg-red-100 text-red-800 border-red-200'
  };

  const handleAddValue = () => {
    const trimmedValue = newValue.trim();
    // Allow adding new values only if they don't exist and under max limit
    if (trimmedValue && !values.includes(trimmedValue) && values.length < maxItems) {
      onChange([...values, trimmedValue]);
      setNewValue('');
    }
    // Note: This validation is only for adding NEW values, not for preventing form submission
  };

  const handleRemoveValue = (index: number) => {
    const newValues = values.filter((_, i) => i !== index);
    onChange(newValues);
  };

  const handleEditStart = (index: number) => {
    setEditingIndex(index);
    setEditValue(values[index]);
  };

  const handleEditSave = () => {
    const trimmedValue = editValue.trim();
    if (trimmedValue && editingIndex !== null) {
      // Check if the new value already exists (excluding current item)
      const existsElsewhere = values.some((value, index) => 
        value === trimmedValue && index !== editingIndex
      );
      
      if (!existsElsewhere) {
        const newValues = [...values];
        newValues[editingIndex] = trimmedValue;
        onChange(newValues);
      }
    }
    setEditingIndex(null);
    setEditValue('');
  };

  const handleEditCancel = () => {
    setEditingIndex(null);
    setEditValue('');
  };

  const handleKeyPress = (e: React.KeyboardEvent, action: 'add' | 'edit') => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (action === 'add') {
        handleAddValue();
      } else {
        handleEditSave();
      }
    } else if (e.key === 'Escape' && action === 'edit') {
      handleEditCancel();
    }
  };

  const isDuplicate = (value: string) => {
    return values.includes(value.trim());
  };

  const canAdd = newValue.trim() && !isDuplicate(newValue) && values.length < maxItems;

  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-700">
        {label} {required && <span className="text-red-500">*</span>}
        <span className="text-gray-500 font-normal">({values.length}/{maxItems})</span>
      </label>

      {/* Current Values */}
      {values.length > 0 && (
        <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-md border border-gray-200">
          <AnimatePresence>
            {values.map((value, index) => (
              <motion.div
                key={`${value}-${index}`}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${colorClasses[colorScheme]}`}
              >
                {editingIndex === index ? (
                  <div className="flex items-center space-x-1">
                    <input
                      type="text"
                      value={editValue}
                      onChange={(e) => setEditValue(e.target.value)}
                      onKeyDown={(e) => handleKeyPress(e, 'edit')}
                      className="bg-white border border-gray-300 rounded px-2 py-1 text-xs w-20 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      autoFocus
                    />
                    <button
                      type="button"
                      onClick={handleEditSave}
                      className="p-1 text-green-600 hover:text-green-800"
                    >
                      <Check className="w-3 h-3" />
                    </button>
                    <button
                      type="button"
                      onClick={handleEditCancel}
                      className="p-1 text-gray-600 hover:text-gray-800"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ) : (
                  <>
                    <span className="mr-2">{value}</span>
                    <div className="flex items-center space-x-1">
                      <button
                        type="button"
                        onClick={() => handleEditStart(index)}
                        className="p-1 text-gray-600 hover:text-gray-800"
                      >
                        <Edit2 className="w-3 h-3" />
                      </button>
                      <button
                        type="button"
                        onClick={() => handleRemoveValue(index)}
                        className="p-1 text-red-600 hover:text-red-800"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  </>
                )}
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Add New Value */}
      {values.length < maxItems && (
        <div className="flex space-x-2">
          <div className="flex-1">
            <input
              type="text"
              value={newValue}
              onChange={(e) => setNewValue(e.target.value)}
              onKeyDown={(e) => handleKeyPress(e, 'add')}
              placeholder={placeholder}
              className={`w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${
                error ? 'border-red-300' : 'border-gray-300'
              }`}
            />
          </div>
          <button
            type="button"
            onClick={handleAddValue}
            disabled={!canAdd}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              canAdd
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Validation Messages */}
      <div className="space-y-1">
        {newValue.trim() && isDuplicate(newValue) && (
          <div className="flex items-center space-x-1 text-yellow-600">
            <AlertCircle className="w-4 h-4" />
            <span className="text-xs">Giá trị này đã tồn tại</span>
          </div>
        )}
        
        {values.length >= maxItems && (
          <div className="flex items-center space-x-1 text-orange-600">
            <AlertCircle className="w-4 h-4" />
            <span className="text-xs">Đã đạt giới hạn tối đa {maxItems} mục</span>
          </div>
        )}
        
        {error && (
          <div className="flex items-center space-x-1 text-red-600">
            <AlertCircle className="w-4 h-4" />
            <span className="text-xs">{error}</span>
          </div>
        )}
      </div>

      {/* Help Text */}
      <p className="text-xs text-gray-500">
        Nhấn Enter để thêm nhanh. Click vào mục để chỉnh sửa.
      </p>
    </div>
  );
}
