'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Package,
  FolderOpen,
  ShoppingCart,
  Users,
  Heart,
  BarChart3,
  TrendingUp,
  Plus,
  Bell,
  RefreshCw,
} from 'lucide-react';

interface QuickAction {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  hoverColor: string;
}

interface Activity {
  id: string;
  type: string;
  title: string;
  description: string;
  time: string;
  timeFormatted: string;
  icon: string;
  color: string;
  bgColor: string;
}

interface QuickStats {
  todayOrders: number;
  activeProducts: number;
  newCustomers: number;
  monthlyRevenue: number;
}

interface DashboardSidebarProps {
  className?: string;
}

interface OrderData {
  createdAt: string;
}

export default function DashboardSidebar({ className = '' }: DashboardSidebarProps) {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [quickStats, setQuickStats] = useState<QuickStats>({
    todayOrders: 0,
    activeProducts: 0,
    newCustomers: 0,
    monthlyRevenue: 0,
  });
  const [loading, setLoading] = useState(true);

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)}M`;
    }
    if (amount >= 1000) {
      return `${(amount / 1000).toFixed(1)}K`;
    }
    return amount.toString();
  };

  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'ShoppingCart': return <ShoppingCart className="w-4 h-4" />;
      case 'Package': return <Package className="w-4 h-4" />;
      case 'Users': return <Users className="w-4 h-4" />;
      case 'Heart': return <Heart className="w-4 h-4" />;
      default: return <Bell className="w-4 h-4" />;
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch activities
        const activitiesResponse = await fetch('/api/admin/dashboard/activities', {
          credentials: 'include',
        });

        // Fetch stats
        const statsResponse = await fetch('/api/admin/dashboard/stats', {
          credentials: 'include',
        });

        if (activitiesResponse.ok) {
          const activitiesResult = await activitiesResponse.json();
          if (activitiesResult.success) {
            setActivities(activitiesResult.data);
          }
        }

        if (statsResponse.ok) {
          const statsResult = await statsResponse.json();
          if (statsResult.success) {
            const stats = statsResult.data;

            // Calculate today's orders
            const today = new Date();
            const todayOrders = stats.recentOrders?.filter((order: OrderData) => {
              const orderDate = new Date(order.createdAt);
              return orderDate.toDateString() === today.toDateString();
            }).length || 0;

            // Calculate new customers this month
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const newCustomers = stats.recentOrders?.reduce((count: number, order: OrderData) => {
              const orderDate = new Date(order.createdAt);
              return orderDate >= startOfMonth ? count + 1 : count;
            }, 0) || 0;

            setQuickStats({
              todayOrders,
              activeProducts: stats.totalProducts || 0,
              newCustomers,
              monthlyRevenue: stats.monthlyRevenue || 0,
            });
          }
        }
      } catch (error) {
        console.error('Error fetching sidebar data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const quickActions: QuickAction[] = [
    {
      title: 'Thêm Sản Phẩm',
      description: 'Tạo sản phẩm mới',
      href: '/admin/san-pham/them-moi',
      icon: <Plus className="w-5 h-5" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      hoverColor: 'hover:bg-blue-100',
    },
    {
      title: 'Quản Lý Sản Phẩm',
      description: 'Xem tất cả sản phẩm',
      href: '/admin/san-pham',
      icon: <Package className="w-5 h-5" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      hoverColor: 'hover:bg-green-100',
    },
    {
      title: 'Đơn Hàng Mới',
      description: 'Xem đơn hàng chờ xử lý',
      href: '/admin/don-hang',
      icon: <ShoppingCart className="w-5 h-5" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      hoverColor: 'hover:bg-orange-100',
    },
    {
      title: 'Danh Mục',
      description: 'Quản lý danh mục',
      href: '/admin/danh-muc',
      icon: <FolderOpen className="w-5 h-5" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      hoverColor: 'hover:bg-purple-100',
    },
    {
      title: 'Khách Hàng',
      description: 'Quản lý khách hàng',
      href: '/admin/khach-hang',
      icon: <Users className="w-5 h-5" />,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      hoverColor: 'hover:bg-indigo-100',
    },
    // {
    //   title: 'Báo Cáo',
    //   description: 'Xem thống kê',
    //   href: '/admin/bao-cao',
    //   icon: <BarChart3 className="w-5 h-5" />,
    //   color: 'text-pink-600',
    //   bgColor: 'bg-pink-50',
    //   hoverColor: 'hover:bg-pink-100',
    // },
  ];

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Quick Actions */}
      <div className="p-6 border-b border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
          Thao Tác Nhanh
        </h3>
        
        <div className="space-y-3">
          {quickActions.map((action, index) => (
            <motion.div
              key={action.title}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Link
                href={action.href}
                className={`flex items-center p-3 rounded-lg transition-all duration-200 ${action.bgColor} ${action.hoverColor} group`}
              >
                <div className={`flex-shrink-0 ${action.color}`}>
                  {action.icon}
                </div>
                <div className="ml-3 flex-1 min-w-0">
                  <p className={`text-sm font-medium ${action.color} group-hover:font-semibold`}>
                    {action.title}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {action.description}
                  </p>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Recent Activities */}
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Bell className="w-5 h-5 mr-2 text-orange-600" />
          Hoạt Động Gần Đây
        </h3>
        
        <div className="space-y-3">
          {loading ? (
            <div className="flex items-center justify-center py-4">
              <RefreshCw className="w-5 h-5 animate-spin text-gray-400 mr-2" />
              <span className="text-sm text-gray-500">Đang tải...</span>
            </div>
          ) : activities.length > 0 ? (
            activities.map((activity, index) => (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className={`flex-shrink-0 p-2 rounded-full ${activity.bgColor}`}>
                  <div className={activity.color}>
                    {getIconComponent(activity.icon)}
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {activity.title}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {activity.description}
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    {activity.timeFormatted}
                  </p>
                </div>
              </motion.div>
            ))
          ) : (
            <div className="text-center py-4">
              <Bell className="w-8 h-8 text-gray-300 mx-auto mb-2" />
              <p className="text-sm text-gray-500">Chưa có hoạt động nào</p>
            </div>
          )}
        </div>

        <div className="mt-4 pt-4 border-t border-gray-100">
          <Link
            href="/admin/hoat-dong"
            className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center"
          >
            Xem tất cả hoạt động
            <TrendingUp className="w-4 h-4 ml-1" />
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="p-6 bg-gray-50 rounded-b-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2 text-green-600" />
          Thống Kê Nhanh
        </h3>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-white rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {loading ? '...' : quickStats.todayOrders}
            </div>
            <div className="text-xs text-gray-500">Đơn hàng hôm nay</div>
          </div>
          <div className="text-center p-3 bg-white rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {loading ? '...' : quickStats.activeProducts}
            </div>
            <div className="text-xs text-gray-500">Sản phẩm đang bán</div>
          </div>
          <div className="text-center p-3 bg-white rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {loading ? '...' : quickStats.newCustomers}
            </div>
            <div className="text-xs text-gray-500">Khách hàng mới</div>
          </div>
          <div className="text-center p-3 bg-white rounded-lg">
            <div className="text-2xl font-bold text-orange-600">
              {loading ? '...' : formatCurrency(quickStats.monthlyRevenue)}
            </div>
            <div className="text-xs text-gray-500">Doanh thu tháng</div>
          </div>
        </div>
      </div>
    </div>
  );
}
