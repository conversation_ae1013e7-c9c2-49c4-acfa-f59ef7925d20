'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Trash2 } from 'lucide-react';
import ConfirmationDialog from './ConfirmationDialog';
import { useToast } from '../ui/Toast';

interface OrderActionsProps {
  orderId: string;
  orderNumber: string;
  currentStatus: string;
  onOrderUpdated?: () => void;
}

const OrderActions: React.FC<OrderActionsProps> = ({
  orderId,
  orderNumber,
  currentStatus,
  onOrderUpdated
}) => {
  const router = useRouter();
  const { showToast } = useToast();
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Business rules for showing actions
  const canCancel = currentStatus !== 'DELIVERED' && currentStatus !== 'CANCELLED';
  const canDelete = true; // ✅ ALLOW DELETION FOR ALL STATUSES - NO RESTRICTIONS

  const handleCancelOrder = async (reason?: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/cancel`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cancelReason: reason }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Lỗi khi hủy đơn hàng');
      }

      showToast(data.message || `Đơn hàng ${orderNumber} đã được hủy thành công`, 'success');
      setShowCancelDialog(false);
      
      // Refresh order data
      if (onOrderUpdated) {
        onOrderUpdated();
      }

    } catch (error) {
      console.error('Error cancelling order:', error);
      showToast(
        error instanceof Error ? error.message : 'Lỗi khi hủy đơn hàng',
        'error'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteOrder = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/delete`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Lỗi khi xóa đơn hàng');
      }

      showToast(data.message || `Đơn hàng ${orderNumber} đã được xóa thành công`, 'success');
      
      // Redirect to orders list after successful deletion
      router.push('/admin/don-hang');

    } catch (error) {
      console.error('Error deleting order:', error);
      showToast(
        error instanceof Error ? error.message : 'Lỗi khi xóa đơn hàng',
        'error'
      );
      setIsLoading(false);
    }
  };

  // Don't render if no actions are available
  if (!canCancel && !canDelete) {
    return null;
  }

  return (
    <>
      <div className="flex justify-end">
        {/* Cancel Order Button */}
        {/* {canCancel && (
          <button
            onClick={() => setShowCancelDialog(true)}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-700 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
          >
            <XCircle className="w-4 h-4 mr-2" />
            Hủy đơn hàng
          </button>
        )} */}

        {/* Delete Order Button - Direct deletion without modal */}
        {canDelete && (
          <button
            onClick={handleDeleteOrder}
            disabled={isLoading}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Trash2 className="w-8 h-4 mr-2" />
            {isLoading ? 'Đang xóa...' : 'Xóa đơn hàng'}
          </button>
        )}
      </div>

      {/* Cancel Order Dialog */}
      <ConfirmationDialog
        isOpen={showCancelDialog}
        onClose={() => setShowCancelDialog(false)}
        onConfirm={handleCancelOrder}
        title="Hủy đơn hàng"
        message={`Bạn có chắc chắn muốn hủy đơn hàng ${orderNumber}? Hành động này sẽ thay đổi trạng thái đơn hàng thành "Đã hủy" và không thể hoàn tác.`}
        confirmText="Hủy đơn hàng"
        cancelText="Không hủy"
        type="warning"
        showReasonInput={true}
        reasonPlaceholder="Nhập lý do hủy đơn hàng (tùy chọn)..."
        isLoading={isLoading}
      />


    </>
  );
};

export default OrderActions;
