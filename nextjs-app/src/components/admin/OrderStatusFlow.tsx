'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Info, 
  X, 
  ArrowRight, 
  Clock, 
  CheckCircle, 
  Package, 
  Truck, 
  Home,
  XCircle,
  AlertTriangle
} from 'lucide-react';

interface OrderStatusFlowProps {
  currentStatus: string;
}

const statusConfig = {
  PENDING: {
    label: 'Chờ xử lý',
    icon: Clock,
    color: 'text-yellow-600 bg-yellow-100',
    description: 'Đơn hàng vừa được tạo, chờ xác nhận'
  },
  CONFIRMED: {
    label: 'Đã xác nhận',
    icon: CheckCircle,
    color: 'text-blue-600 bg-blue-100',
    description: 'Đơn hàng đã được xác nhận và chuẩn bị xử lý'
  },
  PROCESSING: {
    label: 'Đang xử lý',
    icon: Package,
    color: 'text-purple-600 bg-purple-100',
    description: '<PERSON><PERSON> chuẩn bị hàng và đóng gói'
  },
  SHIPPED: {
    label: 'Đã giao vận',
    icon: Truck,
    color: 'text-indigo-600 bg-indigo-100',
    description: 'Đơn hàng đã được giao cho đơn vị vận chuyển'
  },
  DELIVERED: {
    label: 'Đã giao hàng',
    icon: Home,
    color: 'text-green-600 bg-green-100',
    description: 'Đơn hàng đã được giao thành công'
  },
  CANCELLED: {
    label: 'Đã hủy',
    icon: XCircle,
    color: 'text-red-600 bg-red-100',
    description: 'Đơn hàng đã bị hủy'
  }
};

const statusFlow = [
  'PENDING',
  'CONFIRMED', 
  'PROCESSING',
  'SHIPPED',
  'DELIVERED'
];

const businessRules = [
  {
    icon: AlertTriangle,
    title: 'Quy tắc chuyển trạng thái',
    rules: [
      'Đơn hàng chỉ có thể chuyển theo thứ tự: Chờ xử lý → Đã xác nhận → Đang xử lý → Đã giao vận → Đã giao hàng',
      'Đơn hàng có thể được hủy ở bất kỳ trạng thái nào trước khi giao hàng',
      'Đơn hàng đã giao hàng không thể thay đổi trạng thái',
      'Đơn hàng đã hủy không thể chuyển sang trạng thái khác',
      'Đơn hàng có thể được xóa ở BẤT KỲ TRẠNG THÁI NÀO (bao gồm cả đã giao hàng)'
    ]
  }
];

const OrderStatusFlow: React.FC<OrderStatusFlowProps> = ({ currentStatus }) => {
  const [isOpen, setIsOpen] = useState(false);

  const getCurrentStatusIndex = () => {
    return statusFlow.indexOf(currentStatus);
  };

  const isStatusCompleted = (status: string) => {
    const currentIndex = getCurrentStatusIndex();
    const statusIndex = statusFlow.indexOf(status);
    return statusIndex <= currentIndex && currentStatus !== 'CANCELLED';
  };

  const isStatusCurrent = (status: string) => {
    return status === currentStatus;
  };

  return (
    <>
      {/* Info Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
      >
        <Info className="w-4 h-4 mr-2" />
        Quy trình trạng thái
      </button>

      {/* Modal */}
      <AnimatePresence>
        {isOpen && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
              {/* Background overlay */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 transition-opacity bg-opacity-75"
                onClick={() => setIsOpen(false)}
              />

              <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

              {/* Modal content */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: 20 }}
                className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6"
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Quy trình trạng thái đơn hàng
                  </h3>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>

                {/* Status Flow Visualization */}
                <div className="mb-8">
                  <h4 className="text-md font-medium text-gray-900 mb-4">Luồng trạng thái chuẩn</h4>
                  <div className="flex flex-wrap items-center gap-2 sm:gap-4">
                    {statusFlow.map((status, index) => {
                      const config = statusConfig[status as keyof typeof statusConfig];
                      const Icon = config.icon;
                      const isCompleted = isStatusCompleted(status);
                      const isCurrent = isStatusCurrent(status);
                      
                      return (
                        <React.Fragment key={status}>
                          {/* Status Item */}
                          <div className="flex flex-col items-center">
                            <div
                              className={`
                                flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all
                                ${isCurrent 
                                  ? 'border-blue-500 bg-blue-500 text-white shadow-lg' 
                                  : isCompleted 
                                    ? 'border-green-500 bg-green-500 text-white'
                                    : 'border-gray-300 bg-gray-100 text-gray-400'
                                }
                              `}
                            >
                              <Icon className="w-5 h-5" />
                            </div>
                            <div className="mt-2 text-center">
                              <p className={`text-xs font-medium ${isCurrent ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'}`}>
                                {config.label}
                              </p>
                              <p className="text-xs text-gray-400 max-w-20 leading-tight">
                                {config.description}
                              </p>
                            </div>
                          </div>

                          {/* Arrow */}
                          {index < statusFlow.length - 1 && (
                            <ArrowRight className={`w-4 h-4 mx-1 ${isCompleted && index < getCurrentStatusIndex() ? 'text-green-500' : 'text-gray-300'}`} />
                          )}
                        </React.Fragment>
                      );
                    })}
                  </div>

                  {/* Cancelled Status */}
                  <div className="mt-6 p-4 bg-red-50 rounded-lg border border-red-200">
                    <div className="flex items-center">
                      <XCircle className="w-5 h-5 text-red-600 mr-2" />
                      <span className="text-sm font-medium text-red-800">Trạng thái đặc biệt: Đã hủy</span>
                    </div>
                    <p className="text-sm text-red-700 mt-1">
                      Đơn hàng có thể được hủy từ bất kỳ trạng thái nào trước khi giao hàng. 
                      Sau khi hủy, không thể chuyển sang trạng thái khác.
                    </p>
                  </div>
                </div>

                {/* Business Rules */}
                <div>
                  {businessRules.map((section, index) => {
                    const Icon = section.icon;
                    return (
                      <div key={index} className="mb-6">
                        <div className="flex items-center mb-3">
                          <Icon className="w-5 h-5 text-amber-600 mr-2" />
                          <h4 className="text-md font-medium text-gray-900">{section.title}</h4>
                        </div>
                        <ul className="space-y-2">
                          {section.rules.map((rule, ruleIndex) => (
                            <li key={ruleIndex} className="flex items-start">
                              <div className="w-2 h-2 bg-amber-400 rounded-full mt-2 mr-3 flex-shrink-0" />
                              <span className="text-sm text-gray-700 leading-relaxed">{rule}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    );
                  })}
                </div>

                {/* Footer */}
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => setIsOpen(false)}
                    className="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                  >
                    Đã hiểu
                  </button>
                </div>
              </motion.div>
            </div>
          </div>
        )}
      </AnimatePresence>
    </>
  );
};

export default OrderStatusFlow;
