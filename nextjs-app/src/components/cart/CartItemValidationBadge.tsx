'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, AlertTriangle, Clock } from 'lucide-react';

interface CartItemValidationBadgeProps {
  hasRequiredSize: boolean;
  hasRequiredColor: boolean;
  selectedSize?: string | null;
  selectedColor?: string | null;
  compact?: boolean;
}

export default function CartItemValidationBadge({
  hasRequiredSize,
  hasRequiredColor,
  selectedSize,
  selectedColor,
  compact = false
}: CartItemValidationBadgeProps) {
  // Determine validation status
  const sizeValid = !hasRequiredSize || (hasRequiredSize && selectedSize);
  const colorValid = !hasRequiredColor || (hasRequiredColor && selectedColor);
  const isFullyValid = sizeValid && colorValid;
  
  // Determine what's missing
  const missingSizes = hasRequiredSize && !selectedSize;
  const missingColors = hasRequiredColor && !selectedColor;
  
  if (isFullyValid) {
    return (
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className={`inline-flex items-center ${
          compact 
            ? 'px-2 py-1 text-xs' 
            : 'px-3 py-1.5 text-sm'
        } bg-green-50 text-green-700 rounded-md font-medium border border-green-200`}
      >
        <CheckCircle className={`${compact ? 'w-3 h-3' : 'w-4 h-4'} mr-1`} />
        <span>Hoàn tất</span>
      </motion.div>
    );
  }

  if (missingSizes && missingColors) {
    return (
      <motion.div
        animate={{ 
          scale: [1, 1.05, 1],
          opacity: [0.8, 1, 0.8]
        }}
        transition={{
          duration: 2,
          repeat: Infinity
        }}
        className={`inline-flex items-center ${
          compact 
            ? 'px-2 py-1 text-xs' 
            : 'px-3 py-1.5 text-sm'
        } bg-red-50 text-red-700 rounded-md font-medium border border-red-200`}
      >
        <AlertTriangle className={`${compact ? 'w-3 h-3' : 'w-4 h-4'} mr-1`} />
        <span>Thiếu size & màu</span>
      </motion.div>
    );
  }

  if (missingSizes) {
    return (
      <motion.div
        animate={{ 
          scale: [1, 1.05, 1],
          opacity: [0.8, 1, 0.8]
        }}
        transition={{
          duration: 2,
          repeat: Infinity
        }}
        className={`inline-flex items-center ${
          compact 
            ? 'px-2 py-1 text-xs' 
            : 'px-3 py-1.5 text-sm'
        } bg-orange-50 text-orange-700 rounded-md font-medium border border-orange-200`}
      >
        <Clock className={`${compact ? 'w-3 h-3' : 'w-4 h-4'} mr-1`} />
        <span>Thiếu size</span>
      </motion.div>
    );
  }

  if (missingColors) {
    return (
      <motion.div
        animate={{ 
          scale: [1, 1.05, 1],
          opacity: [0.8, 1, 0.8]
        }}
        transition={{
          duration: 2,
          repeat: Infinity
        }}
        className={`inline-flex items-center ${
          compact 
            ? 'px-2 py-1 text-xs' 
            : 'px-3 py-1.5 text-sm'
        } bg-blue-50 text-blue-700 rounded-md font-medium border border-blue-200`}
      >
        <Clock className={`${compact ? 'w-3 h-3' : 'w-4 h-4'} mr-1`} />
        <span>Thiếu màu</span>
      </motion.div>
    );
  }

  return null;
}
