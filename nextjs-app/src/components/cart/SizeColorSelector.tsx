'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Check, AlertCircle } from 'lucide-react';

interface SizeColorSelectorProps {
  sizes: string[];
  colors: string[];
  selectedSize?: string | null;
  selectedColor?: string | null;
  onSizeChange: (size: string) => void;
  onColorChange: (color: string) => void;
  disabled?: boolean;
  showLabels?: boolean;
  compact?: boolean;
  showValidationErrors?: boolean;
}

export default function SizeColorSelector({
  sizes,
  colors,
  selectedSize,
  selectedColor,
  onSizeChange,
  onColorChange,
  disabled = false,
  showLabels = true,
  compact = false,
}: SizeColorSelectorProps) {
  const [sizeDropdownOpen, setSizeDropdownOpen] = useState(false);
  const [colorDropdownOpen, setColorDropdownOpen] = useState(false);

  // Validation helpers

  const handleSizeSelect = (size: string) => {
    onSizeChange(size);
    setSizeDropdownOpen(false);
  };

  const handleColorSelect = (color: string) => {
    onColorChange(color);
    setColorDropdownOpen(false);
  };

  const getColorDisplay = (color: string) => {
    // Map color names to display colors
    const colorMap: { [key: string]: string } = {
      'đen': '#000000',
      'trắng': '#FFFFFF',
      'đỏ': '#DC2626',
      'xanh': '#2563EB',
      'vàng': '#EAB308',
      'hồng': '#EC4899',
      'tím': '#7C3AED',
      'xám': '#6B7280',
      'nâu': '#92400E',
      'cam': '#EA580C',
      'xanh lá': '#16A34A',
      'be': '#D4B896'
    };

    const normalizedColor = color.toLowerCase();
    return colorMap[normalizedColor] || '#6B7280';
  };

  if (compact) {
    return (
      <div className="flex items-center space-x-3">
        {/* Compact Size Selector */}
        {sizes.length > 0 && (
          <div className="relative">
            <button
              onClick={() => !disabled && setSizeDropdownOpen(!sizeDropdownOpen)}
              disabled={disabled}
              className={`inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg border transition-all ${
                disabled
                  ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                  : selectedSize
                  ? 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100'
                  : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
              }`}
            >
              <span className="mr-1">Size:</span>
              <span className="font-bold">{selectedSize || 'Chọn'}</span>
              <ChevronDown className="w-3 h-3 ml-1" />
            </button>

            <AnimatePresence>
              {sizeDropdownOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[120px]"
                >
                  {sizes.map((size) => (
                    <button
                      key={size}
                      onClick={() => handleSizeSelect(size)}
                      className={`w-full px-3 py-2 text-left text-xs font-medium hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                        selectedSize === size ? 'bg-pink-50 text-pink-700' : 'text-gray-700'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span>{size}</span>
                        {selectedSize === size && <Check className="w-3 h-3 text-pink-600" />}
                      </div>
                    </button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}

        {/* Compact Color Selector */}
        {colors.length > 0 && (
          <div className="relative">
            <button
              onClick={() => !disabled && setColorDropdownOpen(!colorDropdownOpen)}
              disabled={disabled}
              className={`inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg border transition-all ${
                disabled
                  ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                  : selectedColor
                  ? 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100'
                  : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
              }`}
            >
              <span className="mr-1">Màu:</span>
              <div className="flex items-center">
                {selectedColor && (
                  <div
                    className="w-3 h-3 rounded-full border border-gray-300 mr-1"
                    style={{ backgroundColor: getColorDisplay(selectedColor) }}
                  />
                )}
                <span className="font-bold">{selectedColor || 'Chọn'}</span>
              </div>
              <ChevronDown className="w-3 h-3 ml-1" />
            </button>

            <AnimatePresence>
              {colorDropdownOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]"
                >
                  {colors.map((color) => (
                    <button
                      key={color}
                      onClick={() => handleColorSelect(color)}
                      className={`w-full px-3 py-2 text-left text-xs font-medium hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                        selectedColor === color ? 'bg-blue-50 text-purple-700' : 'text-gray-700'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full border border-gray-300 mr-2"
                            style={{ backgroundColor: getColorDisplay(color) }}
                          />
                          <span>{color}</span>
                        </div>
                        {selectedColor === color && <Check className="w-3 h-3 text-purple-600" />}
                      </div>
                    </button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Size Selector */}
      {sizes.length > 0 && (
        <div>
          {showLabels && (
            <label className="block text-sm font-medium text-gray-700 mb-2">
              📏 Kích thước:
            </label>
          )}
          <div className="relative">
            <button
              onClick={() => !disabled && setSizeDropdownOpen(!sizeDropdownOpen)}
              disabled={disabled}
              className={`w-full flex items-center justify-between px-4 py-3 text-left border rounded-lg transition-all ${
                disabled
                  ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                  : selectedSize
                  ? 'bg-blue-50 text-blue-700 border-blue-300 hover:border-blue-400'
                  : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
              }`}
            >
              <div className="flex items-center">
                <span className="font-medium">
                  {selectedSize || 'Chọn kích thước'}
                </span>
                {!selectedSize && (
                  <AlertCircle className="w-4 h-4 ml-2 text-orange-500" />
                )}
              </div>
              <ChevronDown className="w-5 h-5" />
            </button>

            <AnimatePresence>
              {sizeDropdownOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-xl shadow-lg z-50 max-h-48 overflow-y-auto"
                >
                  {sizes.map((size) => (
                    <button
                      key={size}
                      onClick={() => handleSizeSelect(size)}
                      className={`w-full px-4 py-3 text-left font-medium hover:bg-gray-50 first:rounded-t-xl last:rounded-b-xl transition-colors ${
                        selectedSize === size ? 'bg-pink-50 text-pink-700' : 'text-gray-700'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span>{size}</span>
                        {selectedSize === size && <Check className="w-5 h-5 text-pink-600" />}
                      </div>
                    </button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      )}

      {/* Color Selector */}
      {colors.length > 0 && (
        <div>
          {showLabels && (
            <label className="block text-sm font-medium text-gray-700 mb-2">
              🎨 Màu sắc:
            </label>
          )}
          <div className="relative">
            <button
              onClick={() => !disabled && setColorDropdownOpen(!colorDropdownOpen)}
              disabled={disabled}
              className={`w-full flex items-center justify-between px-4 py-3 text-left border rounded-lg transition-all ${
                disabled
                  ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                  : selectedColor
                  ? 'bg-green-50 text-green-700 border-green-300 hover:border-green-400'
                  : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
              }`}
            >
              <div className="flex items-center">
                {selectedColor && (
                  <div
                    className="w-5 h-5 rounded-full border border-gray-300 mr-3"
                    style={{ backgroundColor: getColorDisplay(selectedColor) }}
                  />
                )}
                <span className="font-medium">
                  {selectedColor || 'Chọn màu sắc'}
                </span>
                {!selectedColor && (
                  <AlertCircle className="w-4 h-4 ml-2 text-orange-500" />
                )}
              </div>
              <ChevronDown className="w-5 h-5" />
            </button>

            <AnimatePresence>
              {colorDropdownOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-xl shadow-lg z-50 max-h-48 overflow-y-auto"
                >
                  {colors.map((color) => (
                    <button
                      key={color}
                      onClick={() => handleColorSelect(color)}
                      className={`w-full px-4 py-3 text-left font-medium hover:bg-gray-50 first:rounded-t-xl last:rounded-b-xl transition-colors ${
                        selectedColor === color ? 'bg-blue-50 text-purple-700' : 'text-gray-700'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div
                            className="w-5 h-5 rounded-full border border-gray-300 mr-3"
                            style={{ backgroundColor: getColorDisplay(color) }}
                          />
                          <span>{color}</span>
                        </div>
                        {selectedColor === color && <Check className="w-5 h-5 text-purple-600" />}
                      </div>
                    </button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      )}
    </div>
  );
}
