'use client';

import { usePathname } from 'next/navigation';
import Header from './Header';
import Footer from './Footer';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

const ConditionalLayout: React.FC<ConditionalLayoutProps> = ({ children }) => {
  const pathname = usePathname();
  
  // Hide header and footer for admin pages
  const isAdminPage = pathname?.startsWith('/admin');
  
  if (isAdminPage) {
    // Admin pages: No header/footer, just content
    return <>{children}</>;
  }
  
  // Regular pages: Show header and footer
  return (
    <>
      <Header />
      <main className="min-h-screen">
        {children}
      </main>
      <Footer />
    </>
  );
};

export default ConditionalLayout;
