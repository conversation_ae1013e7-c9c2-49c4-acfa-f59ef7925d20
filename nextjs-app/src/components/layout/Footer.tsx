'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { MapPin, Phone, Mail, Clock, Facebook, Instagram, ArrowUp, Heart, Shield, Truck, Headphones } from 'lucide-react';
import { LogoFooter } from '../ui/Logo';
import TikTokIcon from '../ui/TikTokIcon';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: 'Trang Chủ', href: '/' },
    { name: '<PERSON><PERSON><PERSON>', href: '/san-pham' },
    { name: '<PERSON><PERSON>', href: '/danh-muc' },
    { name: '<PERSON><PERSON>', href: '/ban-chay' },
    { name: '<PERSON><PERSON><PERSON>', href: '/lien-he' },
  ];

  const customerService = [
    { name: '<PERSON><PERSON><PERSON> <PERSON>ách <PERSON>', href: '/chinh-sach-doi-tra' },
    { name: '<PERSON><PERSON><PERSON>', href: '/chinh-sach-bao-mat' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/dieu-khoan-su-dung' },
    { name: 'H<PERSON>ớng Dẫn Mua Hàng', href: '/huong-dan-mua-hang' },
    { name: 'Hướng Dẫn Thanh Toán', href: '/huong-dan-thanh-toan' },
  ];

  const contactInfo = [
    {
      icon: <MapPin className="w-5 h-5" />,
      text: '1484 Lê Đức Thọ phường 13 quận Gò Vấp HCM'
    },
    {
      icon: <Phone className="w-5 h-5" />,
      text: '0932935085',
      href: 'tel:0932935085'
    },
    {
      icon: <Mail className="w-5 h-5" />,
      text: '<EMAIL>',
      href: 'mailto:<EMAIL>'
    },
    {
      icon: <Clock className="w-5 h-5" />,
      text: 'Thứ 2 - Chủ nhật: 9:00 - 20:00'
    }
  ];

  const socialLinks = [
    {
      icon: <Facebook className="w-6 h-6" />,
      href: 'https://www.facebook.com/thinluong.kcorder',
      name: 'Facebook',
      color: 'hover:bg-blue-500'
    },
    {
      icon: <Instagram className="w-6 h-6" />,
      href: 'https://www.instagram.com/thinluong.official/',
      name: 'Instagram',
      color: 'hover:bg-gradient-to-br hover:from-purple-500 hover:to-pink-500'
    },
    {
      icon: <TikTokIcon className="w-6 h-6" />,
      href: 'https://www.tiktok.com/@thin_luong',
      name: 'TikTok',
      color: 'hover:bg-black'
    }
  ];

  const features = [
    {
      icon: <Truck className="w-6 h-6" />,
      title: 'Miễn Phí Vận Chuyển',
      desc: 'Đơn hàng từ 500k'
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: 'Bảo Hành Chất Lượng',
      desc: 'Đổi trả trong 30 ngày'
    },
    {
      icon: <Headphones className="w-6 h-6" />,
      title: 'Hỗ Trợ 24/7',
      desc: 'Tư vấn nhiệt tình'
    },
    {
      icon: <Heart className="w-6 h-6" />,
      title: 'Khách Hàng Yêu Thích',
      desc: 'Hơn 10k+ đánh giá 5*'
    }
  ];

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer className="bg-gray-50 text-gray-800 relative overflow-hidden">
      {/* Features Section */}
      <div className="bg-white border-t border-gray-200">
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center group"
              >
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-orange-200 transition-colors duration-300">
                  <div className="text-orange-600">
                    {feature.icon}
                  </div>
                </div>
                <h3 className="font-bold text-gray-900 mb-2 text-lg">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.desc}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Footer */}
      <div className="bg-gray-900 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
            {/* Company Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="lg:col-span-1 space-y-6"
            > 
              <div>
                <div className="text-2xl font-bold text-gray-100 mb-3">
                  🫧Thinluong Official
                </div>
                <p className="text-orange-400 font-medium mb-4">
                  cheapmoment cùng idol
                </p>
                <p className="text-gray-300 leading-relaxed mb-6">
                  Cửa hàng thời trang uy tín, chất lượng cao với phong cách trẻ trung, hiện đại. 
                  Mang đến những sản phẩm tốt nhất cho khách hàng.
                </p>
              </div>
              
              {/* Social Links */}
              <div>
                <div className="text-gray-100 font-semibold mb-4">Theo Dõi Chúng Tôi</div>
                <div className="flex space-x-4">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={index}
                      href={social.href}
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      className={`w-14 h-14 bg-gray-800 rounded-2xl flex items-center justify-center text-gray-300 hover:text-white ${social.color} transition-all duration-300 shadow-lg hover:shadow-xl`}
                      title={social.name}
                    >
                      {social.icon}
                    </motion.a>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="space-y-6"
            >
              <div className="text-xl font-bold text-white mb-6">
                Liên Kết Nhanh
              </div>
              <ul className="space-y-4">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-orange-400 transition-all duration-300 hover:translate-x-2 inline-block text-lg font-medium"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Customer Service */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-6"
            >
              <div className="text-xl font-bold text-white mb-6">
                Hỗ Trợ Khách Hàng
              </div>
              <ul className="space-y-4">
                {customerService.map((link, index) => (
                  <li key={index}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-orange-400 transition-all duration-300 hover:translate-x-2 inline-block text-lg font-medium"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Contact Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="space-y-6"
            >
              <div className="text-xl font-bold text-white mb-6">
                Thông Tin Liên Hệ
              </div>
              <ul className="space-y-6">
                {contactInfo.map((info, index) => (
                  <li key={index} className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-orange-600 rounded-xl flex items-center justify-center text-white flex-shrink-0">
                      {info.icon}
                    </div>
                    <div className="flex-1">
                      {info.href ? (
                        <a 
                          href={info.href}
                          className="text-gray-300 hover:text-orange-400 transition-colors duration-300 text-lg font-medium"
                        >
                          {info.text}
                        </a>
                      ) : (
                        <span className="text-gray-300 text-lg font-medium">{info.text}</span>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-gray-800 bg-gray-950">
          <div className="container mx-auto px-4 py-8">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <motion.p
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                className="text-gray-400 text-lg font-medium"
              >
                © {currentYear} 🫧Thinluong Official - cheapmoment cùng idol. All rights reserved.
              </motion.p>
              
              <motion.div
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                className="flex items-center space-x-8"
              >
                <Link href="/chinh-sach-bao-mat" className="text-gray-400 hover:text-orange-400 text-lg font-medium transition-colors duration-300">
                  Chính Sách Bảo Mật
                </Link>
                <Link href="/dieu-khoan-su-dung" className="text-gray-400 hover:text-orange-400 text-lg font-medium transition-colors duration-300">
                  Điều Khoản Sử Dụng
                </Link>
                <motion.button
                  onClick={scrollToTop}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-12 h-12 bg-orange-600 hover:bg-orange-700 rounded-full flex items-center justify-center text-white transition-colors duration-300 shadow-lg"
                  title="Lên đầu trang"
                >
                  <ArrowUp className="w-6 h-6" />
                </motion.button>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
} 