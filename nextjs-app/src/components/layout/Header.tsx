'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Search, Menu, X, Heart, ChevronDown, ShoppingBag, Package } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCart } from '../../contexts/CartContext';
import { useWishlist } from '../../contexts/WishlistContext';
import Logo from '../ui/Logo';
import CartDropdown from '../ui/CartDropdown';
import SearchDropdown from '../ui/SearchDropdown';
import ElegantPromoBanner from '../ui/ElegantPromoBanner';

interface Category {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  image: string | null;
  level: number;
  sortOrder: number;
  productCount: number;
  children?: Category[];
}

const Header = () => {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const { itemCount } = useCart();
  const { items: wishlistItems } = useWishlist();

  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories?includeChildren=true&limit=10');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCategories(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
      {/* Top Banner */}
      <ElegantPromoBanner />

      {/* Main Header */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          {/* Logo - Leftmost */}
          <div className="flex items-center">
            <Logo
              variant="default"
              size="2xl"
              href="/"
              className="flex items-center"
            />
          </div>

          {/* Search Bar - Prominent */}
          <div className="hidden md:flex flex-1 max-w-md mx-6">
            <SearchDropdown
              placeholder="Tìm kiếm sản phẩm..."
              className="w-full"
            />
          </div>

          {/* Navigation Items */}
          <nav className="hidden lg:flex items-center space-x-6">
            {/* Trang chủ */}
            <Link
              href="/"
              className="text-sm font-medium text-gray-900 hover:text-gray-600 transition-colors whitespace-nowrap"
            >
              Trang chủ
            </Link>

            {/* Categories Dropdown */}
            <div
              className="relative"
              onMouseEnter={() => setShowCategoryDropdown(true)}
              onMouseLeave={() => setShowCategoryDropdown(false)}
            >
              <button className="flex items-center text-sm font-medium text-gray-900 hover:text-gray-600 transition-colors whitespace-nowrap">
                Bộ sưu tập
                <ChevronDown className="ml-1 w-4 h-4" />
              </button>

              <AnimatePresence>
                {showCategoryDropdown && categories.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    className="absolute top-full left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
                  >
                    <div className="py-2">
                      {categories.map((category) => (
                        <div key={category.id}>
                          <Link
                            href={`/danh-muc/${category.slug}`}
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                          >
                            {category.name}
                            {category.productCount > 0 && (
                              <span className="ml-2 text-xs text-gray-500">({category.productCount})</span>
                            )}
                          </Link>
                          {category.children && category.children.length > 0 && (
                            <div className="ml-4">
                              {category.children.map((child) => (
                                <Link
                                  key={child.id}
                                  href={`/danh-muc/${child.slug}`}
                                  className="block px-4 py-1 text-xs text-gray-600 hover:bg-gray-50 transition-colors"
                                >
                                  — {child.name}
                                  {child.productCount > 0 && (
                                    <span className="ml-2 text-xs text-gray-400">({child.productCount})</span>
                                  )}
                                </Link>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Sản phẩm */}
            <Link
              href="/san-pham"
              className="text-sm font-medium text-gray-900 hover:text-gray-600 transition-colors whitespace-nowrap"
            >
              Sản phẩm
            </Link>

            {/* Bán chạy */}
            <Link
              href="/ban-chay"
              className="text-sm font-medium text-gray-900 hover:text-gray-600 transition-colors whitespace-nowrap"
            >
              Bán chạy
            </Link>

            {/* Tra cứu đơn hàng */}
            <Link
              href="/tra-cuu-don-hang"
              className="text-sm font-medium text-gray-900 hover:text-gray-600 transition-colors whitespace-nowrap"
            >
              Tra cứu đơn hàng
            </Link>
          </nav>

          {/* Right Section - Icons */}
          <div className="flex items-center space-x-3">
            {/* Mobile Search Button */}
            <button
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className="md:hidden p-2 hover:bg-gray-100 rounded-full transition-colors"
              aria-label="Tìm kiếm"
            >
              <Search className="w-5 h-5" />
            </button>

            {/* Wishlist */}
            <Link href="/yeu-thich" className="p-2 hover:bg-gray-100 rounded-full transition-colors relative">
              <Heart className="w-5 h-5" />
              {wishlistItems.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {wishlistItems.length}
                </span>
              )}
            </Link>

            {/* Shopping Cart - Rightmost */}
            <CartDropdown />

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2 hover:bg-gray-100 rounded-full transition-colors"
              aria-label="Menu"
            >
              {isMobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Mobile Search Bar */}
        <AnimatePresence>
          {isSearchOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-t border-gray-200 py-4"
            >
              <SearchDropdown
                placeholder="Tìm kiếm sản phẩm..."
                className="w-full"
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.nav
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden border-t border-gray-200 py-4"
            >
              <div className="flex flex-col space-y-4">
                {/* Main navigation items in order */}
                <Link
                  href="/"
                  className="text-sm font-medium text-gray-900 hover:text-gray-600 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Trang chủ
                </Link>

                <Link
                  href="/san-pham"
                  className="text-sm font-medium text-gray-900 hover:text-gray-600 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Sản phẩm
                </Link>

                <Link
                  href="/ban-chay"
                  className="text-sm font-medium text-gray-900 hover:text-gray-600 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Bán chạy
                </Link>

                <Link
                  href="/tra-cuu-don-hang"
                  className="text-sm font-medium text-gray-900 hover:text-gray-600 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Tra cứu đơn hàng
                </Link>

                {/* Mobile Categories */}
                {categories.length > 0 && (
                  <div className="border-t border-gray-200 pt-4">
                    <h3 className="text-sm font-medium text-gray-900 mb-2">Danh mục sản phẩm</h3>
                    {categories.map((category) => (
                      <div key={category.id} className="mb-2">
                        <Link
                          href={`/danh-muc/${category.slug}`}
                          className="block text-sm text-gray-700 hover:text-gray-900 transition-colors"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          {category.name}
                          {category.productCount > 0 && (
                            <span className="ml-2 text-xs text-gray-500">({category.productCount})</span>
                          )}
                        </Link>
                        {category.children && category.children.length > 0 && (
                          <div className="ml-4 mt-1">
                            {category.children.map((child) => (
                              <Link
                                key={child.id}
                                href={`/danh-muc/${child.slug}`}
                                className="block text-xs text-gray-600 hover:text-gray-800 transition-colors py-1"
                                onClick={() => setIsMobileMenuOpen(false)}
                              >
                                — {child.name}
                                {child.productCount > 0 && (
                                  <span className="ml-2 text-xs text-gray-400">({child.productCount})</span>
                                )}
                              </Link>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <div className="flex items-center justify-between">
                    <Link
                      href="/yeu-thich"
                      className="flex items-center space-x-2 text-gray-700 hover:text-red-500 transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <Heart className="w-5 h-5" />
                      <span>Yêu thích ({wishlistItems.length})</span>
                    </Link>
                    <Link
                      href="/gio-hang"
                      className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <ShoppingBag className="w-5 h-5" />
                      <span>Giỏ hàng ({itemCount})</span>
                    </Link>
                  </div>
                </div>
              </div>
            </motion.nav>
          )}
        </AnimatePresence>
      </div>
    </header>
  );
};

export default Header;
