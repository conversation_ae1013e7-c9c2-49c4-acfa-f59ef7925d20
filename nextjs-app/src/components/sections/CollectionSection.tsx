'use client';

import React from 'react';
import { motion } from 'framer-motion';
import ProductCard from '../ui/ProductCard';
import { ProductWithCategory } from '@/types';
import { ProductStatus } from '@prisma/client';

interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  slug: string;
  isOnSale?: boolean;
  description?: string;
  shortDescription?: string;
  sku?: string;
  categoryId?: string;
}

interface CollectionSectionProps {
  title: string;
  description?: string;
  products: Product[];
  backgroundColor?: string;
  showButton?: boolean;
  buttonText?: string;
  buttonHref?: string;
}

const CollectionSection: React.FC<CollectionSectionProps> = ({
  title,
  description,
  products,
  backgroundColor = 'bg-white',
  showButton = true,
  buttonText = 'Mua Sắm Bộ Sưu Tập',
  buttonHref = '#',
}) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section className={`py-16 ${backgroundColor}`}>
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          {/* Decorative Line */}
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-gray-300 flex-1 max-w-20"></div>
          </div>

          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            {title}
          </h2>

          {description && (
            <p className="text-gray-600 max-w-2xl mx-auto text-lg leading-relaxed">
              {description}
            </p>
          )}
        </motion.div>

        {/* Products Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
        >
          {products.map((product) => {
            // Convert simple product to ProductWithCategory format
            const productWithCategory: ProductWithCategory = {
              id: product.id,
              name: product.name,
              slug: product.slug,
              description: product.description || null,
              shortDescription: product.shortDescription || null,
              price: product.price,
              originalPrice: product.originalPrice || null,
              isOnSale: product.isOnSale || false,
              stock: 10,
              sku: product.sku || null,
              barcode: null,
              weight: null,
              dimensions: null,
              images: [product.image],
              categoryId: product.categoryId || '1',
              brand: null,
              tags: [],
              featured: false,
              status: ProductStatus.ACTIVE,
              seoTitle: null,
              seoDescription: null,
              seoKeywords: [],
              createdAt: new Date(),
              updatedAt: new Date(),
              category: {
                id: '1',
                name: 'Thời trang',
                slug: 'thoi-trang',
                description: null,
                image: null,
                parentId: null,
                level: 0,
                sortOrder: 0,
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date()
              },
              reviews: [],
              _count: { reviews: 0 },
            };

            return (
              <motion.div key={product.id} variants={itemVariants}>
                <ProductCard product={productWithCategory} />
              </motion.div>
            );
          })}
        </motion.div>

        {/* Call to Action Button */}
        {showButton && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-center"
          >
            <motion.a
              href={buttonHref}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-block bg-transparent border-2 border-black text-black px-8 py-3 text-sm font-semibold uppercase tracking-wider hover:bg-black hover:text-white transition-all duration-300"
            >
              {buttonText}
            </motion.a>
          </motion.div>
        )}
      </div>
    </section>
  );
};

export default CollectionSection;
