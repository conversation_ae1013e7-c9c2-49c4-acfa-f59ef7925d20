'use client';

import React, { useState, useEffect } from 'react';
// import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight, Star, Shield, Truck, Award } from 'lucide-react';
import Link from 'next/link';

const ElegantHeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  const slides = [
    {
      title: "Bộ Sưu Tập Thời Trang",
      subtitle: "<PERSON>ong cách tinh tế, chất lượng vượt trời",
      description: "Khám phá những xu hướng thời trang mới nhất với thiết kế tinh tế và chất lượng cao. Tạo nên phong cách riêng của bạn với bộ sưu tập đa dạng.",
      ctaText: "<PERSON>h<PERSON><PERSON> Phá Ngay",
      ctaLink: "/san-pham",
      accent: "primary"
    },
    {
      title: "Chất L<PERSON>ợng <PERSON>",
      subtitle: "Cam kết chất lượng và dịch vụ tốt nhất",
      description: "Mỗi sản phẩm đều được tuyển chọn kỹ lưỡng với tiêu chuẩn chất lượng cao. Đội ngũ chuyên nghiệp luôn sẵn sàng phục vụ bạn.",
      ctaText: "Tìm Hiểu Thêm",
      ctaLink: "/danh-muc",
      accent: "secondary"
    },
    {
      title: "Giao Hàng Toàn Quốc",
      subtitle: "Miễn phí vận chuyển cho đơn hàng từ 500K",
      description: "Giao hàng nhanh chóng và an toàn đến tận nơi. Đội ngũ vận chuyển chuyên nghiệp đảm bảo sản phẩm đến tay bạn trong tình trạng hoàn hảo.",
      ctaText: "Đặt Hàng Ngay",
      ctaLink: "/ban-chay",
      accent: "tertiary"
    }
  ];

  const features = [
    { icon: Star, text: "Chất lượng cao", description: "Sản phẩm được tuyển chọn" },
    { icon: Shield, text: "Bảo hành", description: "Đổi trả trong 30 ngày" },
    { icon: Truck, text: "Giao hàng nhanh", description: "Miễn phí từ 500K" },
    { icon: Award, text: "Uy tín", description: "Được khách hàng tin tưởng" }
  ];

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 6000);

    return () => clearInterval(timer);
  }, [slides.length]);

  const currentSlideData = slides[currentSlide];

  return (
    <section className="relative min-h-[600px] md:min-h-[700px] bg-white overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-50" />
      
      {/* Minimal geometric elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-20 w-32 h-32 bg-gray-100 rounded-full opacity-30" />
        <div className="absolute bottom-20 left-20 w-24 h-24 bg-gray-200 rounded-full opacity-20" />
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-gray-150 rounded-full opacity-25" />
      </div>

      {/* Content */}
      <div className="relative container mx-auto px-4 h-full flex items-center">
        <div className="w-full">
          <div className="max-w-4xl mx-auto text-center">
            <div key={currentSlide} className="animate-fade-in">
              {/* Subtitle badge */}
              <div className="inline-flex items-center space-x-2 bg-gray-100 rounded-full px-6 py-2 mb-6">
                <Star className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">{currentSlideData.subtitle}</span>
              </div>

              {/* Main title */}
              <h1 className="elegant-title text-4xl md:text-5xl lg:text-6xl mb-6">
                {currentSlideData.title}
              </h1>

              {/* Description */}
              <p className="elegant-body text-lg md:text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed">
                {currentSlideData.description}
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                <Link href={currentSlideData.ctaLink}>
                  <button className="elegant-button-primary group">
                    <span className="flex items-center">
                      {currentSlideData.ctaText}
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </span>
                  </button>
                </Link>

                <Link href="/danh-muc">
                  <button className="elegant-button-outline">
                    Xem Danh Mục
                  </button>
                </Link>
              </div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="elegant-card p-4 text-center hover:shadow-lg transition-all duration-300"
                >
                  <feature.icon className="w-6 h-6 text-gray-600 mx-auto mb-3" />
                  <h3 className="elegant-subtitle text-sm font-semibold text-gray-900 mb-1">
                    {feature.text}
                  </h3>
                  <p className="elegant-text-subtle text-xs">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Slide indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="flex space-x-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentSlide
                  ? 'bg-gray-800 w-8'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Navigation arrows */}
      <button
        onClick={() => setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/80 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110"
      >
        <ArrowRight className="w-5 h-5 text-gray-600 rotate-180" />
      </button>
      
      <button
        onClick={() => setCurrentSlide((prev) => (prev + 1) % slides.length)}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/80 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110"
      >
        <ArrowRight className="w-5 h-5 text-gray-600" />
      </button>
    </section>
  );
};

export default ElegantHeroSection;
