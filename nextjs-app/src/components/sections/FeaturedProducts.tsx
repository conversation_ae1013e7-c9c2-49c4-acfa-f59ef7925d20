/* eslint-disable react/jsx-no-comment-textnodes */
/* eslint-disable @next/next/no-img-element */
'use client'

import React, { useState } from 'react'
import Link from 'next/link'
// import { motion } from 'framer-motion'
import { Heart, ShoppingCart } from 'lucide-react'
import { useCart } from '../../contexts/CartContext'
import { useWishlist } from '../../contexts/WishlistContext'
import { useToast } from '../ui/Toast'

interface Product {
  id: string
  name: string
  slug: string
  price: number
  salePrice?: number
  images: string[]
  featured: boolean
  category?: {
    name: string
    slug: string
  } | null
  categories?: {
    id: string
    name: string
    slug: string
  }[]
}

interface FeaturedProductsProps {
  products: Product[]
}

const FeaturedProducts: React.FC<FeaturedProductsProps> = ({ products }) => {
  const [loadingCart, setLoadingCart] = useState<string | null>(null)
  const [loadingWishlist, setLoadingWishlist] = useState<string | null>(null)
  const { addItem: addToCart } = useCart()
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist()
  const { showToast } = useToast()

  const handleAddToCart = async (product: Product, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setLoadingCart(product.id)
    try {
      // Auto-select first size and color if available (CartContext will handle this too)
      await addToCart(product.id, 1, null, null)
      showToast(`${product.name} đã được thêm vào giỏ hàng`, 'success', 'cart')
    } catch (error) {
      console.error('Error adding to cart:', error)
      showToast('Có lỗi xảy ra khi thêm vào giỏ hàng', 'error')
    } finally {
      setLoadingCart(null)
    }
  }

  const handleWishlistToggle = async (product: Product, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setLoadingWishlist(product.id)
    try {
      const isInWishlistState = isInWishlist(product.id)
      if (isInWishlistState) {
        await removeFromWishlist(product.id)
        showToast(`${product.name} đã được xóa khỏi danh sách yêu thích`, 'info', 'heart')
      } else {
        await addToWishlist(product.id)
        showToast(`${product.name} đã được thêm vào danh sách yêu thích`, 'success', 'heart')
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error)
      showToast('Có lỗi xảy ra khi cập nhật danh sách yêu thích', 'error')
    } finally {
      setLoadingWishlist(null)
    }
  }

  if (products.length === 0) {
    return (
      <div className="container mx-auto px-4 text-center py-16">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Sản Phẩm Bán Chạy</h2>
        <p className="text-gray-600">Chưa có sản phẩm bán chạy nào.</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4">
      <div className="text-center mb-12">
        <div className="inline-block">
          <span className="inline-block px-4 py-2 bg-orange-100 text-orange-600 rounded-full text-sm font-medium mb-4">
            🔥 Sản Phẩm Bán Chạy
          </span>
        </div>

        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          Top Sản Phẩm Bán Chạy
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Những sản phẩm được khách hàng mua nhiều nhất, chất lượng được kiểm chứng
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product, index) => (
          <div
            key={product.id}
            className="group hover:-translate-y-2 transition-transform duration-300"
          >
            <Link
              href={`/san-pham/${product.slug}`}
              className="block bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 cursor-pointer transform hover:scale-[1.02]"
              aria-label={`Xem chi tiết sản phẩm ${product.name}`}
            >
              <div className="relative overflow-hidden aspect-[3/4]">
                <img
                  src={product.images[0] || '/images/placeholder.jpg'}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-400"
                />

                {/* Quick Action Buttons */}
                <div className="absolute top-4 right-4 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleWishlistToggle(product, e);
                    }}
                    disabled={loadingWishlist === product.id}
                    className={`w-10 h-10 rounded-full backdrop-blur-sm transition-all duration-200 flex items-center justify-center hover:scale-110 ${
                      isInWishlist(product.id)
                        ? 'bg-red-500/90 text-white shadow-lg'
                        : 'bg-white/90 text-gray-700 hover:bg-white shadow-lg'
                    }`}
                    title={isInWishlist(product.id) ? 'Xóa khỏi yêu thích' : 'Thêm vào yêu thích'}
                  >
                    {loadingWishlist === product.id ? (
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Heart className={`w-4 h-4 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
                    )}
                  </button>

                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleAddToCart(product, e);
                    }}
                    disabled={loadingCart === product.id}
                    className="w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full text-gray-700 hover:bg-white transition-all duration-200 shadow-lg flex items-center justify-center hover:scale-110"
                    title="Thêm vào giỏ hàng"
                  >
                    {loadingCart === product.id ? (
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <ShoppingCart className="w-4 h-4" />
                    )}
                  </button>
                </div>

                {/* Sale Badge */}
                {product.salePrice && (
                  <div className="absolute top-3 left-3 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg transform -rotate-12">
                    -{Math.round(((product.price - product.salePrice) / product.price) * 100)}%
                  </div>
                )}

                {/* Bestseller Badge */}
                {/* <div className="absolute top-11 right-3 bg-gradient-to-r from-green-400 to-green-600 text-white px-2 py-1 rounded-full text-xs font-bold">
                  🔥 BÁN CHẠY
                </div> */}
              </div>

              <div className="p-5">
                <div>
                  {/* Category */}
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
                    {product.category?.name || product.categories?.[0]?.name || 'Chưa phân loại'}
                  </p>

                  {/* Product Name */}
                  <h3 className="font-bold text-gray-900 mb-3 line-clamp-2 text-lg leading-tight group-hover:text-gray-700 transition-colors">
                    {product.name}
                  </h3>

                  {/* Price */}
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      {product.salePrice ? (
                        <>
                          <span className="text-sm text-gray-500 line-through mb-1">
                            {product.price.toLocaleString('vi-VN')}đ
                          </span>
                          <span className="text-xl font-bold text-red-600">
                            {product.salePrice.toLocaleString('vi-VN')}đ
                          </span>
                        </>
                      ) : (
                        <span className="text-xl font-bold text-gray-900">
                          {product.price.toLocaleString('vi-VN')}đ
                        </span>
                      )}
                    </div>

                    {/* Rating */}
                    <div className="flex items-center space-x-1">
                      <div className="flex text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          <svg key={i} className="w-4 h-4 fill-current" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                      <span className="text-xs text-gray-500">(4.8)</span>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>

      <div className="text-center mt-12">
        <Link
          href="/san-pham"
          className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-orange-500 to-red-600 text-white font-bold rounded-full hover:from-orange-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          Xem Tất Cả Sản Phẩm
          <svg
            className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </Link>
      </div>
    </div>
  )
}

export default FeaturedProducts 