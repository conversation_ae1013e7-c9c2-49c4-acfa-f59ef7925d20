'use client';

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowRight, Star, Zap, Shield, Truck } from 'lucide-react'
import Link from 'next/link'

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isMounted, setIsMounted] = useState(false)

  const slides = [
    {
      title: "Bộ Sưu Tập Mùa Hè 2025",
      subtitle: "Thời trang trẻ trung, năng động",
      description: "Khám phá những xu hướng mới nhất với chất lượng vượt trội",
      bgColor: "from-blue-400 via-purple-500 to-purple-600",
      ctaText: "Khám Phá Ngay",
      ctaLink: "/ban-chay"
    },
    {
      title: "Giảm Giá Đặc Biệt 40%",
      subtitle: "<PERSON><PERSON> hội vàng không thể bỏ lỡ",
      description: "<PERSON><PERSON> dụng cho tất cả sản phẩm - Thời gian có hạn",
      bgColor: "from-orange-400 via-red-500 to-pink-600",
      ctaText: "Mua Ngay",
      ctaLink: "/san-pham"
    },
    {
      title: "Miễn Phí Vận Chuyển",
      subtitle: "Giao hàng tận nơi toàn quốc",
      description: "Đơn hàng từ 500K - Giao hàng nhanh chỉ trong 24h",
      bgColor: "from-green-400 via-teal-500 to-blue-600",
      ctaText: "Tìm Hiểu Thêm",
      ctaLink: "/danh-muc"
    }
  ]

  const features = [
    { icon: Star, text: "Chất lượng đảm bảo" },
    { icon: Zap, text: "Giao hàng nhanh" },
    { icon: Shield, text: "Bảo hành 12 tháng" },
    { icon: Truck, text: "Miễn phí ship" }
  ]

  // Set mounted state
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Auto-slide
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000)

    return () => clearInterval(timer)
  }, [slides.length])

  const currentSlideData = slides[currentSlide]

  return (
    <section className="relative min-h-[600px] md:min-h-[700px] overflow-hidden">
      {/* Animated Background */}
      <motion.div
        key={currentSlide}
        initial={{ scale: 1.1, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 1.5 }}
        className={`absolute inset-0 bg-gradient-to-br ${currentSlideData.bgColor}`}
      />

      {/* Animated Shapes - Only render on client */}
      {isMounted && (
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(8)].map((_, i) => {
            // Generate consistent positions and sizes using index-based seed
            const size = 50 + (i * 23) % 150
            const leftPosition = (i * 13 + 17) % 100
            const topPosition = (i * 19 + 31) % 100
            const moveX = (i % 2 === 0 ? 1 : -1) * ((i * 7) % 50 + 25)
            const moveY = (i % 3 === 0 ? 1 : -1) * ((i * 11) % 50 + 25)
            
            return (
              <motion.div
                key={`shape-${currentSlide}-${i}`}
                className="absolute rounded-full bg-white/10"
                style={{
                  width: size,
                  height: size,
                  left: `${leftPosition}%`,
                  top: `${topPosition}%`,
                }}
                animate={{
                  x: [0, moveX],
                  y: [0, moveY],
                  scale: [1, 1.2, 1],
                  opacity: [0.1, 0.3, 0.1]
                }}
                transition={{
                  duration: 10 + (i % 5) * 2,
                  repeat: Infinity,
                  repeatType: "reverse",
                  delay: i * 0.5
                }}
              />
            )
          })}
        </div>
      )}

      {/* Content */}
      <div className="relative container mx-auto px-4 h-full flex items-center">
        <div className="w-full">
          <div className="max-w-4xl mx-auto text-center text-white">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                initial={{ y: 100, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: -100, opacity: 0 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              >
                {/* Subtitle */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.3, type: "spring" }}
                  className="inline-flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 mb-6"
                >
                  <Star className="w-4 h-4" />
                  <span className="text-sm font-medium">{currentSlideData.subtitle}</span>
                </motion.div>

                {/* Main Title */}
                <motion.h1
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight"
                >
                  {currentSlideData.title}
                </motion.h1>

                {/* Description */}
                <motion.p
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.7 }}
                  className="text-lg md:text-xl mb-8 max-w-2xl mx-auto opacity-90"
                >
                  {currentSlideData.description}
                </motion.p>

                {/* CTA Button */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.9, type: "spring" }}
                >
                  <Link
                    href={currentSlideData.ctaLink}
                    className="inline-flex items-center space-x-2 bg-white text-gray-900 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-2xl"
                  >
                    <span>{currentSlideData.ctaText}</span>
                    <ArrowRight className="w-5 h-5" />
                  </Link>
                </motion.div>
              </motion.div>
            </AnimatePresence>

            {/* Features */}
            <motion.div
              initial={{ y: 100, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 1.2 }}
              className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto"
            >
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 1.4 + index * 0.1, type: "spring" }}
                  className="flex flex-col items-center space-y-2 bg-white/10 backdrop-blur-sm rounded-2xl p-4"
                >
                  <feature.icon className="w-6 h-6" />
                  <span className="text-sm font-medium text-center">{feature.text}</span>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3">
        {slides.map((_, index) => (
          <motion.button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide ? 'bg-white scale-125' : 'bg-white/50'
            }`}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
          />
        ))}
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2 }}
        className="absolute bottom-16 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-white/70 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  )
}

export default HeroSection
