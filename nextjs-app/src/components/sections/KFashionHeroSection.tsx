'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight, Star, Heart, Sparkles, Crown, Music, ShoppingBag } from 'lucide-react';
import Link from 'next/link';

const KFashionHeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  const slides = [
    {
      title: "K-Fashion Collection",
      subtitle: "✨ Phong Cách Idol Hàn Quốc",
      description: "Khám phá xu hướng thời trang được yêu thích bởi các idol K-pop. Từ street style đến stage outfit, tạo nên phong cách riêng của bạn.",
      bgGradient: "from-pink-400 via-purple-500 to-indigo-600",
      ctaText: "Shop K-Fashion",
      ctaLink: "/danh-muc",
      accent: "pink",
      keywords: ["Trendy", "Stylish", "K-Pop Inspired"]
    },
    {
      title: "Seoul Street Style",
      subtitle: "🌟 Thời Trang Đường Phố Seoul",
      description: "<PERSON>g phong cách đường phố Seoul về Việt Nam. Những outfit năng động, trẻ trung như các bạn trẻ Hàn Quốc.",
      bgGradient: "from-blue-400 via-cyan-500 to-teal-600",
      ctaText: "Khám Phá Ngay",
      ctaLink: "/ban-chay",
      accent: "blue",
      keywords: ["Urban", "Cool", "Street Fashion"]
    },
    {
      title: "Idol Inspired Looks",
      subtitle: "👑 Phong Cách Thần Tượng",
      description: "Tái hiện những set đồ iconic của các idol. Từ casual đến formal, tự tin tỏa sáng như một ngôi sao.",
      bgGradient: "from-purple-400 via-pink-500 to-red-500",
      ctaText: "Tỏa Sáng Ngay",
      ctaLink: "/san-pham",
      accent: "purple",
      keywords: ["Iconic", "Glamorous", "Star Quality"]
    }
  ];

  const features = [
    { icon: Crown, text: "Phong Cách Idol", color: "text-yellow-300" },
    { icon: Heart, text: "Trendy & Cute", color: "text-pink-300" },
    { icon: Sparkles, text: "Chất Lượng Cao", color: "text-blue-300" },
    { icon: Music, text: "K-Pop Vibes", color: "text-purple-300" }
  ];

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 6000);

    return () => clearInterval(timer);
  }, [slides.length]);

  const currentSlideData = slides[currentSlide];

  return (
    <section className="relative min-h-[700px] md:min-h-[800px] overflow-hidden">
      {/* Dynamic Background with Korean-inspired patterns */}
      <motion.div
        key={currentSlide}
        initial={{ scale: 1.1, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 2 }}
        className={`absolute inset-0 bg-gradient-to-br ${currentSlideData.bgGradient}`}
      />

      {/* Korean-inspired floating elements */}
      {isMounted && (
        <div className="absolute inset-0 overflow-hidden">
          {/* Hearts floating animation */}
          {[...Array(12)].map((_, i) => {
            const size = 20 + (i * 7) % 40;
            const leftPosition = (i * 11 + 5) % 95;
            const topPosition = (i * 17 + 10) % 90;
            const duration = 15 + (i % 4) * 5;
            
            return (
              <motion.div
                key={`heart-${currentSlide}-${i}`}
                className="absolute"
                style={{
                  left: `${leftPosition}%`,
                  top: `${topPosition}%`,
                }}
                animate={{
                  y: [0, -100, 0],
                  x: [0, (i % 2 === 0 ? 50 : -50), 0],
                  rotate: [0, 360],
                  scale: [0.5, 1, 0.5],
                  opacity: [0, 0.6, 0]
                }}
                transition={{
                  duration: duration,
                  repeat: Infinity,
                  delay: i * 0.8,
                  ease: "easeInOut"
                }}
              >
                {i % 4 === 0 ? (
                  <Heart className={`w-${Math.floor(size/4)} h-${Math.floor(size/4)} text-white/30`} fill="currentColor" />
                ) : i % 4 === 1 ? (
                  <Star className={`w-${Math.floor(size/4)} h-${Math.floor(size/4)} text-white/30`} fill="currentColor" />
                ) : i % 4 === 2 ? (
                  <Sparkles className={`w-${Math.floor(size/4)} h-${Math.floor(size/4)} text-white/30`} />
                ) : (
                  <Crown className={`w-${Math.floor(size/4)} h-${Math.floor(size/4)} text-white/30`} />
                )}
              </motion.div>
            );
          })}

          {/* Geometric shapes for modern K-fashion feel */}
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={`shape-${currentSlide}-${i}`}
              className="absolute rounded-full bg-white/5 backdrop-blur-sm"
              style={{
                width: 100 + i * 50,
                height: 100 + i * 50,
                left: `${(i * 20 + 10) % 80}%`,
                top: `${(i * 15 + 20) % 60}%`,
              }}
              animate={{
                scale: [1, 1.3, 1],
                opacity: [0.1, 0.3, 0.1],
                rotate: [0, 180, 360]
              }}
              transition={{
                duration: 20 + i * 3,
                repeat: Infinity,
                delay: i * 2
              }}
            />
          ))}
        </div>
      )}

      {/* Main Content */}
      <div className="relative container mx-auto px-4 h-full flex items-center">
        <div className="w-full">
          <div className="max-w-5xl mx-auto text-center text-white">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                initial={{ y: 100, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: -100, opacity: 0 }}
                transition={{ duration: 1, ease: "easeOut" }}
              >
                {/* Subtitle Badge */}
                <motion.div
                  initial={{ scale: 0, rotate: -10 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.3, type: "spring", bounce: 0.5 }}
                  className="inline-flex items-center space-x-2 bg-white/20 backdrop-blur-md rounded-full px-8 py-3 mb-8 border border-white/30"
                >
                  <Sparkles className="w-5 h-5" />
                  <span className="text-lg font-bold tracking-wide">{currentSlideData.subtitle}</span>
                </motion.div>

                {/* Main Title with Korean-style typography */}
                <motion.h1
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="text-5xl md:text-7xl lg:text-8xl font-black mb-6 leading-tight tracking-tight"
                  style={{
                    textShadow: '0 4px 20px rgba(0,0,0,0.3)',
                    fontFamily: 'system-ui, -apple-system, sans-serif'
                  }}
                >
                  {currentSlideData.title}
                </motion.h1>

                {/* Keywords */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.7 }}
                  className="flex justify-center space-x-4 mb-6"
                >
                  {currentSlideData.keywords.map((keyword, index) => (
                    <motion.span
                      key={keyword}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.8 + index * 0.1, type: "spring" }}
                      className="bg-white/15 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-semibold border border-white/20"
                    >
                      #{keyword}
                    </motion.span>
                  ))}
                </motion.div>

                {/* Description */}
                <motion.p
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.9 }}
                  className="text-xl md:text-2xl mb-10 max-w-3xl mx-auto opacity-95 leading-relaxed"
                >
                  {currentSlideData.description}
                </motion.p>

                {/* CTA Buttons */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 1.1, type: "spring" }}
                  className="flex flex-col sm:flex-row gap-4 justify-center items-center"
                >
                  <Link
                    href={currentSlideData.ctaLink}
                    className="group inline-flex items-center space-x-3 bg-white text-gray-900 px-10 py-5 rounded-full font-bold text-xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-2xl"
                  >
                    <ShoppingBag className="w-6 h-6 group-hover:scale-110 transition-transform" />
                    <span>{currentSlideData.ctaText}</span>
                    <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform" />
                  </Link>
                  
                  <Link
                    href="/danh-muc"
                    className="inline-flex items-center space-x-2 bg-transparent border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-gray-900 transition-all duration-300"
                  >
                    <span>Xem Bộ Sưu Tập</span>
                  </Link>
                </motion.div>
              </motion.div>
            </AnimatePresence>

            {/* Features with K-pop style */}
            <motion.div
              initial={{ y: 100, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 1.4 }}
              className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
            >
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ scale: 0, rotate: -10 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 1.6 + index * 0.1, type: "spring", bounce: 0.4 }}
                  className="flex flex-col items-center space-y-3 bg-white/10 backdrop-blur-md rounded-3xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 group"
                >
                  <feature.icon className={`w-8 h-8 ${feature.color} group-hover:scale-110 transition-transform`} />
                  <span className="text-sm font-bold text-center tracking-wide">{feature.text}</span>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>

      {/* Slide Indicators with K-pop style */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-4">
        {slides.map((_, index) => (
          <motion.button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`relative w-4 h-4 rounded-full transition-all duration-300 ${
              index === currentSlide ? 'bg-white scale-125' : 'bg-white/50'
            }`}
            whileHover={{ scale: 1.3 }}
            whileTap={{ scale: 0.9 }}
          >
            {index === currentSlide && (
              <motion.div
                className="absolute inset-0 rounded-full bg-white"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", bounce: 0.5 }}
              />
            )}
          </motion.button>
        ))}
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2.5 }}
        className="absolute bottom-20 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 15, 0] }}
          transition={{ duration: 2.5, repeat: Infinity }}
          className="w-8 h-12 border-2 border-white/60 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 16, 0] }}
            transition={{ duration: 2.5, repeat: Infinity }}
            className="w-1.5 h-4 bg-white/80 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  );
};

export default KFashionHeroSection;
