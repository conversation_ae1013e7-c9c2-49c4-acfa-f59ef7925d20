'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Mail, Gift, Bell, Sparkles, Star, Zap } from 'lucide-react'

const NewsletterSection: React.FC = () => {
  const [email, setEmail] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    setIsLoading(true)
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitted(true)
      setIsLoading(false)
      setEmail('')
    }, 1500)
  }

  return (
    <div className="container mx-auto px-4">
      <div className="relative max-w-6xl mx-auto">
        {/* Main Content Container */}
        <div className="relative bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 rounded-3xl p-8 md:p-16 overflow-hidden shadow-2xl">
          
          {/* Decorative Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-0 left-0 w-72 h-72 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2"></div>
            <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-pink-400/20 to-red-400/20 rounded-full blur-3xl transform translate-x-1/2 translate-y-1/2"></div>
            <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-br from-yellow-400/10 to-orange-400/10 rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2"></div>
          </div>

          {/* Grid Pattern Background */}
          <div className="absolute inset-0 opacity-10">
            <div 
              className="w-full h-full"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='white' fill-opacity='0.2'%3E%3Cpath d='m0 0h40v40h-40z' stroke='white' stroke-width='1' fill='none'/%3E%3C/g%3E%3C/svg%3E")`,
              }}
            ></div>
          </div>

          <div className="relative z-10 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="mb-12"
            >
              {/* Animated Icons */}
              <div className="flex justify-center items-center space-x-6 mb-8">
                <motion.div
                  animate={{ 
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1] 
                  }}
                  transition={{ 
                    duration: 3,
                    repeat: Infinity,
                    delay: 0 
                  }}
                  className="p-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl shadow-lg"
                >
                  <Mail className="w-8 h-8 text-white" />
                </motion.div>
                <motion.div
                  animate={{ 
                    rotate: [0, -10, 10, 0],
                    scale: [1, 1.1, 1] 
                  }}
                  transition={{ 
                    duration: 3,
                    repeat: Infinity,
                    delay: 1 
                  }}
                  className="p-4 bg-gradient-to-br from-pink-400 to-red-500 rounded-2xl shadow-lg"
                >
                  <Gift className="w-8 h-8 text-white" />
                </motion.div>
                <motion.div
                  animate={{ 
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1] 
                  }}
                  transition={{ 
                    duration: 3,
                    repeat: Infinity,
                    delay: 2 
                  }}
                  className="p-4 bg-gradient-to-br from-blue-400 to-purple-500 rounded-2xl shadow-lg"
                >
                  <Bell className="w-8 h-8 text-white" />
                </motion.div>
              </div>

              <motion.div
                initial={{ scale: 0.5 }}
                whileInView={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                className="inline-block"
              >
                <span className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 rounded-full text-sm font-bold mb-8 shadow-lg">
                  <Sparkles className="w-5 h-5 mr-2" />
                  Ưu Đãi Đặc Biệt Chỉ Dành Cho Bạn
                </span>
              </motion.div>

              <div className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
                Đăng Ký Nhận Tin
                <br />
                <span className="bg-gradient-to-r from-yellow-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
                  Ưu Đãi Hấp Dẫn
                </span>
              </div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed">
                Nhận thông tin về các sản phẩm mới, khuyến mãi đặc biệt và xu hướng thời trang mới nhất. 
                Đăng ký ngay để không bỏ lỡ cơ hội tiết kiệm!
              </p>
            </motion.div>

            {/* Benefits Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              {[
                {
                  icon: Gift,
                  title: "Giảm giá 20%",
                  description: "Cho đơn hàng đầu tiên",
                  color: "from-pink-500 to-red-500"
                },
                {
                  icon: Zap,
                  title: "Thông báo nhanh",
                  description: "Về flash sale & khuyến mãi",
                  color: "from-yellow-500 to-orange-500"
                },
                {
                  icon: Star,
                  title: "Ưu đãi VIP",
                  description: "Chỉ dành cho thành viên",
                  color: "from-purple-500 to-blue-500"
                }
              ].map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 + 0.3 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                  className="relative group"
                >
                  <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:bg-white/15 transition-all duration-300">
                    <motion.div
                      whileHover={{ scale: 1.2, rotate: 10 }}
                      className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${benefit.color} rounded-2xl mb-4 shadow-lg`}
                    >
                      <benefit.icon className="w-8 h-8 text-white" />
                    </motion.div>
                    <h3 className="text-white font-bold text-lg mb-2">{benefit.title}</h3>
                    <p className="text-gray-300 text-sm">{benefit.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Newsletter Form */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="max-w-lg mx-auto"
            >
              {!isSubmitted ? (
                <form onSubmit={handleSubmit} className="relative">
                  <div className="relative">
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Nhập email của bạn để nhận ưu đãi..."
                      className="w-full px-6 py-5 pr-40 bg-white/20 backdrop-blur-sm border border-white/30 rounded-2xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent text-lg"
                      required
                    />
                    <motion.button
                      type="submit"
                      disabled={isLoading}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="absolute right-2 top-2 bottom-2 px-8 bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold rounded-xl hover:from-yellow-500 hover:to-orange-600 transition-all duration-300 disabled:opacity-50 flex items-center justify-center shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-6 h-6 border-2 border-gray-900 border-t-transparent rounded-full"
                        />
                      ) : (
                        'Đăng Ký'
                      )}
                    </motion.button>
                  </div>
                </form>
              ) : (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", duration: 0.5 }}
                  className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm border border-green-400/30 rounded-2xl p-8 text-center"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring" }}
                    className="w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg"
                  >
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                    </svg>
                  </motion.div>
                  <h3 className="text-white font-bold text-2xl mb-3">Đăng ký thành công!</h3>
                  <p className="text-gray-300 text-lg">
                    Chúc mừng! Bạn sẽ nhận được mã giảm giá 20% qua email trong vài phút tới.
                  </p>
                </motion.div>
              )}

              <p className="text-gray-400 text-sm mt-6 leading-relaxed">
                Bằng cách đăng ký, bạn đồng ý với{' '}
                <a href="#" className="text-yellow-400 hover:text-yellow-300 underline transition-colors">
                  Điều khoản dịch vụ
                </a>{' '}
                và{' '}
                <a href="#" className="text-yellow-400 hover:text-yellow-300 underline transition-colors">
                  Chính sách bảo mật
                </a>{' '}
                của chúng tôi.
              </p>
            </motion.div>
          </div>

          {/* Floating Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(15)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-gradient-to-br from-yellow-400 to-pink-400 rounded-full opacity-60"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, -40, 0],
                  opacity: [0.3, 0.8, 0.3],
                  scale: [1, 1.5, 1],
                }}
                transition={{
                  duration: 4 + Math.random() * 3,
                  repeat: Infinity,
                  delay: Math.random() * 4,
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default NewsletterSection 