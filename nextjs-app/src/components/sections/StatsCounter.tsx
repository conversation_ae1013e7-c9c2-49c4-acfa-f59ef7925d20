'use client'

import React, { useState, useEffect } from 'react'
import { motion, useInView } from 'framer-motion'
import { useRef } from 'react'
import { Users, ShoppingBag, Award, Truck } from 'lucide-react'

interface StatItem {
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>
  number: number
  label: string
  suffix?: string
  color: string
  bgColor: string
}

const stats: StatItem[] = [
  {
    icon: Users,
    number: 50000,
    label: "Khách <PERSON>ng Hà<PERSON> Lòng",
    suffix: "+",
    color: "text-blue-600",
    bgColor: "bg-gradient-to-br from-blue-500 to-blue-600"
  },
  {
    icon: ShoppingBag,
    number: 120000,
    label: "Sản Phẩm Đã Bán",
    suffix: "+",
    color: "text-green-600",
    bgColor: "bg-gradient-to-br from-green-500 to-green-600"
  },
  {
    icon: Award,
    number: 5,
    label: "<PERSON>ă<PERSON> Kinh Nghiệm",
    color: "text-purple-600",
    bgColor: "bg-gradient-to-br from-purple-500 to-purple-600"
  },
  {
    icon: Truck,
    number: 99,
    label: "Giao Hàng T<PERSON>ành Công",
    suffix: "%",
    color: "text-orange-600",
    bgColor: "bg-gradient-to-br from-orange-500 to-orange-600"
  }
]

const CountingNumber: React.FC<{ target: number; isInView: boolean; suffix?: string; color: string }> = ({ 
  target, 
  isInView, 
  suffix = "",
  color
}) => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    if (!isInView) return

    let startTimestamp: number | null = null
    const duration = 2000 // 2 seconds

    const step = (timestamp: number) => {
      if (!startTimestamp) startTimestamp = timestamp
      const progress = Math.min((timestamp - startTimestamp) / duration, 1)
      
      setCount(Math.floor(progress * target))
      
      if (progress < 1) {
        window.requestAnimationFrame(step)
      }
    }

    window.requestAnimationFrame(step)
  }, [target, isInView])

  return (
    <span className={`text-4xl md:text-5xl font-bold ${color}`}>
      {count.toLocaleString()}{suffix}
    </span>
  )
}

const StatsCounter: React.FC = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  return (
    <section 
      ref={ref}
      className="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden"
    >
      {/* Decorative Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-br from-green-200/30 to-blue-200/30 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-3xl"></div>
      </div>

      {/* Pattern Background */}
      <div className="absolute inset-0 opacity-5">
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        ></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ scale: 0.5 }}
            animate={isInView ? { scale: 1 } : { scale: 0.5 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="inline-block"
          >
            <span className="inline-block px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full text-sm font-semibold mb-6 shadow-lg">
              📊 Thành Tựu Ấn Tượng
            </span>
          </motion.div>
          
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 bg-clip-text text-transparent mb-6">
            Thành Tựu Của Chúng Tôi
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Những con số ấn tượng thể hiện sự tin tưởng và hài lòng của khách hàng qua nhiều năm phát triển
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.6, delay: index * 0.15 }}
              className="text-center group"
            >
              <motion.div
                whileHover={{ scale: 1.05, rotate: 5 }}
                className="relative mb-6"
              >
                <div className={`inline-flex items-center justify-center w-20 h-20 ${stat.bgColor} rounded-2xl shadow-xl group-hover:shadow-2xl transition-all duration-300`}>
                  <stat.icon className="w-10 h-10 text-white" />
                </div>
                <div className="absolute -inset-2 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
              </motion.div>
              
              <div className="mb-3">
                <CountingNumber 
                  target={stat.number} 
                  isInView={isInView} 
                  suffix={stat.suffix} 
                  color={stat.color}
                />
              </div>
              
              <p className="text-gray-700 text-sm md:text-base font-semibold leading-relaxed">
                {stat.label}
              </p>
            </motion.div>
          ))}
        </div>

      
      </div>
    </section>
  )
}

export default StatsCounter 