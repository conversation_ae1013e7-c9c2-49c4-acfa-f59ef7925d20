'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, Quote, User, ShoppingBag, Users, TrendingUp, Award } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

interface Review {
  id: string;
  rating: number;
  comment: string;
  author: string;
  reviewTime: string;
  createdAt: string;
  product: {
    id: string;
    name: string;
    slug: string;
    image: string | null;
    category: string;
  };
}

interface ReviewStats {
  totalReviews: number;
  averageRating: number;
}

interface TestimonialsData {
  reviews: Review[];
  stats: ReviewStats;
}

// Star rating component
const StarRating = ({ rating, size = 'sm' }: { rating: number; size?: 'sm' | 'md' | 'lg' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`${sizeClasses[size]} ${
            star <= rating
              ? 'fill-yellow-400 text-yellow-400'
              : 'fill-gray-200 text-gray-200'
          }`}
        />
      ))}
    </div>
  );
};

// Individual review card component
const ReviewCard = ({ review }: { review: Review }) => {
  // Generate avatar initials
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300"
    >
      {/* Quote icon */}
      <div className="flex justify-between items-start mb-4">
        <Quote className="w-8 h-8 text-orange-500 opacity-60" />
        <StarRating rating={review.rating} size="sm" />
      </div>

      {/* Review content */}
      <blockquote className="text-gray-700 mb-4 leading-relaxed">
        "{review.comment}"
      </blockquote>

      {/* Product info */}
      <div className="flex items-center gap-3 mb-4 p-3 bg-gray-50 rounded-lg">
        {review.product.image ? (
          <div className="relative w-12 h-12 rounded-lg overflow-hidden">
            <Image
              src={review.product.image}
              alt={review.product.name}
              fill
              className="object-cover"
            />
          </div>
        ) : (
          <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
            <ShoppingBag className="w-6 h-6 text-gray-400" />
          </div>
        )}
        <div className="flex-1 min-w-0">
          <Link
            href={`/san-pham/${review.product.slug}`}
            className="text-sm font-medium text-gray-900 hover:text-orange-600 transition-colors line-clamp-1"
          >
            {review.product.name}
          </Link>
          <p className="text-xs text-gray-500">{review.product.category}</p>
        </div>
      </div>

      {/* Author info */}
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-gradient-to-br from-orange-400 to-pink-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
          {getInitials(review.author)}
        </div>
        <div>
          <p className="font-medium text-gray-900">{review.author}</p>
          <p className="text-sm text-gray-500">{review.reviewTime}</p>
        </div>
      </div>
    </motion.div>
  );
};

// Main testimonials section component
export default function TestimonialsSection() {
  const [data, setData] = useState<TestimonialsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReviews = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/reviews/featured?limit=6&minRating=4');
        const result = await response.json();

        if (result.success) {
          setData(result.data);
        } else {
          setError('Không thể tải đánh giá');
        }
      } catch (err) {
        setError('Lỗi kết nối');
        console.error('Error fetching reviews:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchReviews();
  }, []);

  if (loading) {
    return (
      <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Đánh Giá Khách Hàng
            </h2>
            <div className="w-24 h-1 bg-orange-500 mx-auto mb-4"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="h-20 bg-gray-200 rounded mb-4"></div>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error || !data) {
    return (
      <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Đánh Giá Khách Hàng
          </h2>
          <p className="text-gray-600">Không thể tải đánh giá. Vui lòng thử lại sau.</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.div
            initial={{ scale: 0.5 }}
            whileInView={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="inline-block"
          >
            <span className="inline-block px-4 py-2 bg-orange-100 text-orange-600 rounded-full text-sm font-medium mb-4">
              💬 Khách Hàng Nói Gì
            </span>
          </motion.div>

          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Đánh Giá Khách Hàng
          </h2>
          <div className="w-24 h-1 bg-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600 max-w-2xl mx-auto mb-6">
            Khám phá những trải nghiệm thực tế từ khách hàng đã mua sắm tại cửa hàng của chúng tôi
          </p>

          {/* Stats */}
          <div className="flex items-center justify-center gap-8 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-2xl font-bold text-orange-600">{data.stats.totalReviews}</div>
              <div className="text-sm text-gray-600">Đánh giá</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="flex items-center justify-center gap-2">
                <span className="text-2xl font-bold text-orange-600">{data.stats.averageRating}</span>
                <StarRating rating={Math.round(data.stats.averageRating)} size="md" />
              </div>
              <div className="text-sm text-gray-600">Điểm trung bình</div>
            </motion.div>
          </div>
        </div>

        {/* Reviews Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {data.reviews.map((review) => (
            <ReviewCard key={review.id} review={review} />
          ))}
        </div>

        {/* Call to action */}
        <div className="text-center mt-12">
          <Link
            href="/san-pham"
            className="inline-flex items-center gap-2 bg-orange-600 text-white px-8 py-3 rounded-full font-semibold hover:bg-orange-700 transition-colors"
          >
            <ShoppingBag className="w-5 h-5" />
            Khám Phá Sản Phẩm
          </Link>
        </div>
      </div>
    </section>
  );
}