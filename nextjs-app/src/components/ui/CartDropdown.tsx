'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ShoppingBag, Plus, Minus, Trash2 } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useCart } from '../../contexts/CartContext';

const CartDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { items, removeItem, updateQuantity, total, itemCount, loading } = useCart();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleQuantityUpdate = async (itemId: string, newQuantity: number) => {
    try {
      await updateQuantity(itemId, newQuantity);
    } catch (error) {
      console.error('Error updating quantity:', error);
    }
  };

  const handleRemoveItem = async (itemId: string) => {
    try {
      await removeItem(itemId);
    } catch (error) {
      console.error('Error removing item:', error);
    }
  };

  const getProductImage = (images: string | string[]) => {
    try {
      const imageArray = Array.isArray(images)
        ? images
        : (images ? JSON.parse(images) : []);
      return imageArray.length > 0 ? imageArray[0] : '/images/placeholder.svg';
    } catch {
      return '/images/placeholder.svg';
    }
  };

  function getSizeDisplayName(variant: {id: string; name: string; attributes: any; price?: number; stock: number;}|null|undefined, variantId: string): React.ReactNode {
    if (!variant && !variantId) return null;

    if (variant) {
      const attributes = variant.attributes || {};
      return attributes.size || variant.name;
    }

    // If no variant object but we have variantId, return the variantId as fallback
    return variantId;
  }

  function getColorDisplay(color: string) {
    // Map color names to display colors for visual representation
    const colorMap: { [key: string]: string } = {
      'đen': '#000000',
      'trắng': '#FFFFFF',
      'đỏ': '#DC2626',
      'xanh': '#2563EB',
      'vàng': '#EAB308',
      'hồng': '#EC4899',
      'tím': '#7C3AED',
      'xám': '#6B7280',
      'nâu': '#92400E',
      'cam': '#EA580C',
      'xanh lá': '#16A34A',
      'be': '#D4B896'
    };

    const normalizedColor = color.toLowerCase();
    return colorMap[normalizedColor] || '#6B7280';
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Cart Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors"
      >
        <ShoppingBag className="w-6 h-6" />
        {itemCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
            {itemCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 top-full mt-2 w-80 sm:w-96 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
          >
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Giỏ hàng ({itemCount} sản phẩm)
              </h3>
            </div>

            <div className="max-h-96 overflow-y-auto">
              {items.length === 0 ? (
                <div className="p-6 text-center">
                  <ShoppingBag className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                  <p className="text-gray-500">Giỏ hàng trống</p>
                  <Link
                    href="/san-pham"
                    onClick={() => setIsOpen(false)}
                    className="mt-3 inline-block text-orange-500 hover:text-orange-600 font-medium"
                  >
                    Mua sắm ngay
                  </Link>
                </div>
              ) : (
                <div className="p-4 space-y-4">
                  {items.map((item) => (
                    <div key={item.id} className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <Image
                          src={getProductImage(item.product.images)}
                          alt={item.product.name}
                          width={60}
                          height={60}
                          className="rounded-md object-cover"
                        />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {item.product.name}
                        </h4>
                        <p className="text-sm text-gray-500">
                          {(item.product.price || 0).toLocaleString('vi-VN')}đ
                        </p>
                        
                        {/* Size and Color Info */}
                        <div className="mt-2 mb-2">
                          {(item.variantId || item.selectedColor) ? (
                            <div className="flex items-center gap-2 flex-wrap">
                              {item.variantId && (
                                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                  <span className="mr-1">📏</span>
                                  {getSizeDisplayName(item.variant, item.variantId)}
                                </span>
                              )}
                              {item.selectedColor && (
                                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-50 text-green-700 border border-green-200">
                                  <div
                                    className="w-3 h-3 rounded-full border border-gray-300 mr-1.5"
                                    style={{ backgroundColor: getColorDisplay(item.selectedColor) }}
                                    title={`Màu: ${item.selectedColor}`}
                                  />
                                  {item.selectedColor}
                                </span>
                              )}
                            </div>
                          ) : (
                            <span className="text-xs text-gray-500">
                              Size/màu: Mặc định
                            </span>
                          )}
                        </div>

                        {/* Quantity Controls */}
                        <div className="flex items-center">
                          <button
                            onClick={() => handleQuantityUpdate(item.id, item.quantity - 1)}
                            className="p-1 hover:bg-gray-100 rounded transition-colors"
                            disabled={loading}
                          >
                            <Minus className="w-3 h-3" />
                          </button>
                          <span className="mx-2 text-sm font-medium">
                            {item.quantity}
                          </span>
                          <button
                            onClick={() => handleQuantityUpdate(item.id, item.quantity + 1)}
                            className="p-1 hover:bg-gray-100 rounded transition-colors"
                            disabled={loading}
                          >
                            <Plus className="w-3 h-3" />
                          </button>
                        </div>
                      </div>

                      <button
                        onClick={() => handleRemoveItem(item.id)}
                        className="p-1 text-red-500 hover:text-red-700 transition-colors"
                        disabled={loading}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {items.length > 0 && (
              <div className="p-4 border-t border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-lg font-semibold text-gray-900">
                    Tổng cộng:
                  </span>
                  <span className="text-lg font-bold text-orange-500">
                    {total.toLocaleString('vi-VN')}đ
                  </span>
                </div>
                
                <div className="space-y-2">
                  <Link
                    href="/gio-hang"
                    onClick={() => setIsOpen(false)}
                    className="w-full bg-orange-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-orange-600 transition-colors text-center block"
                  >
                    Xem giỏ hàng
                  </Link>
                  <Link
                    href="/thanh-toan"
                    onClick={() => setIsOpen(false)}
                    className="w-full bg-gray-900 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-800 transition-colors text-center block"
                  >
                    Thanh toán
                  </Link>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CartDropdown; 