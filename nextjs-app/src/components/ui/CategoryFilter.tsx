'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Category } from '@prisma/client';

interface CategoryFilterProps {
  categories: Category[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  loading?: boolean;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategory,
  onCategoryChange,
  loading = false
}) => {
  if (loading) {
    return (
      <div className="flex justify-center space-x-4">
        {[...Array(5)].map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-10 bg-gray-300 rounded-full w-24"></div>
          </div>
        ))}
      </div>
    );
  }

  const allCategories = [
    { id: '', name: 'Tất <PERSON>ả Sản Phẩm', slug: '' },
    ...categories
  ];

  return (
    <div className="flex flex-wrap justify-center gap-4">
      {allCategories.map((category) => {
        const isSelected = selectedCategory === category.slug;
        
        return (
          <motion.button
            key={category.id || 'all'}
            onClick={() => onCategoryChange(category.slug)}
            className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
              isSelected
                ? 'bg-black text-white shadow-lg'
                : 'bg-white text-gray-700 border border-gray-300 hover:border-gray-400 hover:shadow-md'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {category.name}
          </motion.button>
        );
      })}
    </div>
  );
};

export default CategoryFilter;
