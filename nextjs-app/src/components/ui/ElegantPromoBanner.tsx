'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Truck, Shield, Star, Gift } from 'lucide-react';
import Link from 'next/link';

const ElegantPromoBanner = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [currentPromo, setCurrentPromo] = useState(0);

  const promos = [
    {
      icon: Truck,
      text: "Miễn phí vận chuyển cho đơn hàng từ 500K",
      link: "/san-pham",
      bgColor: "bg-gray-900",
      textColor: "text-white"
    },
    {
      icon: Gift,
      text: "Ưu đãi đặc biệt - Giảm giá lên đến 30% cho khách hàng mới",
      link: "/ban-chay",
      bgColor: "bg-gray-800",
      textColor: "text-white"
    },
    {
      icon: Shield,
      text: "Đổi trả miễn phí trong 30 ngày - <PERSON> kết chất lượng",
      link: "/huong-dan-mua-hang",
      bgColor: "bg-gray-900",
      textColor: "text-white"
    },
    {
      icon: Star,
      text: "Bộ sưu tập mới - Xu hướng thời trang 2025",
      link: "/danh-muc",
      bgColor: "bg-gray-800",
      textColor: "text-white"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentPromo((prev) => (prev + 1) % promos.length);
    }, 4000);

    return () => clearInterval(timer);
  }, [promos.length]);

  if (!isVisible) return null;

  const currentPromoData = promos[currentPromo];

  return (
    <motion.div
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: 'auto', opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      transition={{ duration: 0.3 }}
      className={`relative ${currentPromoData.bgColor} ${currentPromoData.textColor} overflow-hidden`}
    >
      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent" />
      </div>

      <div className="relative container mx-auto px-4">
        <div className="flex items-center justify-between py-3">
          {/* Promo content */}
          <div className="flex-1 flex items-center justify-center">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentPromo}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: -20, opacity: 0 }}
                transition={{ duration: 0.4 }}
                className="flex items-center space-x-3"
              >
                <currentPromoData.icon className="w-4 h-4 flex-shrink-0" />
                <Link 
                  href={currentPromoData.link}
                  className="text-sm font-medium hover:underline transition-all duration-200 text-center"
                >
                  {currentPromoData.text}
                </Link>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Close button */}
          <button
            onClick={() => setIsVisible(false)}
            className="ml-4 p-1 hover:bg-white/10 rounded-full transition-colors duration-200 flex-shrink-0"
            aria-label="Đóng banner"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Progress indicator */}
      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-white/20">
        <motion.div
          key={currentPromo}
          initial={{ width: 0 }}
          animate={{ width: '100%' }}
          transition={{ duration: 4, ease: 'linear' }}
          className="h-full bg-white/60"
        />
      </div>
    </motion.div>
  );
};

export default ElegantPromoBanner;
