'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Heart, Star, Crown, Sparkles, ShoppingBag, Gift } from 'lucide-react';

const KFashionPromoBanner = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [currentPromoIndex, setCurrentPromoIndex] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  // K-Fashion themed promotions
  const promos = [
    {
      id: 'k-fashion-sale',
      icon: Crown,
      text: "👑 K-FASHION SALE - GIẢM GIÁ ĐẾN 50% CHO PHONG CÁCH IDOL",
      gradient: "from-pink-500 via-purple-500 to-indigo-600",
      bgPattern: "hearts"
    },
    {
      id: 'seoul-style',
      icon: Star,
      text: "⭐ SEOUL STREET STYLE - BỘ SƯU TẬP MỚI VỪA RA MẮT",
      gradient: "from-blue-500 via-cyan-500 to-teal-600",
      bgPattern: "stars"
    },
    {
      id: 'idol-inspired',
      icon: Sparkles,
      text: "✨ IDOL INSPIRED LOOKS - MIỄN PHÍ SHIP CHO ĐƠN HÀNG TRÊN 399K",
      gradient: "from-purple-500 via-pink-500 to-red-500",
      bgPattern: "sparkles"
    },
    {
      id: 'k-pop-vibes',
      icon: Heart,
      text: "💖 K-POP VIBES - MUA 2 TẶNG 1 CHO TẤT CẢ PHỤ KIỆN",
      gradient: "from-rose-500 via-pink-500 to-purple-600",
      bgPattern: "mixed"
    }
  ];

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Rotate promotions
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPromoIndex((prev) => (prev + 1) % promos.length);
    }, 4500); // Change every 4.5 seconds

    return () => clearInterval(interval);
  }, [promos.length]);

  if (!isVisible) return null;

  const currentPromo = promos[currentPromoIndex];
  const IconComponent = currentPromo.icon;

  const renderBackgroundPattern = () => {
    if (!isMounted) return null;

    const patterns = {
      hearts: [...Array(8)].map((_, i) => (
        <motion.div
          key={`heart-${i}`}
          className="absolute"
          style={{
            left: `${(i * 12 + 5) % 90}%`,
            top: `${(i * 7 + 10) % 80}%`,
          }}
          animate={{
            y: [0, -20, 0],
            scale: [0.8, 1.2, 0.8],
            opacity: [0.3, 0.7, 0.3]
          }}
          transition={{
            duration: 3 + i * 0.5,
            repeat: Infinity,
            delay: i * 0.3
          }}
        >
          <Heart className="w-4 h-4 text-white/20" fill="currentColor" />
        </motion.div>
      )),
      stars: [...Array(10)].map((_, i) => (
        <motion.div
          key={`star-${i}`}
          className="absolute"
          style={{
            left: `${(i * 10 + 3) % 95}%`,
            top: `${(i * 11 + 5) % 90}%`,
          }}
          animate={{
            rotate: [0, 360],
            scale: [0.5, 1, 0.5],
            opacity: [0.2, 0.6, 0.2]
          }}
          transition={{
            duration: 4 + i * 0.3,
            repeat: Infinity,
            delay: i * 0.2
          }}
        >
          <Star className="w-3 h-3 text-white/25" fill="currentColor" />
        </motion.div>
      )),
      sparkles: [...Array(12)].map((_, i) => (
        <motion.div
          key={`sparkle-${i}`}
          className="absolute"
          style={{
            left: `${(i * 8 + 2) % 96}%`,
            top: `${(i * 13 + 8) % 85}%`,
          }}
          animate={{
            scale: [0, 1.5, 0],
            opacity: [0, 0.8, 0]
          }}
          transition={{
            duration: 2.5 + i * 0.2,
            repeat: Infinity,
            delay: i * 0.4
          }}
        >
          <Sparkles className="w-3 h-3 text-white/30" />
        </motion.div>
      )),
      mixed: [...Array(15)].map((_, i) => {
        const icons = [Heart, Star, Sparkles, Crown];
        const IconComp = icons[i % icons.length];
        return (
          <motion.div
            key={`mixed-${i}`}
            className="absolute"
            style={{
              left: `${(i * 6 + 1) % 98}%`,
              top: `${(i * 9 + 3) % 94}%`,
            }}
            animate={{
              y: [0, -15, 0],
              rotate: [0, 180, 360],
              scale: [0.6, 1, 0.6],
              opacity: [0.2, 0.5, 0.2]
            }}
            transition={{
              duration: 3.5 + i * 0.1,
              repeat: Infinity,
              delay: i * 0.15
            }}
          >
            <IconComp className="w-3 h-3 text-white/25" fill={i % 2 === 0 ? "currentColor" : "none"} />
          </motion.div>
        );
      })
    };

    return patterns[currentPromo.bgPattern as keyof typeof patterns] || patterns.mixed;
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ height: 0, opacity: 0 }}
        animate={{ height: 'auto', opacity: 1 }}
        exit={{ height: 0, opacity: 0 }}
        className={`relative overflow-hidden bg-gradient-to-r ${currentPromo.gradient} text-white`}
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 overflow-hidden">
          {renderBackgroundPattern()}
          
          {/* Animated gradient overlay */}
          <motion.div
            key={`gradient-${currentPromo.id}`}
            animate={{
              x: ['-100%', '100%'],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: 'linear'
            }}
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
            style={{ width: '200%' }}
          />
        </div>

        {/* Content Container */}
        <div className="relative container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Main Promo Content */}
            <div className="flex items-center space-x-4 flex-1">
              {/* Animated Icon */}
              <motion.div
                key={`icon-${currentPromo.id}`}
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.6, type: 'spring', bounce: 0.5 }}
                className="hidden sm:block"
              >
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 10, -10, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                >
                  <IconComponent className="w-7 h-7" />
                </motion.div>
              </motion.div>

              {/* Promo Text */}
              <motion.div
                key={`text-${currentPromo.id}`}
                initial={{ x: -50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="flex-1"
              >
                <motion.p
                  className="text-sm md:text-base lg:text-lg font-bold tracking-wide"
                  animate={{
                    textShadow: [
                      '0 0 0px rgba(255,255,255,0)',
                      '0 0 10px rgba(255,255,255,0.5)',
                      '0 0 0px rgba(255,255,255,0)'
                    ]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity
                  }}
                >
                  {currentPromo.text}
                </motion.p>
              </motion.div>
            </div>

            {/* CTA Button */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.4, type: 'spring' }}
              className="hidden md:block ml-6"
            >
              <motion.a
                href="/danh-muc"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center space-x-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 px-6 py-2 rounded-full font-semibold text-sm transition-all duration-300 border border-white/30"
              >
                <ShoppingBag className="w-4 h-4" />
                <span>Shop Now</span>
              </motion.a>
            </motion.div>

            {/* Close Button */}
            <motion.button
              whileHover={{ scale: 1.1, rotate: 90 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsVisible(false)}
              className="ml-4 p-2 hover:bg-white/20 rounded-full transition-colors group"
              aria-label="Đóng banner"
            >
              <X className="w-4 h-4 group-hover:rotate-90 transition-transform duration-300" />
            </motion.button>
          </div>

          {/* Mobile CTA */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="md:hidden mt-3 text-center"
          >
            <motion.a
              href="/danh-muc"
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center space-x-2 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full font-semibold text-sm border border-white/30"
            >
              <Gift className="w-4 h-4" />
              <span>Khám Phá Ngay</span>
            </motion.a>
          </motion.div>
        </div>

        {/* Progress Indicator */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20">
          <motion.div
            key={`progress-${currentPromo.id}`}
            className="h-full bg-white/60"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 4.5, ease: 'linear' }}
          />
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default KFashionPromoBanner;
