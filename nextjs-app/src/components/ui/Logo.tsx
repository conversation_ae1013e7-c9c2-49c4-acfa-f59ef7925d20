'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';

interface LogoProps {
  variant?: 'default' | 'admin' | 'mobile' | 'footer';
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  className?: string;
  href?: string;
}

export default function Logo({
  variant = 'default',
  size = 'md',
  className = '',
  href = '/'
}: LogoProps) {
  
  // Size configurations
  const sizeConfig = {
    sm: {
      width: 40,
      height: 40,
      text: 'text-sm',
      container: 'space-x-2'
    },
    md: {
      width: 50,
      height: 50,
      text: 'text-lg',
      container: 'space-x-3'
    },
    lg: {
      width: 60,
      height: 60,
      text: 'text-xl',
      container: 'space-x-3'
    },
    xl: {
      width: 80,
      height: 80,
      text: 'text-2xl',
      container: 'space-x-4'
    },
    '2xl': {
      width: 100,
      height: 100,
      text: 'text-3xl',
      container: 'space-x-5'
    },
    '3xl': {
      width: 120,
      height: 120,
      text: 'text-4xl',
      container: 'space-x-6'
    }
  };

  // Variant configurations - Elegant styling with original colors
  const variantConfig = {
    default: {
      iconColor: 'from-blue-600 to-purple-600',
      textColor: 'text-gray-900',
      hoverEffect: 'hover:scale-105'
    },
    admin: {
      iconColor: 'from-blue-500 to-indigo-600',
      textColor: 'text-gray-800',
      hoverEffect: 'hover:scale-105'
    },
    mobile: {
      iconColor: 'from-blue-600 to-purple-600',
      textColor: 'text-white',
      hoverEffect: 'hover:scale-105'
    },
    footer: {
      iconColor: 'from-blue-400 to-purple-500',
      textColor: 'text-gray-300',
      hoverEffect: 'hover:scale-105'
    }
  };

  const config = sizeConfig[size];
  const variantStyle = variantConfig[variant];

  const LogoIcon = () => (
    <div className="relative overflow-hidden">
      <Image
        src="/logo.png"
        alt="ThinLuong Fashion Store"
        width={config.width}
        height={config.height}
        className="object-contain drop-shadow-lg filter brightness-110 contrast-110"
        priority
      />
      {/* Elegant rectangular gradient background for larger sizes */}
      {(size === 'xl' || size === '2xl' || size === '3xl') && (
        <>
          {/* Main gradient background */}
          <motion.div
            className="absolute inset-0 opacity-20 bg-gradient-to-r from-blue-400/40 to-purple-400/40 -z-10 rounded-xl"
            style={{
              width: config.width * 1.15,
              height: config.height * 0.75,
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)'
            }}
            animate={{
              scale: [1, 1.08, 1],
              opacity: [0.2, 0.35, 0.2],
              background: [
                'linear-gradient(to right, rgba(96, 165, 250, 0.4), rgba(168, 85, 247, 0.4))',
                'linear-gradient(to right, rgba(168, 85, 247, 0.4), rgba(236, 72, 153, 0.4))',
                'linear-gradient(to right, rgba(236, 72, 153, 0.4), rgba(96, 165, 250, 0.4))',
                'linear-gradient(to right, rgba(96, 165, 250, 0.4), rgba(168, 85, 247, 0.4))'
              ]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />

          {/* Secondary glow effect */}
          <motion.div
            className="absolute inset-0 opacity-10 bg-gradient-to-r from-cyan-300/20 to-pink-300/20 -z-20 rounded-2xl blur-sm"
            style={{
              width: config.width * 1.3,
              height: config.height * 0.9,
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)'
            }}
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.1, 0.2, 0.1],
              rotate: [0, 2, -2, 0]
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          />
        </>
      )}
    </div>
  );


  const LogoContent = () => (
    <motion.div
      className={`${variantStyle.hoverEffect} transition-all duration-300 ${className}`}
      whileHover={{
        scale: 1.05,
        filter: 'brightness(1.15) saturate(1.1)',
        rotateY: 5
      }}
      whileTap={{ scale: 0.95 }}
      initial={{
        filter: 'brightness(1) saturate(1)',
        rotateY: 0
      }}
      animate={{
        filter: 'brightness(1) saturate(1)',
        rotateY: 0
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 20
      }}
    >
      <LogoIcon />
    </motion.div>
  );

  if (href) {
    return (
      <Link href={href} className="inline-block">
        <LogoContent />
      </Link>
    );
  }

  return <LogoContent />;
}

// Preset components for common use cases
export const LogoDefault = (props: Omit<LogoProps, 'variant'>) => (
  <Logo variant="default" {...props} />
);

export const LogoAdmin = (props: Omit<LogoProps, 'variant'>) => (
  <Logo variant="admin" {...props} />
);

export const LogoMobile = (props: Omit<LogoProps, 'variant'>) => (
  <Logo variant="mobile" {...props} />
);

export const LogoFooter = (props: Omit<LogoProps, 'variant'>) => (
  <Logo variant="footer" {...props} />
);

// Icon-only version for compact spaces
export const LogoIcon = ({ size = 'md', variant = 'default', className = '', href }: Pick<LogoProps, 'size' | 'variant' | 'className' | 'href'>) => (
  <Logo
    variant={variant}
    size={size}
    className={className}
    href={href}
  />
);

