'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Check } from 'lucide-react';

interface ProductAttributeTagsProps {
  sizes: string[];
  colors: string[];
  selectedSize?: string | null;
  selectedColor?: string | null;
  onSizeChange: (size: string) => void;
  onColorChange: (color: string) => void;
  disabled?: boolean;
  showLabels?: boolean;
  showValidationErrors?: boolean;
}

export default function ProductAttributeTags({
  sizes,
  colors,
  selectedSize,
  selectedColor,
  onSizeChange,
  onColorChange,
  disabled = false,
  showLabels = true,
  showValidationErrors = true
}: ProductAttributeTagsProps) {
  // Color mapping for visual representation
  const getColorDisplay = (color: string) => {
    const colorMap: { [key: string]: string } = {
      'đen': '#000000',
      'trắng': '#FFFFFF',
      'đỏ': '#DC2626',
      'xanh': '#2563EB',
      'xanh dương': '#1E40AF',
      'xanh lá': '#16A34A',
      'vàng': '#EAB308',
      'hồng': '#EC4899',
      'tím': '#7C3AED',
      'xám': '#6B7280',
      'nâu': '#92400E',
      'cam': '#EA580C',
      'be': '#D4B896',
      'kem': '#FEF3C7',
      'navy': '#1E3A8A',
      'burgundy': '#7F1D1D'
    };

    const normalizedColor = color.toLowerCase();
    return colorMap[normalizedColor] || '#6B7280';
  };

  // Validation helpers
  const isSizeRequired = sizes.length > 0;
  const isColorRequired = colors.length > 0;
  const isSizeMissing = isSizeRequired && !selectedSize;
  const isColorMissing = isColorRequired && !selectedColor;

  return (
    <div className="space-y-6">
      {/* Size Tags */}
      {sizes.length > 0 && (
        <div>
          {showLabels && (
            <div className="flex items-center mb-3">
              <span className="text-sm font-medium text-gray-700 mr-2">
                📏 Kích thước:
              </span>
              {showValidationErrors && isSizeMissing && (
                <span className="text-xs text-red-500 font-medium">
                  (Vui lòng chọn size)
                </span>
              )}
            </div>
          )}
          
          <div className="flex flex-wrap gap-2">
            {sizes.map((size, index) => (
              <motion.button
                key={size}
                type="button"
                onClick={() => !disabled && onSizeChange(size)}
                disabled={disabled}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                whileHover={!disabled ? { scale: 1.05 } : {}}
                whileTap={!disabled ? { scale: 0.95 } : {}}
                className={`
                  relative min-w-[44px] min-h-[44px] px-4 py-2 rounded-lg text-sm font-medium 
                  transition-all duration-200 border-2 flex items-center justify-center
                  ${disabled 
                    ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed' 
                    : selectedSize === size
                      ? 'bg-blue-600 text-white border-blue-600 shadow-md'
                      : isSizeMissing && showValidationErrors
                        ? 'bg-white text-gray-700 border-red-300 hover:border-red-400 hover:bg-red-50'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400 hover:bg-gray-50'
                  }
                `}
              >
                {size}
                {selectedSize === size && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center"
                  >
                    <Check className="w-3 h-3 text-white" />
                  </motion.div>
                )}
              </motion.button>
            ))}
          </div>
        </div>
      )}

      {/* Color Tags */}
      {colors.length > 0 && (
        <div>
          {showLabels && (
            <div className="flex items-center mb-3">
              <span className="text-sm font-medium text-gray-700 mr-2">
                🎨 Màu sắc:
              </span>
              {showValidationErrors && isColorMissing && (
                <span className="text-xs text-red-500 font-medium">
                  (Vui lòng chọn màu)
                </span>
              )}
            </div>
          )}
          
          <div className="flex flex-wrap gap-2">
            {colors.map((color, index) => (
              <motion.button
                key={color}
                type="button"
                onClick={() => !disabled && onColorChange(color)}
                disabled={disabled}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                whileHover={!disabled ? { scale: 1.05 } : {}}
                whileTap={!disabled ? { scale: 0.95 } : {}}
                className={`
                  relative min-w-[44px] min-h-[44px] px-3 py-2 rounded-lg text-sm font-medium 
                  transition-all duration-200 border-2 flex items-center gap-2
                  ${disabled 
                    ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed' 
                    : selectedColor === color
                      ? 'bg-purple-600 text-white border-purple-600 shadow-md'
                      : isColorMissing && showValidationErrors
                        ? 'bg-white text-gray-700 border-red-300 hover:border-red-400 hover:bg-red-50'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400 hover:bg-gray-50'
                  }
                `}
              >
                <div
                  className={`w-4 h-4 rounded-full border-2 border-white shadow-sm ${
                    getColorDisplay(color) === '#FFFFFF' ? 'border-gray-300' : ''
                  }`}
                  style={{ backgroundColor: getColorDisplay(color) }}
                />
                <span className="capitalize">{color}</span>
                {selectedColor === color && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center"
                  >
                    <Check className="w-3 h-3 text-white" />
                  </motion.div>
                )}
              </motion.button>
            ))}
          </div>
        </div>
      )}

      {/* Validation Error Summary */}
      {showValidationErrors && (isSizeMissing || isColorMissing) && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-3"
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Vui lòng chọn đầy đủ thông tin:
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <ul className="list-disc list-inside space-y-1">
                  {isSizeMissing && <li>Chọn kích thước</li>}
                  {isColorMissing && <li>Chọn màu sắc</li>}
                </ul>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
} 