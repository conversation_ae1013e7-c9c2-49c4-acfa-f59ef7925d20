'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ShoppingBag, Heart } from 'lucide-react';
import { motion } from 'framer-motion';
import { ProductWithCategory } from '../../types';
import { useCart } from '../../contexts/CartContext';
import { useWishlist } from '../../contexts/WishlistContext';
import { useToast } from './Toast';

interface ProductCardProps {
  product: ProductWithCategory;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { addItem: addToCart } = useCart();
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist();
  const { showToast } = useToast();

  // Handle images - could be array or JSON string
  const getProductImages = () => {
    try {
      if (Array.isArray(product.images)) {
        return product.images;
      }
      if (typeof product.images === 'string' && product.images.trim()) {
        // Try to parse as JSON
        return JSON.parse(product.images);
      }
      return [];
    } catch (error) {
      console.warn('Error parsing product images:', error);
      return [];
    }
  };

  const images = getProductImages();
  const mainImage = images?.[0] || '/images/placeholder.svg';
  const isInWishlistState = isInWishlist(product.id);

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsLoading(true);
    try {
      await addToCart(product.id, 1);
      showToast(`${product.name} đã được thêm vào giỏ hàng`, 'success', 'cart');
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast('Có lỗi xảy ra khi thêm vào giỏ hàng', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleWishlistToggle = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    try {
      if (isInWishlistState) {
        await removeFromWishlist(product.id);
        showToast(`${product.name} đã được xóa khỏi danh sách yêu thích`, 'info', 'heart');
      } else {
        await addToWishlist(product.id);
        showToast(`${product.name} đã được thêm vào danh sách yêu thích`, 'success', 'heart');
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      showToast('Có lỗi xảy ra khi cập nhật danh sách yêu thích', 'error');
    }
  };

  // Calculate average rating with safe checking
  const reviews = product.reviews || [];
  const averageRating = reviews.length > 0
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
    : 0;

  // Safe access to category and _count
  const categoryName = product.category?.name || 'Chưa phân loại';
  const reviewCount = product._count?.reviews || 0;

  return (
    <Link
      href={`/san-pham/${product.slug}`}
      className="group elegant-product-card relative block cursor-pointer transform hover:scale-[1.02] transition-transform duration-300"
      aria-label={`Xem chi tiết sản phẩm ${product.name}`}
    >
      {/* Product Image */}
      <div className="relative aspect-[3/4] overflow-hidden bg-gray-50 rounded-t-xl">
        <Image
          src={mainImage}
          alt={product.name}
          fill
          className="elegant-product-image object-cover transition-transform duration-300 group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          onError={(e) => {
            console.warn('Image load error for product:', product.name, 'Image URL:', mainImage);
            e.currentTarget.src = '/images/placeholder.svg';
          }}
        />

        {/* Elegant badges */}
        <div className="absolute top-3 left-3 flex flex-col space-y-2">
          {product.isOnSale && (
            <span className="elegant-product-badge-sale">
              SALE
            </span>
          )}
          {(product.stock || 0) < 5 && (product.stock || 0) > 0 && (
            <span className="elegant-product-badge">
              Sắp hết
            </span>
          )}
          {(product.stock || 0) === 0 && (
            <span className="elegant-product-badge">
              Hết hàng
            </span>
          )}
        </div>

        {/* Wishlist Button - Higher z-index to work over clickable card */}
        <button
          onClick={handleWishlistToggle}
          className={`absolute top-3 right-3 w-9 h-9 rounded-lg transition-all duration-200 z-30 ${
            isInWishlistState
              ? 'bg-red-500 text-white shadow-md'
              : 'bg-white text-gray-600 hover:bg-gray-50 hover:text-red-500 shadow-sm border border-gray-200'
          }`}
          aria-label={`${isInWishlistState ? 'Remove from' : 'Add to'} wishlist`}
        >
          <Heart className={`w-4 h-4 mx-auto ${isInWishlistState ? 'fill-current' : ''}`} />
        </button>
      </div>

      {/* Product Info */}
      <div className="p-4">
        {/* Category */}
        {categoryName && (
          <span className="elegant-badge text-xs mb-2 inline-block">
            {categoryName}
          </span>
        )}

        {/* Product title - no separate link needed since entire card is clickable */}
        <h3 className="elegant-subtitle text-base mb-3 line-clamp-2 group-hover:text-gray-600 transition-colors">
          {product.name}
        </h3>

        {/* Rating Stars */}
        <div className="flex items-center mb-3">
          <div className="flex text-yellow-400">
            {[...Array(5)].map((_, i) => (
              <svg
                key={i}
                className={`w-3 h-3 ${i < Math.floor(averageRating) ? 'text-yellow-400' : 'text-gray-300'}`}
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
          </div>
          <span className="text-xs text-gray-500 ml-1">({reviewCount})</span>
        </div>

        {/* Price with elegant styling */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex flex-col">
            {product.isOnSale && product.originalPrice ? (
              <>
                <span className="text-xs text-gray-500 line-through mb-1">
                  {(product.originalPrice || 0).toLocaleString('vi-VN')}đ
                </span>
                <span className="elegant-product-price-sale">
                  {(product.price || 0).toLocaleString('vi-VN')}đ
                </span>
              </>
            ) : (
              <span className="elegant-product-price">
                {(product.price || 0).toLocaleString('vi-VN')}đ
              </span>
            )}
          </div>

          {/* Stock indicator */}
          {(product.stock || 0) <= 5 && (product.stock || 0) > 0 && (
            <span className="elegant-badge text-xs">
              Chỉ còn {product.stock}
            </span>
          )}
        </div>

        {/* Rating */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            <div className="flex text-yellow-400">
              {[...Array(5)].map((_, i) => (
                <svg key={i} className="w-4 h-4 fill-current" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <span className="text-xs text-gray-500">(4.8)</span>
          </div>

          {/* Quick add to cart on hover */}
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <span className="text-xs text-gray-500">Click để xem</span>
          </div>
        </div>

      </div>
    </Link>
  );
};

export default ProductCard;
