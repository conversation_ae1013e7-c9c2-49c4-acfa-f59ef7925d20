'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ProductCard from './ProductCard';
import { ProductWithCategory, ProductFilters } from '../../types';

interface ProductGridProps {
  filters?: ProductFilters;
  title?: string;
  showPagination?: boolean;
  limit?: number;
}

const ProductGrid: React.FC<ProductGridProps> = ({
  filters = {},
  title,
  showPagination = true,
  limit = 12
}) => {
  const [products, setProducts] = useState<ProductWithCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit,
    total: 0,
    totalPages: 0
  });

  const fetchProducts = async (page: number = 1) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([, value]) => value !== undefined && value !== '')
        )
      });

      const response = await fetch(`/api/products?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }

      const result = await response.json();
      if (result.success && result.data) {
        const productsData = result.data.products || [];
        const paginationData = result.data.pagination || {
          page: 1,
          limit,
          total: 0,
          totalPages: 0
        };
        
        setProducts(productsData);
        setPagination(paginationData);
      } else {
        throw new Error(result.error || result.message || 'Failed to fetch products');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts(1);
  }, [filters, limit]);

  const handlePageChange = (newPage: number) => {
    fetchProducts(newPage);
    // Scroll to top of grid
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {title && (
          <h2 className="text-2xl font-bold text-center">{title}</h2>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(limit)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-300 aspect-[3/4] rounded-lg mb-4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                <div className="h-8 bg-gray-300 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-semibold">Lỗi Tải Sản Phẩm</h3>
          <p className="text-gray-600 mt-2">{error}</p>
        </div>
        <button
          onClick={() => fetchProducts(pagination?.page || 1)}
          className="bg-black text-white px-6 py-2 rounded-md hover:bg-gray-800 transition-colors"
        >
          Thử Lại
        </button>
      </div>
    );
  }

  if (!products || products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5" />
          </svg>
          <h3 className="text-lg font-semibold">Không Tìm Thấy Sản Phẩm</h3>
          <p className="text-gray-600 mt-2">Hãy thử điều chỉnh tiêu chí tìm kiếm hoặc bộ lọc của bạn.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {title && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <h2 className="text-3xl font-bold text-gray-900">{title}</h2>
          <p className="text-gray-600 mt-2">
            Hiển thị {products?.length || 0} trong tổng số {pagination?.total || 0} sản phẩm
          </p>
        </motion.div>
      )}

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      >
        {products?.map((product) => (
          <motion.div key={product.id} variants={itemVariants}>
            <ProductCard product={product} />
          </motion.div>
        ))}
      </motion.div>

      {showPagination && pagination?.totalPages > 1 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-center items-center space-x-2 mt-8"
        >
          <button
            onClick={() => handlePageChange((pagination?.page || 1) - 1)}
            disabled={pagination?.page === 1}
            className="px-4 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Trước
          </button>

          <div className="flex space-x-1">
            {[...Array(pagination?.totalPages || 0)].map((_, index) => {
              const page = index + 1;
              const isCurrentPage = page === pagination?.page;
              
              // Show first page, last page, current page, and pages around current page
              if (
                page === 1 ||
                page === pagination?.totalPages ||
                (page >= (pagination?.page || 1) - 1 && page <= (pagination?.page || 1) + 1)
              ) {
                return (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-3 py-2 rounded-md ${
                      isCurrentPage
                        ? 'bg-black text-white'
                        : 'border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                );
              } else if (
                page === (pagination?.page || 1) - 2 ||
                page === (pagination?.page || 1) + 2
              ) {
                return <span key={page} className="px-2">...</span>;
              }
              return null;
            })}
          </div>

          <button
            onClick={() => handlePageChange((pagination?.page || 1) + 1)}
            disabled={pagination?.page === pagination?.totalPages}
            className="px-4 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Sau
          </button>
        </motion.div>
      )}
    </div>
  );
};

export default ProductGrid;
