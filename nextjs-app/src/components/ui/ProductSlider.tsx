'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import ProductCard from './ProductCard';
import { ProductWithCategory, ProductFilters } from '../../types';

interface ProductSliderProps {
  filters?: ProductFilters;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showDots?: boolean;
  slidesToShow?: number;
  title?: string;
}

const ProductSlider: React.FC<ProductSliderProps> = ({
  filters = {},
  autoPlay = false,
  autoPlayInterval = 5000,
  showDots = true,
  slidesToShow = 3,
  title
}) => {
  const [products, setProducts] = useState<ProductWithCategory[]>([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const totalSlides = Math.ceil(products.length / slidesToShow);

  useEffect(() => {
    fetchProducts();
  }, [filters]);

  useEffect(() => {
    if (autoPlay && products.length > slidesToShow) {
      startAutoPlay();
    }
    return () => stopAutoPlay();
  }, [autoPlay, products.length, slidesToShow]);

  const fetchProducts = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        limit: '12',
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '')
        )
      });

      const response = await fetch(`/api/products?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }

      const result = await response.json();
      if (result.success) {
        setProducts(result.data.data);
      } else {
        throw new Error(result.error || 'Failed to fetch products');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const startAutoPlay = () => {
    stopAutoPlay();
    intervalRef.current = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % totalSlides);
    }, autoPlayInterval);
  };

  const stopAutoPlay = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const goToSlide = (slideIndex: number) => {
    setCurrentSlide(slideIndex);
    if (autoPlay) {
      startAutoPlay(); // Restart autoplay
    }
  };

  const nextSlide = () => {
    goToSlide((currentSlide + 1) % totalSlides);
  };

  const prevSlide = () => {
    goToSlide(currentSlide === 0 ? totalSlides - 1 : currentSlide - 1);
  };

  const getVisibleProducts = () => {
    const startIndex = currentSlide * slidesToShow;
    return products.slice(startIndex, startIndex + slidesToShow);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {title && <h2 className="text-2xl font-bold text-center">{title}</h2>}
        <div className="flex gap-6 overflow-hidden">
          {[...Array(slidesToShow)].map((_, index) => (
            <div key={index} className="flex-1 animate-pulse">
              <div className="bg-gray-300 aspect-[3/4] rounded-lg mb-4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                <div className="h-8 bg-gray-300 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error || products.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">
          {error || 'No products found for this collection.'}
        </p>
      </div>
    );
  }

  return (
    <div className="relative">
      {title && (
        <h2 className="text-2xl font-bold text-center mb-8">{title}</h2>
      )}

      <div className="relative overflow-hidden">
        {/* Navigation Buttons */}
        {totalSlides > 1 && (
          <>
            <button
              onClick={prevSlide}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/90 hover:bg-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
              onMouseEnter={stopAutoPlay}
              onMouseLeave={() => autoPlay && startAutoPlay()}
            >
              <ChevronLeft className="w-6 h-6 text-gray-800" />
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/90 hover:bg-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
              onMouseEnter={stopAutoPlay}
              onMouseLeave={() => autoPlay && startAutoPlay()}
            >
              <ChevronRight className="w-6 h-6 text-gray-800" />
            </button>
          </>
        )}

        {/* Products Container */}
        <div
          className="flex transition-transform duration-500 ease-in-out gap-6"
          style={{
            transform: `translateX(-${currentSlide * 100}%)`,
            width: `${totalSlides * 100}%`
          }}
          onMouseEnter={stopAutoPlay}
          onMouseLeave={() => autoPlay && startAutoPlay()}
        >
          {Array.from({ length: totalSlides }).map((_, slideIndex) => (
            <div
              key={slideIndex}
              className="flex gap-6"
              style={{ width: `${100 / totalSlides}%` }}
            >
              {products
                .slice(slideIndex * slidesToShow, (slideIndex + 1) * slidesToShow)
                .map((product) => (
                  <div key={product.id} style={{ width: `${100 / slidesToShow}%` }}>
                    <ProductCard product={product} />
                  </div>
                ))}
            </div>
          ))}
        </div>
      </div>

      {/* Dots Indicator */}
      {showDots && totalSlides > 1 && (
        <div className="flex justify-center mt-8 space-x-2">
          {Array.from({ length: totalSlides }).map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentSlide
                  ? 'bg-gray-800 scale-125'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>
      )}

      {/* Product Counter */}
      <div className="text-center mt-4 text-sm text-gray-500">
        Showing {getVisibleProducts().length} of {products.length} products
      </div>
    </div>
  );
};

export default ProductSlider;
