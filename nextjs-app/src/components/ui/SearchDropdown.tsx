'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Search, Package, Tag, ArrowRight, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  images: string | string[];
  category: {
    id: string;
    name: string;
    slug: string;
  };
  variants: any[];
  reviewCount: number;
  stock: number;
}

interface Category {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  image: string | null;
  productCount: number;
}

interface SearchResults {
  products: Product[];
  categories: Category[];
  total: number;
}

interface SearchDropdownProps {
  placeholder?: string;
  className?: string;
  onSearch?: (query: string) => void;
}

const SearchDropdown: React.FC<SearchDropdownProps> = ({
  placeholder = "Tìm kiếm sản phẩm...",
  className = "",
  onSearch
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResults>({ products: [], categories: [], total: 0 });
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // Get product image
  const getProductImage = (images: string | string[]) => {
    try {
      const imageArray = Array.isArray(images) 
        ? images 
        : (images ? JSON.parse(images) : []);
      return imageArray.length > 0 ? imageArray[0] : '/images/placeholder.svg';
    } catch {
      return '/images/placeholder.svg';
    }
  };

  // Format price
  const formatPrice = (price: number) => {
    return price.toLocaleString('vi-VN') + 'đ';
  };

  // Search function
  const performSearch = async (searchQuery: string) => {
    if (searchQuery.trim().length < 2) {
      setResults({ products: [], categories: [], total: 0 });
      setIsOpen(false);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/search?q=${encodeURIComponent(searchQuery)}&limit=10`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setResults(data.data);
          setIsOpen(true);
          setSelectedIndex(-1);
        }
      }
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input change with debounce
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);

    // Clear previous debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Debounce search
    debounceRef.current = setTimeout(() => {
      performSearch(value);
    }, 300);
  };

  // Handle form submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      setIsOpen(false);
      onSearch?.(query.trim());
      // Navigate to search results page
      window.location.href = `/san-pham?search=${encodeURIComponent(query.trim())}`;
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    const totalItems = results.products.length + results.categories.length;
    
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => (prev < totalItems - 1 ? prev + 1 : -1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => (prev > -1 ? prev - 1 : totalItems - 1));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (selectedIndex >= 0) {
        // Navigate to selected item
        if (selectedIndex < results.products.length) {
          const product = results.products[selectedIndex];
          window.location.href = `/san-pham/${product.slug}`;
        } else {
          const categoryIndex = selectedIndex - results.products.length;
          const category = results.categories[categoryIndex];
          window.location.href = `/danh-muc/${category.slug}`;
        }
      } else {
        handleSubmit(e);
      }
    } else if (e.key === 'Escape') {
      setIsOpen(false);
      setSelectedIndex(-1);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Clear debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <input
          ref={inputRef}
          type="search"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => query.length >= 2 && setIsOpen(true)}
          placeholder={placeholder}
          className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
          autoComplete="off"
        />
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        {isLoading && (
          <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 animate-spin" />
        )}
      </form>

      {/* Search Results Dropdown */}
      <AnimatePresence>
        {isOpen && (results.products.length > 0 || results.categories.length > 0) && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto"
          >
            {/* Categories */}
            {results.categories.length > 0 && (
              <div className="border-b border-gray-100">
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Danh mục
                </div>
                {results.categories.map((category, index) => {
                  const itemIndex = results.products.length + index;
                  return (
                    <Link
                      key={category.id}
                      href={`/danh-muc/${category.slug}`}
                      className={`flex items-center px-4 py-2 hover:bg-gray-50 transition-colors ${
                        selectedIndex === itemIndex ? 'bg-gray-50' : ''
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      <Tag className="w-4 h-4 text-gray-400 mr-3" />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">{category.name}</div>
                        <div className="text-xs text-gray-500">{category.productCount} sản phẩm</div>
                      </div>
                    </Link>
                  );
                })}
              </div>
            )}

            {/* Products */}
            {results.products.length > 0 && (
              <div>
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Sản phẩm
                </div>
                {results.products.map((product, index) => (
                  <Link
                    key={product.id}
                    href={`/san-pham/${product.slug}`}
                    className={`flex items-center px-4 py-3 hover:bg-gray-50 transition-colors ${
                      selectedIndex === index ? 'bg-gray-50' : ''
                    }`}
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex-shrink-0 mr-3">
                      <Image
                        src={getProductImage(product.images)}
                        alt={product.name}
                        width={40}
                        height={40}
                        className="rounded object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {product.name}
                      </div>
                      <div className="text-xs text-gray-500">
                        {product.category.name} • {formatPrice(product.price)}
                      </div>
                    </div>
                    <ArrowRight className="w-4 h-4 text-gray-400" />
                  </Link>
                ))}
              </div>
            )}

            {/* View All Results */}
            {results.total > 0 && (
              <div className="border-t border-gray-100">
                <Link
                  href={`/san-pham?search=${encodeURIComponent(query)}`}
                  className="flex items-center justify-center px-4 py-3 text-sm font-medium text-orange-600 hover:text-orange-700 hover:bg-orange-50 transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  Xem tất cả {results.total} kết quả
                  <ArrowRight className="w-4 h-4 ml-1" />
                </Link>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* No Results */}
      <AnimatePresence>
        {isOpen && query.length >= 2 && !isLoading && results.total === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
          >
            <div className="px-4 py-8 text-center">
              <Package className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <div className="text-sm text-gray-500">Không tìm thấy kết quả cho "{query}"</div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SearchDropdown;
