'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Flame, Gift, Clock, Sparkles, X } from 'lucide-react'

const SummerPromoBanner = () => {
  const [isVisible, setIsVisible] = useState(true)
  const [currentPromoIndex, setCurrentPromoIndex] = useState(0)
  const [isMounted, setIsMounted] = useState(false)
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  // Danh sách các khuyến mãi xoay vòng
  const promos = [
    {
      id: 'summer-sale',
      icon: Flame,
      text: "🔥 KHUYẾN MÃI MÙA HÈ - GIẢM GIÁ LÊN ĐẾN 40%",
      gradient: "from-orange-500 to-red-600"
    },
    {
      id: 'free-ship',
      icon: Gift,
      text: "🎁 MIỄN PHÍ SHIP CHO ĐƠN HÀNG TRÊN 500K",
      gradient: "from-blue-500 to-purple-600"
    },
    {
      id: 'buy-2-get-1',
      icon: Sparkles,
      text: "✨ MUA 2 TẶNG 1 - ÁP DỤNG CHO TẤT CẢ SẢN PHẨM",
      gradient: "from-pink-500 to-purple-600"
    }
  ]

  // Set mounted state
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Đếm ngược thời gian khuyến mãi (ví dụ: 7 ngày từ bây giờ)
  useEffect(() => {
    const endDate = new Date()
    endDate.setDate(endDate.getDate() + 7) // 7 ngày từ bây giờ

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const distance = endDate.getTime() - now

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Xoay vòng các khuyến mãi
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPromoIndex((prev) => (prev + 1) % promos.length)
    }, 4000) // Thay đổi mỗi 4 giây

    return () => clearInterval(interval)
  }, [promos.length])

  if (!isVisible) return null

  const currentPromo = promos[currentPromoIndex]
  const IconComponent = currentPromo.icon

  return (
    <AnimatePresence>
      <motion.div
        initial={{ height: 0, opacity: 0 }}
        animate={{ height: 'auto', opacity: 1 }}
        exit={{ height: 0, opacity: 0 }}
        className={`relative overflow-hidden bg-gradient-to-r ${currentPromo.gradient} text-white`}
      >
        {/* Background Animation */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            key={`bg-animation-${currentPromo.id}`}
            animate={{
              x: ['-100%', '100%'],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: 'linear'
            }}
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
            style={{ width: '200%' }}
          />
        </div>

        {/* Content Container */}
        <div className="relative container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Main Promo Text */}
            <div className="flex items-center space-x-4 flex-1">
              <motion.div
                key={`icon-${currentPromo.id}`}
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.5, type: 'spring' }}
                className="hidden sm:block"
              >
                <IconComponent className="w-6 h-6" />
              </motion.div>

              <AnimatePresence mode="wait">
                <motion.div
                  key={`text-${currentPromo.id}`}
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: -20, opacity: 0 }}
                  transition={{ duration: 0.5 }}
                  className="flex-1"
                >
                  <p className="text-sm md:text-base font-bold text-center sm:text-left">
                    {currentPromo.text}
                  </p>
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Countdown Timer */}
            <div className="hidden md:flex items-center space-x-2 text-xs">
              <Clock className="w-4 h-4" />
              <span className="font-medium">Còn lại:</span>
              <div className="flex space-x-1">
                <motion.div
                  key={`days-${timeLeft.days}`}
                  initial={{ scale: 1.2 }}
                  animate={{ scale: 1 }}
                  className="bg-white/20 px-2 py-1 rounded"
                >
                  {timeLeft.days.toString().padStart(2, '0')}d
                </motion.div>
                <motion.div
                  key={`hours-${timeLeft.hours}`}
                  initial={{ scale: 1.2 }}
                  animate={{ scale: 1 }}
                  className="bg-white/20 px-2 py-1 rounded"
                >
                  {timeLeft.hours.toString().padStart(2, '0')}h
                </motion.div>
                <motion.div
                  key={`minutes-${timeLeft.minutes}`}
                  initial={{ scale: 1.2 }}
                  animate={{ scale: 1 }}
                  className="bg-white/20 px-2 py-1 rounded"
                >
                  {timeLeft.minutes.toString().padStart(2, '0')}m
                </motion.div>
                <motion.div
                  key={`seconds-${timeLeft.seconds}`}
                  initial={{ scale: 1.2 }}
                  animate={{ scale: 1 }}
                  className="bg-white/20 px-2 py-1 rounded"
                >
                  {timeLeft.seconds.toString().padStart(2, '0')}s
                </motion.div>
              </div>
            </div>

            {/* Close Button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsVisible(false)}
              className="ml-4 p-1 hover:bg-white/20 rounded-full transition-colors"
              aria-label="Đóng banner"
            >
              <X className="w-4 h-4" />
            </motion.button>
          </div>

          {/* Progress Indicators */}
          <div className="flex justify-center mt-2 space-x-1">
            {promos.map((promo, index) => (
              <motion.div
                key={`progress-${promo.id}`}
                className={`h-1 rounded-full transition-all duration-300 ${
                  index === currentPromoIndex ? 'bg-white w-6' : 'bg-white/40 w-2'
                }`}
                animate={{
                  scale: index === currentPromoIndex ? 1.2 : 1
                }}
              />
            ))}
          </div>
        </div>

        {/* Floating Elements - Only render on client */}
        {isMounted && (
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            {[...Array(6)].map((_, i) => {
              // Generate consistent positions using index-based seed
              const leftPosition = (i * 17 + 23) % 100
              const topPosition = (i * 13 + 31) % 100
              
              return (
                <motion.div
                  key={`sparkle-${currentPromo.id}-${i}`}
                  className="absolute"
                  style={{
                    left: `${leftPosition}%`,
                    top: `${topPosition}%`,
                  }}
                  animate={{
                    y: [0, -20, 0],
                    opacity: [0.3, 0.8, 0.3],
                    scale: [0.8, 1.2, 0.8]
                  }}
                  transition={{
                    duration: 3 + (i % 3),
                    repeat: Infinity,
                    delay: i * 0.5
                  }}
                >
                  <Sparkles className="w-3 h-3 text-white/40" />
                </motion.div>
              )
            })}
          </div>
        )}
      </motion.div>
    </AnimatePresence>
  )
}

export default SummerPromoBanner 