import React from 'react';

interface TikTokIconProps {
  className?: string;
  size?: number;
}

const TikTokIcon: React.FC<TikTokIconProps> = ({ className = "w-6 h-6", size }) => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M19.321 5.562a5.124 5.124 0 0 1-.443-.258 6.228 6.228 0 0 1-1.137-.966c-.849-.849-1.294-1.98-1.294-3.338h-3.239v14.19c0 1.457-1.183 2.64-2.64 2.64s-2.64-1.183-2.64-2.64 1.183-2.64 2.64-2.64c.291 0 .57.047.832.134V9.604a5.853 5.853 0 0 0-.832-.06c-3.235 0-5.86 2.625-5.86 5.86s2.625 5.86 5.86 5.86 5.86-2.625 5.86-5.86V9.321a9.46 9.46 0 0 0 5.453 1.706V7.788a6.221 6.221 0 0 1-2.56-2.226z"/>
    </svg>
  );
};

export default TikTokIcon;
