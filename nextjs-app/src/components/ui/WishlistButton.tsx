'use client';

import React, { useState } from 'react';
import { Heart } from 'lucide-react';
import { motion } from 'framer-motion';
import { useWishlist } from '../../contexts/WishlistContext';
import { useToast } from './Toast';

interface WishlistButtonProps {
  productId: string;
  productName?: string;
  variant?: 'default' | 'minimal' | 'large';
  className?: string;
  showText?: boolean;
}

const WishlistButton: React.FC<WishlistButtonProps> = ({
  productId,
  productName = 'Sản phẩm',
  variant = 'default',
  className = '',
  showText = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist();
  const { showToast } = useToast();

  const isInWishlistState = isInWishlist(productId);

  const handleWishlistToggle = async (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    setIsLoading(true);
    try {
      if (isInWishlistState) {
        await removeFromWishlist(productId);
        showToast(`${productName} đã được xóa khỏi danh sách yêu thích`, 'info', 'heart');
      } else {
        await addToWishlist(productId);
        showToast(`${productName} đã được thêm vào danh sách yêu thích`, 'success', 'heart');
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      showToast('Có lỗi xảy ra khi cập nhật danh sách yêu thích', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'minimal':
        return 'p-1.5 text-sm';
      case 'large':
        return 'p-3 text-lg';
      default:
        return 'p-2 text-base';
    }
  };

  const getIconSize = () => {
    switch (variant) {
      case 'minimal':
        return 'w-3 h-3';
      case 'large':
        return 'w-6 h-6';
      default:
        return 'w-4 h-4';
    }
  };

  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className={`
        inline-flex items-center justify-center rounded-full transition-all duration-300 
        ${isInWishlistState
          ? 'bg-red-500 text-white shadow-lg'
          : 'bg-white text-gray-600 hover:text-red-500 border border-gray-200 hover:border-red-300'
        }
        ${getVariantClasses()}
        ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-md'}
        ${className}
      `}
      onClick={handleWishlistToggle}
      disabled={isLoading}
      aria-label={`${isInWishlistState ? 'Xóa khỏi' : 'Thêm vào'} danh sách yêu thích`}
      title={`${isInWishlistState ? 'Xóa khỏi' : 'Thêm vào'} danh sách yêu thích`}
    >
      {isLoading ? (
        <div className={`animate-spin rounded-full border-2 border-current border-t-transparent ${getIconSize()}`} />
      ) : (
        <Heart className={`${getIconSize()} ${isInWishlistState ? 'fill-current' : ''}`} />
      )}
      
      {showText && (
        <span className="ml-2 text-sm font-medium">
          {isInWishlistState ? 'Đã yêu thích' : 'Yêu thích'}
        </span>
      )}
    </motion.button>
  );
};

export default WishlistButton; 