'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, X, AlertCircle, Info, Trash2, Edit, Plus } from 'lucide-react';

interface Toast {
  id: string;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  icon?: 'check' | 'edit' | 'delete' | 'add' | 'info';
  duration?: number;
}

interface AdminToastContextType {
  showToast: (message: string, type?: 'success' | 'error' | 'info' | 'warning', icon?: 'check' | 'edit' | 'delete' | 'add' | 'info', duration?: number) => void;
}

const AdminToastContext = createContext<AdminToastContextType | undefined>(undefined);

interface AdminToastProviderProps {
  children: ReactNode;
}

export function AdminToastProvider({ children }: AdminToastProviderProps) {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const showToast = (
    message: string, 
    type: 'success' | 'error' | 'info' | 'warning' = 'success', 
    icon: 'check' | 'edit' | 'delete' | 'add' | 'info' = 'check',
    duration: number = 4000
  ) => {
    const id = Math.random().toString(36).substr(2, 9);
    const toast: Toast = { id, message, type, icon, duration };
    
    setToasts(prev => [...prev, toast]);

    // Auto remove after duration
    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id));
    }, duration);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
  };

  const getIcon = (icon: string) => {
    switch (icon) {
      case 'edit':
        return <Edit className="w-5 h-5" />;
      case 'delete':
        return <Trash2 className="w-5 h-5" />;
      case 'add':
        return <Plus className="w-5 h-5" />;
      case 'info':
        return <Info className="w-5 h-5" />;
      default:
        return <Check className="w-5 h-5" />;
    }
  };

  const getToastStyles = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-gradient-to-r from-green-500 to-green-600 text-white border-green-600 shadow-green-200';
      case 'error':
        return 'bg-gradient-to-r from-red-500 to-red-600 text-white border-red-600 shadow-red-200';
      case 'warning':
        return 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white border-yellow-600 shadow-yellow-200';
      case 'info':
        return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-600 shadow-blue-200';
      default:
        return 'bg-gradient-to-r from-green-500 to-green-600 text-white border-green-600 shadow-green-200';
    }
  };

  return (
    <AdminToastContext.Provider value={{ showToast }}>
      {children}
      
      {/* Toast Container */}
      <div className="fixed top-4 right-4 z-[9999] space-y-2">
        <AnimatePresence>
          {toasts.map((toast) => (
            <motion.div
              key={toast.id}
              initial={{ opacity: 0, x: 300, scale: 0.3, rotateY: 90 }}
              animate={{
                opacity: 1,
                x: 0,
                scale: 1,
                rotateY: 0,
                transition: {
                  type: "spring",
                  stiffness: 300,
                  damping: 30
                }
              }}
              exit={{
                opacity: 0,
                x: 300,
                scale: 0.5,
                rotateY: -90,
                transition: { duration: 0.3 }
              }}
              whileHover={{ scale: 1.02 }}
              className={`
                relative min-w-[320px] max-w-md p-4 rounded-lg shadow-xl border-l-4 flex items-center space-x-3
                backdrop-blur-sm bg-opacity-95 cursor-pointer
                ${getToastStyles(toast.type)}
              `}
              onClick={() => removeToast(toast.id)}
            >
              <div className="flex-shrink-0">
                {getIcon(toast.icon || 'check')}
              </div>
              
              <div className="flex-1">
                <p className="text-sm font-medium leading-relaxed">{toast.message}</p>
              </div>

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  removeToast(toast.id);
                }}
                className="flex-shrink-0 ml-auto hover:opacity-70 transition-opacity p-1 rounded-full hover:bg-white hover:bg-opacity-20"
              >
                <X className="w-4 h-4" />
              </button>

              {/* Progress bar */}
              <motion.div
                className="absolute bottom-0 left-0 h-1 bg-white bg-opacity-30 rounded-b-lg"
                initial={{ width: "100%" }}
                animate={{ width: "0%" }}
                transition={{ duration: (toast.duration || 4000) / 1000, ease: "linear" }}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </AdminToastContext.Provider>
  );
}

export function useAdminToast() {
  const context = useContext(AdminToastContext);
  if (!context) {
    throw new Error('useAdminToast must be used within an AdminToastProvider');
  }
  return context;
} 