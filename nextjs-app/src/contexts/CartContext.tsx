'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { CartContextType, CartItemWithProduct } from '@/types'

const CartContext = createContext<CartContextType | undefined>(undefined)

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<CartItemWithProduct[]>([])
  const [loading, setLoading] = useState(false)

  // Hydrate variant data for cart items
  const hydrateVariantData = async (cartItems: CartItemWithProduct[]) => {
    const hydratedItems = await Promise.all(
      cartItems.map(async (item) => {
        if (item.variantId && !item.variant) {
          try {
            const productResponse = await fetch(`/api/products/by-id/${item.productId}`);
            if (productResponse.ok) {
              const productData = await productResponse.json();

              if (productData.success && productData.data.variants) {
                const variant = productData.data.variants.find((v: any) => v.id === item.variantId);

                if (variant) {
                  return { ...item, variant };
                }
              }
            }
          } catch (error) {
            console.error('Failed to fetch variant info for item:', item.id, error);
          }
        }
        return item;
      })
    );

    return hydratedItems;
  };

  // Load cart from localStorage on mount
  useEffect(() => {
    const loadCart = async () => {
      const savedCart = localStorage.getItem('cart')
      if (savedCart) {
        try {
          const cartItems = JSON.parse(savedCart);
          // Parse dates back to Date objects
          const parsedItems = cartItems.map((item: any) => ({
            ...item,
            createdAt: typeof item.createdAt === 'string' ? new Date(item.createdAt) : item.createdAt,
            updatedAt: typeof item.updatedAt === 'string' ? new Date(item.updatedAt) : item.updatedAt,
          }));
          // Hydrate variant data for items that have variantId but no variant
          const hydratedItems = await hydrateVariantData(parsedItems);
          setItems(hydratedItems);
        } catch (error) {
          console.error('Error loading cart from localStorage:', error)
        }
      }
    };

    loadCart();
  }, [])

  // Save cart to localStorage whenever items change
  useEffect(() => {
    // Create a serializable version of cart items
    const serializableItems = items.map(item => ({
      ...item,
      createdAt: item.createdAt instanceof Date ? item.createdAt.toISOString() : item.createdAt,
      updatedAt: item.updatedAt instanceof Date ? item.updatedAt.toISOString() : item.updatedAt,
    }));
    localStorage.setItem('cart', JSON.stringify(serializableItems))
  }, [items])

  const addItem = async (productId: string, quantity: number = 1, selectedSize?: string | null, selectedColor?: string | null) => {
    setLoading(true)
    try {
      // Check if product is already in cart with same variant, size, and color
      const existingItem = items.find(item =>
        item.productId === productId &&
        (item.variantId === selectedSize || (!item.variantId && !selectedSize)) &&
        (item.selectedColor === selectedColor || (!item.selectedColor && !selectedColor))
      )

      if (existingItem) {
        // Update quantity
        setItems(currentItems =>
          currentItems.map(item =>
            item.productId === productId &&
            (item.variantId === selectedSize || (!item.variantId && !selectedSize)) &&
            (item.selectedColor === selectedColor || (!item.selectedColor && !selectedColor))
              ? { ...item, quantity: item.quantity + quantity }
              : item
          )
        )
      } else {
        // Try to get product details from the specific product endpoint first
        let product = null
        
        try {
          // First try to get by ID directly (if that endpoint exists)
          const productResponse = await fetch(`/api/products/by-id/${productId}`)
          if (productResponse.ok) {
            const productData = await productResponse.json()
            if (productData.success && productData.data) {
              product = productData.data
            }
          }
        } catch {
          // Direct product fetch failed, trying general endpoint
        }
        
        // If direct fetch failed, try general products endpoint
        if (!product) {
          const productResponse = await fetch(`/api/products`)
          if (!productResponse.ok) throw new Error('Failed to fetch products')
          
          const responseData = await productResponse.json()
          
          // Handle different response structures
          let products = []
          if (responseData.data && Array.isArray(responseData.data)) {
            // For regular product listing: { data: [...] }
            products = responseData.data
          } else if (responseData.data && responseData.data.products && Array.isArray(responseData.data.products)) {
            // For bestselling products: { data: { products: [...] } }
            products = responseData.data.products
          } else {
            throw new Error('Invalid response structure')
          }
          
          product = products.find((p: { id: string }) => p.id === productId)
        }
        
        if (!product) {
          throw new Error('Product not found')
        }
        
        // Fetch variant information if selectedSize is provided
        let variant = null;
        if (selectedSize) {
          try {
            // If we already have product with variants, use it
            if (product.variants && product.variants.length > 0) {
              variant = product.variants.find((v: any) => v.id === selectedSize);
            } else {
              // Otherwise fetch from API
              const productResponse = await fetch(`/api/products/by-id/${productId}`);
              if (productResponse.ok) {
                const productData = await productResponse.json();
                if (productData.success && productData.data.variants) {
                  variant = productData.data.variants.find((v: any) => v.id === selectedSize);
                }
              }
            }
          } catch (error) {
            console.error('Failed to fetch variant info:', error);
          }
        }

        // Auto-select default size and color if not provided
        let finalSelectedSize = selectedSize;
        let finalSelectedColor = selectedColor;

        // If no size selected but product has sizes, use first size as default
        if (!finalSelectedSize && product.sizes && product.sizes.length > 0) {
          finalSelectedSize = product.sizes[0];
        }

        // If no color selected but product has colors, use first color as default
        if (!finalSelectedColor && product.colors && product.colors.length > 0) {
          finalSelectedColor = product.colors[0];
        }

        // Add new item
        const newItem: CartItemWithProduct = {
          id: `temp-${Date.now()}`,
          userId: 'guest', // Will be updated when user logs in
          productId,
          variantId: finalSelectedSize || null,
          selectedColor: finalSelectedColor || null,
          quantity,
          createdAt: new Date(),
          updatedAt: new Date(),
          product,
          variant
        }
        setItems(currentItems => [...currentItems, newItem])
      }
    } catch (error) {
      console.error('Error adding item to cart:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const removeItem = async (itemId: string) => {
    setItems(currentItems => currentItems.filter(item => item.id !== itemId))
  }

  const removeAllProductVariants = async (productId: string) => {
    setItems(currentItems => currentItems.filter(item => item.productId !== productId))
  }

  const updateQuantity = async (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      await removeItem(itemId)
      return
    }

    setItems(currentItems =>
      currentItems.map(item =>
        item.id === itemId
          ? { ...item, quantity, updatedAt: new Date() }
          : item
      )
    )
  }

  const updateItemSize = async (itemId: string, newSize: string) => {
    setItems(currentItems =>
      currentItems.map(item =>
        item.id === itemId
          ? { ...item, variantId: newSize, updatedAt: new Date() }
          : item
      )
    );
  }

  const updateItemColor = async (itemId: string, newColor: string) => {
    setItems(currentItems =>
      currentItems.map(item =>
        item.id === itemId
          ? { ...item, selectedColor: newColor, updatedAt: new Date() }
          : item
      )
    );
  }

  const clearCart = async () => {
    setItems([])
  }

  const refreshVariantData = async () => {
    const hydratedItems = await hydrateVariantData(items);
    setItems(hydratedItems);
  }

  const total = items.reduce((sum, item) => {
    const price = item.product.isOnSale && item.product.originalPrice 
      ? item.product.price 
      : item.product.price
    return sum + (price * item.quantity)
  }, 0)

  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0)

  const value: CartContextType = {
    items,
    addItem,
    removeItem,
    removeAllProductVariants,
    updateQuantity,
    updateItemSize,
    updateItemColor,
    clearCart,
    refreshVariantData,
    total,
    itemCount,
    loading
  }

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  )
}

export function useCart() {
  const context = useContext(CartContext)
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider')
  }
  return context
}
