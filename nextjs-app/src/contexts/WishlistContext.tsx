'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { WishlistContextType, WishlistItemWithProduct } from '@/types'

const WishlistContext = createContext<WishlistContextType | undefined>(undefined)

export function WishlistProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<WishlistItemWithProduct[]>([])
  const [loading, setLoading] = useState(false)
  const [userId, setUserId] = useState<string>('guest') // This will be updated when auth is implemented

  // Load wishlist on mount
  useEffect(() => {
    loadWishlist()
  }, [userId])

  // Save to localStorage for guest users whenever items change
  useEffect(() => {
    if (userId === 'guest') {
      localStorage.setItem('wishlist', JSON.stringify(items))
    }
  }, [items, userId])

  const loadWishlist = async () => {
    try {
      if (userId === 'guest') {
        // Load from localStorage for guest users
        const savedWishlist = localStorage.getItem('wishlist')
        if (savedWishlist) {
          try {
            setItems(JSON.parse(savedWishlist))
          } catch (error) {
            console.error('Error loading wishlist from localStorage:', error)
          }
        }
      } else {
        // Load from database for logged-in users
        const response = await fetch(`/api/wishlist?userId=${userId}`)
        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            setItems(result.data)
          }
        }
      }
    } catch (error) {
      console.error('Error loading wishlist:', error)
    }
  }

  const addItem = async (productId: string) => {
    if (isInWishlist(productId)) return

    setLoading(true)
    try {
      if (userId === 'guest') {
        // For guest users, add to localStorage only
        const response = await fetch(`/api/products/by-id/${productId}`)
        if (!response.ok) throw new Error('Failed to fetch product')
        
        const { data: product } = await response.json()
        
        const newItem: WishlistItemWithProduct = {
          id: `temp-${Date.now()}`,
          userId: 'guest',
          productId,
          createdAt: new Date(),
          product
        }

        setItems(currentItems => [...currentItems, newItem])
      } else {
        // For logged-in users, add to database
        const response = await fetch('/api/wishlist', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ productId, userId })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            setItems(currentItems => [...currentItems, result.data])
          } else {
            console.error('Failed to add to wishlist:', result.error)
          }
        } else {
          console.error('Failed to add to wishlist')
        }
      }
    } catch (error) {
      console.error('Error adding item to wishlist:', error)
    } finally {
      setLoading(false)
    }
  }

  const removeItem = async (productId: string) => {
    try {
      if (userId === 'guest') {
        // For guest users, remove from localStorage
        setItems(currentItems => currentItems.filter(item => item.productId !== productId))
      } else {
        // For logged-in users, remove from database
        const response = await fetch(`/api/wishlist/${productId}?userId=${userId}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            setItems(currentItems => currentItems.filter(item => item.productId !== productId))
          } else {
            console.error('Failed to remove from wishlist:', result.error)
          }
        } else {
          console.error('Failed to remove from wishlist')
        }
      }
    } catch (error) {
      console.error('Error removing item from wishlist:', error)
    }
  }

  const isInWishlist = (productId: string): boolean => {
    return items.some(item => item.productId === productId)
  }

  // This function can be called when user logs in to sync localStorage data to database
  const syncGuestWishlistToUser = async (newUserId: string) => {
    if (userId === 'guest' && items.length > 0) {
      setLoading(true)
      try {
        // Add each item to database
        for (const item of items) {
          await fetch('/api/wishlist', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ productId: item.productId, userId: newUserId })
          })
        }
        
        // Clear localStorage
        localStorage.removeItem('wishlist')
        
        // Update userId and reload from database
        setUserId(newUserId)
      } catch (error) {
        console.error('Error syncing wishlist:', error)
      } finally {
        setLoading(false)
      }
    } else {
      setUserId(newUserId)
    }
  }

  const value: WishlistContextType = {
    items,
    addItem,
    removeItem,
    isInWishlist,
    loading,
    syncGuestWishlistToUser
  }

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  )
}

export function useWishlist() {
  const context = useContext(WishlistContext)
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider')
  }
  return context
}
