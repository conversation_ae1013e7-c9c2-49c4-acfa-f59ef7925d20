'use client';

import { useState, useEffect, createContext, useContext } from 'react';

interface AdminUser {
  id: string;
  email: string;
  name: string | null;
  role: string;
}

interface AdminAuthContextType {
  user: AdminUser | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
}

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);

export function useAdminAuth() {
  const [user, setUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);

  const checkAuth = async () => {
    try {
      // Check if we have a token in localStorage as backup
      const localToken = typeof window !== 'undefined' ? localStorage.getItem('admin-token') : null;

      console.log('🔍 Auth check:', { hasLocalToken: !!localToken });

      const response = await fetch('/api/admin/auth/me', {
        credentials: 'include',
        headers: localToken ? {
          'Authorization': `Bearer ${localToken}`
        } : {}
      });

      console.log('🔍 Auth response:', response.status, response.ok);

      if (response.ok) {
        const result = await response.json();
        console.log('🔍 Auth result:', result);
        if (result.success) {
          setUser(result.data);
        } else {
          setUser(null);
          // Clear invalid token
          if (typeof window !== 'undefined') {
            localStorage.removeItem('admin-token');
          }
        }
      } else {
        setUser(null);
        // Clear invalid token
        if (typeof window !== 'undefined') {
          localStorage.removeItem('admin-token');
        }
      }
    } catch (error) {
      console.error('Auth check error:', error);
      setUser(null);
      // Clear invalid token
      if (typeof window !== 'undefined') {
        localStorage.removeItem('admin-token');
      }
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      console.log('🔐 Attempting login for:', email);

      const response = await fetch('/api/admin/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        credentials: 'include',
      });

      console.log('🔐 Login response:', response.status, response.ok);

      const result = await response.json();
      console.log('🔐 Login result:', result);

      if (result.success) {
        // API trả về result.data.admin, không phải result.data.user
        setUser(result.data.admin);

        // Store token in localStorage as backup
        if (result.data.token && typeof window !== 'undefined') {
          localStorage.setItem('admin-token', result.data.token);
          console.log('✅ Token stored in localStorage');
        }

        return { success: true };
      } else {
        return { success: false, error: result.message || result.error };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Lỗi kết nối' };
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/admin/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
    }
  };

  useEffect(() => {
    checkAuth();
  }, []);

  return {
    user,
    loading,
    login,
    logout,
    checkAuth,
  };
}
