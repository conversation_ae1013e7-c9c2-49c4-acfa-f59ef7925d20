'use client';

import { useEffect, useState, useCallback } from 'react';

interface AdminEvent {
  type: string;
  data?: any;
  message?: string;
  timestamp: string;
}

interface UseAdminEventsOptions {
  onNewOrder?: (order: any) => void;
  onOrderStatusUpdate?: (order: any) => void;
  onNewProduct?: (product: any) => void;
  onProductUpdate?: (product: any) => void;
  onNewCategory?: (category: any) => void;
  onCategoryUpdate?: (category: any) => void;
  onNewWishlistItem?: (item: any) => void;
  onLowStock?: (product: any) => void;
  onSystemAlert?: (alert: any) => void;
}

export function useAdminEvents(options: UseAdminEventsOptions = {}) {
  const [isConnected, setIsConnected] = useState(true); // Mock as connected
  const [lastEvent, setLastEvent] = useState<AdminEvent | null>(null);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  // Temporarily disabled - mock implementation
  useEffect(() => {
    // Set as connected immediately for now
    setIsConnected(true);
    setConnectionError(null);
    console.log('Admin events temporarily disabled - using mock');
  }, []);

  return {
    isConnected,
    lastEvent,
    connectionError,
  };
}

// Hook for showing notifications - temporarily disabled
export function useAdminNotifications() {
  const [notifications, setNotifications] = useState<AdminEvent[]>([]);

  const removeNotification = useCallback((timestamp: string) => {
    setNotifications(prev => prev.filter(n => n.timestamp !== timestamp));
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Temporarily disabled - no real-time events
  // useAdminEvents({ ... });

  return {
    notifications,
    removeNotification,
    clearNotifications,
  };
}
