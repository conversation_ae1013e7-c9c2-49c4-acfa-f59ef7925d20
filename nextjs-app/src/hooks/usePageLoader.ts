'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';

interface UsePageLoaderOptions {
  minLoadingTime?: number;
  showOnRouteChange?: boolean;
  showOnInitialLoad?: boolean;
  autoHide?: boolean;
}

interface RouterWithEvents {
  events?: {
    on: (event: string, handler: () => void) => void;
    off: (event: string, handler: () => void) => void;
  };
}

export function usePageLoader(options: UsePageLoaderOptions = {}) {
  const {
    minLoadingTime = 1500,
    showOnRouteChange = true,
    showOnInitialLoad = true,
    autoHide = true
  } = options;

  const [isLoading, setIsLoading] = useState(showOnInitialLoad);
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null);
  const router = useRouter();

  // Start loading
  const startLoading = useCallback(() => {
    setIsLoading(true);
    setLoadingStartTime(Date.now());
  }, []);

  // Stop loading with minimum time check
  const stopLoading = useCallback(() => {
    if (loadingStartTime) {
      const elapsed = Date.now() - loadingStartTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsed);
      
      setTimeout(() => {
        setIsLoading(false);
        setLoadingStartTime(null);
      }, remainingTime);
    } else {
      setIsLoading(false);
    }
  }, [loadingStartTime, minLoadingTime]);

  // Force stop loading immediately
  const forceStopLoading = useCallback(() => {
    setIsLoading(false);
    setLoadingStartTime(null);
  }, []);

  // Auto-hide loading after initial load
  useEffect(() => {
    if (showOnInitialLoad && autoHide) {
      const timer = setTimeout(() => {
        stopLoading();
      }, minLoadingTime);

      return () => clearTimeout(timer);
    }
  }, [showOnInitialLoad, autoHide, minLoadingTime, stopLoading]);

  // Handle route changes
  useEffect(() => {
    if (!showOnRouteChange) return;

    const handleRouteChangeStart = () => {
      startLoading();
    };

    const handleRouteChangeComplete = () => {
      stopLoading();
    };

    const handleRouteChangeError = () => {
      stopLoading();
    };

    // Note: Next.js 13+ App Router doesn't have router events
    // This is for compatibility with Pages Router
    const routerWithEvents = router as RouterWithEvents;
    if (typeof window !== 'undefined' && routerWithEvents.events) {
      routerWithEvents.events.on('routeChangeStart', handleRouteChangeStart);
      routerWithEvents.events.on('routeChangeComplete', handleRouteChangeComplete);
      routerWithEvents.events.on('routeChangeError', handleRouteChangeError);

      return () => {
        if (routerWithEvents.events) {
          routerWithEvents.events.off('routeChangeStart', handleRouteChangeStart);
          routerWithEvents.events.off('routeChangeComplete', handleRouteChangeComplete);
          routerWithEvents.events.off('routeChangeError', handleRouteChangeError);
        }
      };
    }
  }, [showOnRouteChange, startLoading, stopLoading, router]);

  return {
    isLoading,
    startLoading,
    stopLoading,
    forceStopLoading
  };
}

// Hook for component-level loading
export function useComponentLoader(initialLoading = false) {
  const [isLoading, setIsLoading] = useState(initialLoading);
  const [loadingMessage, setLoadingMessage] = useState<string>('');

  const startLoading = useCallback((message = 'Đang tải...') => {
    setLoadingMessage(message);
    setIsLoading(true);
  }, []);

  const stopLoading = useCallback(() => {
    setIsLoading(false);
    setLoadingMessage('');
  }, []);

  const updateMessage = useCallback((message: string) => {
    setLoadingMessage(message);
  }, []);

  return {
    isLoading,
    loadingMessage,
    startLoading,
    stopLoading,
    updateMessage
  };
}

// Hook for async operations with loading
export function useAsyncLoader<T = unknown>() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<T | null>(null);

  const execute = useCallback(async (
    asyncFunction: () => Promise<T>
  ) => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await asyncFunction();
      setData(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setIsLoading(false);
    setError(null);
    setData(null);
  }, []);

  return {
    isLoading,
    error,
    data,
    execute,
    reset
  };
}
