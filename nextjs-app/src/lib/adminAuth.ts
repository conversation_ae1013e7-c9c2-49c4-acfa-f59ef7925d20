import { NextRequest, NextResponse } from 'next/server';
import { validateAdminSession, isAdmin } from './auth';
import { User } from '@prisma/client';

export async function requireAdminAuth(request: NextRequest): Promise<{ user: User } | NextResponse> {
  try {
    const token = request.cookies.get('admin-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Không có token xác thực' },
        { status: 401 }
      );
    }

    const user = await validateAdminSession(token);

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Token không hợp lệ hoặc đã hết hạn' },
        { status: 401 }
      );
    }

    if (!isAdmin(user)) {
      return NextResponse.json(
        { success: false, error: 'Không có quyền truy cập admin' },
        { status: 403 }
      );
    }

    return { user };
  } catch (error) {
    console.error('Admin auth error:', error);
    return NextResponse.json(
      { success: false, error: 'Lỗi xác thực' },
      { status: 500 }
    );
  }
}

export function getClientIP(request: NextRequest): string {
  return request.headers.get('x-forwarded-for') || 
         request.headers.get('x-real-ip') || 
         'unknown';
}

export function getUserAgent(request: NextRequest): string {
  return request.headers.get('user-agent') || 'unknown';
}
