import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { NextRequest } from 'next/server';
import prisma from './db';

const JWT_SECRET = process.env.ADMIN_JWT_SECRET || 'fallback-secret';
const JWT_EXPIRES_IN = '24h';

export interface AdminTokenPayload {
  adminId: string;
  email: string;
  role: string;
}

export interface AuthUser {
  id: string;
  email: string;
  name: string | null;
  role: string;
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Generate JWT token for admin
export function generateToken(payload: AdminTokenPayload): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

// Verify JWT token
export function verifyToken(token: string): AdminTokenPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as AdminTokenPayload;
  } catch {
    return null;
  }
}

// Create admin session
export async function createAdminSession(adminId: string, token: string, request?: NextRequest): Promise<string> {
  const userAgent = request?.headers.get('user-agent') || '';
  const ipAddress = request?.headers.get('x-forwarded-for') ||
                   request?.headers.get('x-real-ip') ||
                   'unknown';

  await prisma.adminSession.create({
    data: {
      adminId,
      token,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      ipAddress,
      userAgent,
    },
  });

  return token;
}

// Get admin from request
export async function getAdminFromRequest(request: NextRequest) {
  try {
    const token = request.cookies.get('admin-token')?.value;

    if (!token) {
      return null;
    }

    const payload = verifyToken(token);
    if (!payload) {
      return null;
    }

    const admin = await prisma.adminUser.findUnique({
      where: { id: payload.adminId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        permissions: true,
        isActive: true,
      },
    });

    if (!admin || !admin.isActive) {
      return null;
    }

    return admin;
  } catch (error) {
    console.error('Error getting admin from request:', error);
    return null;
  }
}

// Validate admin session
export async function validateAdminSession(token: string) {
  try {
    const session = await prisma.adminSession.findUnique({
      where: { token },
      include: { admin: true },
    });

    if (!session || session.expiresAt < new Date()) {
      if (session) {
        await prisma.adminSession.delete({ where: { id: session.id } });
      }
      return null;
    }

    return session.admin;
  } catch {
    return null;
  }
}

export async function deleteAdminSession(token: string): Promise<void> {
  try {
    await prisma.adminSession.delete({ where: { token } });
  } catch {
    // Session might not exist
  }
}

export async function logAdminAction(
  adminId: string,
  action: string,
  resource: string,
  resourceId?: string,
  details?: object,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await prisma.adminLog.create({
    data: {
      adminId,
      action,
      resource,
      resourceId,
      details: details ? JSON.stringify(details) : null,
      ipAddress,
      userAgent,
    },
  });
}

export function isAdmin(user: AuthUser): boolean {
  const adminRoles = ['SUPER_ADMIN', 'ADMIN', 'MODERATOR'];
  return adminRoles.includes(user.role);
}

export function requireAdmin(user: AuthUser | null): void {
  if (!user || !isAdmin(user)) {
    throw new Error('Admin access required');
  }
}

// Verify admin authentication for API routes
export async function verifyAdminAuth(request: NextRequest): Promise<{
  success: boolean;
  admin?: AuthUser;
  error?: string;
}> {
  try {
    const admin = await getAdminFromRequest(request);

    if (!admin) {
      return {
        success: false,
        error: 'Authentication required'
      };
    }

    if (!isAdmin(admin)) {
      return {
        success: false,
        error: 'Admin access required'
      };
    }

    return {
      success: true,
      admin
    };
  } catch (error) {
    return {
      success: false,
      error: 'Authentication failed'
    };
  }
}
