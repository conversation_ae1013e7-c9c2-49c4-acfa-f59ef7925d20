// Utility functions for handling review data

export interface ReviewData {
  id: string;
  rating: number;
  comment: string;
  author: string;
  reviewTime: string;
  createdAt: string;
  product: {
    id: string;
    name: string;
    slug: string;
    image: string | null;
    category: string;
  };
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
}

// Generate avatar initials from name
export const getAvatarInitials = (name: string): string => {
  if (!name) return 'KH';
  
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

// Format review time to Vietnamese format
export const formatReviewTime = (timeString: string): string => {
  try {
    // If it's already a formatted string, return as is
    if (timeString.includes('/') || timeString.includes('-')) {
      return timeString;
    }
    
    // Try to parse as date
    const date = new Date(timeString);
    if (isNaN(date.getTime())) {
      return timeString;
    }
    
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    return timeString;
  }
};

// Truncate review comment to specified length
export const truncateComment = (comment: string, maxLength: number = 150): string => {
  if (!comment) return '';
  
  if (comment.length <= maxLength) {
    return comment;
  }
  
  // Find the last space before maxLength to avoid cutting words
  const truncated = comment.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > maxLength * 0.8) {
    return truncated.substring(0, lastSpace) + '...';
  }
  
  return truncated + '...';
};

// Validate review data
export const isValidReview = (review: any): boolean => {
  return (
    review &&
    review.id &&
    review.rating &&
    review.rating >= 1 &&
    review.rating <= 5 &&
    review.comment &&
    review.comment.trim().length > 0 &&
    review.author &&
    review.product &&
    review.product.name
  );
};

// Filter reviews by quality
export const filterQualityReviews = (reviews: any[]): any[] => {
  return reviews.filter(review => {
    if (!isValidReview(review)) return false;
    
    const commentLength = review.comment?.trim().length || 0;
    
    // Filter out very short or very long comments
    if (commentLength < 10 || commentLength > 500) return false;
    
    // Filter out comments that are just ratings without text
    const comment = review.comment.toLowerCase();
    const lowQualityPhrases = [
      'good', 'bad', 'ok', 'nice', 'great', 'tốt', 'hay', 'đẹp', 'ổn'
    ];
    
    if (lowQualityPhrases.includes(comment.trim())) return false;
    
    return true;
  });
};

// Sort reviews by relevance and quality
export const sortReviewsByQuality = (reviews: any[]): any[] => {
  return reviews.sort((a, b) => {
    // First, sort by rating (higher is better)
    if (a.rating !== b.rating) {
      return b.rating - a.rating;
    }
    
    // Then by comment length (moderate length is better)
    const aLength = a.comment?.length || 0;
    const bLength = b.comment?.length || 0;
    
    const idealLength = 100;
    const aDiff = Math.abs(aLength - idealLength);
    const bDiff = Math.abs(bLength - idealLength);
    
    if (aDiff !== bDiff) {
      return aDiff - bDiff;
    }
    
    // Finally by date (newer is better)
    const aDate = new Date(a.createdAt || a.reviewTime);
    const bDate = new Date(b.createdAt || b.reviewTime);
    
    return bDate.getTime() - aDate.getTime();
  });
};

// Parse image data from different formats
export const parseProductImage = (images: any): string | null => {
  if (!images) return null;
  
  try {
    // If it's already an array
    if (Array.isArray(images)) {
      return images.length > 0 ? images[0] : null;
    }
    
    // If it's a JSON string
    if (typeof images === 'string') {
      try {
        const parsed = JSON.parse(images);
        if (Array.isArray(parsed)) {
          return parsed.length > 0 ? parsed[0] : null;
        }
        return parsed || null;
      } catch {
        // If it's just a string URL
        return images;
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing product image:', error);
    return null;
  }
};

// Generate review statistics
export const calculateReviewStats = (reviews: any[]): ReviewStats => {
  if (!reviews || reviews.length === 0) {
    return {
      totalReviews: 0,
      averageRating: 0
    };
  }
  
  const validReviews = reviews.filter(review => 
    review.rating && review.rating >= 1 && review.rating <= 5
  );
  
  if (validReviews.length === 0) {
    return {
      totalReviews: reviews.length,
      averageRating: 0
    };
  }
  
  const totalRating = validReviews.reduce((sum, review) => sum + review.rating, 0);
  const averageRating = totalRating / validReviews.length;
  
  return {
    totalReviews: reviews.length,
    averageRating: Math.round(averageRating * 10) / 10
  };
};

// Format review data for display
export const formatReviewForDisplay = (review: any): ReviewData | null => {
  if (!isValidReview(review)) return null;
  
  return {
    id: review.id,
    rating: review.rating,
    comment: truncateComment(review.comment, 200),
    author: review.author || review.user?.name || 'Khách hàng',
    reviewTime: formatReviewTime(review.reviewTime || review.createdAt),
    createdAt: review.createdAt,
    product: {
      id: review.product.id,
      name: review.product.name,
      slug: review.product.slug,
      image: parseProductImage(review.product.images),
      category: review.product.category?.name || 'Sản phẩm'
    }
  };
};

// Get featured reviews with quality filtering
export const getFeaturedReviews = (reviews: any[], limit: number = 6): ReviewData[] => {
  const qualityReviews = filterQualityReviews(reviews);
  const sortedReviews = sortReviewsByQuality(qualityReviews);
  
  return sortedReviews
    .slice(0, limit)
    .map(formatReviewForDisplay)
    .filter((review): review is ReviewData => review !== null);
};
