/* Elegant Design System - Inspired by sodopeclub.vn */

/* Elegant typography */
.elegant-title {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 700;
  letter-spacing: -0.01em;
  line-height: 1.2;
  color: #1a1a1a;
}

.elegant-subtitle {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  letter-spacing: 0.01em;
  color: #2d2d2d;
}

.elegant-body {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: #4a4a4a;
}

/* Elegant color palette */
.elegant-primary {
  background-color: #1a1a1a;
  color: #ffffff;
}

.elegant-secondary {
  background-color: #f8f9fa;
  color: #1a1a1a;
}

.elegant-accent {
  background-color: #2563eb;
  color: #ffffff;
}

.elegant-muted {
  background-color: #6b7280;
  color: #ffffff;
}

/* Elegant animations */
@keyframes elegant-fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes elegant-scale {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes elegant-slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.elegant-fade-in {
  animation: elegant-fade-in 0.6s ease-out;
}

.elegant-scale {
  animation: elegant-scale 2s ease-in-out infinite;
}

.elegant-slide-up {
  animation: elegant-slide-up 0.4s ease-out;
}

/* Elegant buttons */
.elegant-button-primary {
  background-color: #1a1a1a;
  border: 1px solid #1a1a1a;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  padding: 12px 24px;
  font-size: 14px;
  letter-spacing: 0.01em;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.elegant-button-primary:hover {
  background-color: #2d2d2d;
  border-color: #2d2d2d;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.elegant-button-secondary {
  background-color: transparent;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #374151;
  font-weight: 500;
  padding: 12px 24px;
  font-size: 14px;
  letter-spacing: 0.01em;
  transition: all 0.2s ease;
}

.elegant-button-secondary:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.elegant-button-outline {
  background-color: transparent;
  border: 1px solid #1a1a1a;
  border-radius: 8px;
  color: #1a1a1a;
  font-weight: 500;
  padding: 12px 24px;
  font-size: 14px;
  letter-spacing: 0.01em;
  transition: all 0.2s ease;
}

.elegant-button-outline:hover {
  background-color: #1a1a1a;
  color: white;
  transform: translateY(-1px);
}

/* Elegant cards */
.elegant-card {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.elegant-card:hover {
  border-color: #d1d5db;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Elegant badges */
.elegant-badge {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.elegant-badge-primary {
  background-color: #1a1a1a;
  border-color: #1a1a1a;
  color: white;
}

.elegant-badge-accent {
  background-color: #dbeafe;
  border-color: #bfdbfe;
  color: #1e40af;
}

/* Elegant color palette */
.elegant-color-primary { color: #1a1a1a; }
.elegant-color-secondary { color: #4a4a4a; }
.elegant-color-muted { color: #6b7280; }
.elegant-color-accent { color: #2563eb; }
.elegant-color-success { color: #059669; }
.elegant-color-warning { color: #d97706; }
.elegant-color-error { color: #dc2626; }

.elegant-bg-primary { background-color: #1a1a1a; }
.elegant-bg-secondary { background-color: #f8f9fa; }
.elegant-bg-muted { background-color: #f3f4f6; }
.elegant-bg-accent { background-color: #2563eb; }
.elegant-bg-success { background-color: #059669; }
.elegant-bg-warning { background-color: #d97706; }
.elegant-bg-error { background-color: #dc2626; }

/* Elegant text effects */
.elegant-text-shadow {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.elegant-text-subtle {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Responsive elegant typography */
@media (max-width: 768px) {
  .elegant-title {
    font-size: 2rem;
    line-height: 1.2;
  }

  .elegant-subtitle {
    font-size: 1.125rem;
    line-height: 1.4;
  }

  .elegant-body {
    font-size: 0.875rem;
    line-height: 1.5;
  }
}

@media (min-width: 769px) {
  .elegant-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .elegant-subtitle {
    font-size: 1.25rem;
    line-height: 1.4;
  }

  .elegant-body {
    font-size: 1rem;
    line-height: 1.6;
  }
}

@media (min-width: 1024px) {
  .elegant-title {
    font-size: 3rem;
    line-height: 1.1;
  }

  .elegant-subtitle {
    font-size: 1.5rem;
    line-height: 1.3;
  }

  .elegant-body {
    font-size: 1.125rem;
    line-height: 1.7;
  }
}

/* Korean-style loading animations */
.k-loading-dots {
  display: inline-flex;
  gap: 4px;
}

.k-loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: k-loading-bounce 1.4s ease-in-out infinite both;
}

.k-loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.k-loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes k-loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Elegant glass effects */
.elegant-glass {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.elegant-glass-dark {
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Responsive Cart Utilities */
.cart-item-mobile {
  @apply flex flex-col space-y-3;
}

.cart-item-desktop {
  @apply hidden md:flex md:items-center md:space-x-6 md:space-y-0;
}

.cart-quantity-mobile {
  @apply flex items-center justify-center w-10 h-10 min-w-[44px] min-h-[44px];
}

.cart-quantity-desktop {
  @apply flex items-center justify-center w-12 h-12 min-w-[48px] min-h-[48px];
}

/* Touch-friendly buttons */
.touch-target {
  min-width: 44px;
  min-height: 44px;
}

.touch-target-large {
  min-width: 48px;
  min-height: 48px;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Elegant Product Cards */
.elegant-product-card {
  @apply relative overflow-hidden transition-all duration-300;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.elegant-product-card:hover {
  @apply transform -translate-y-1;
  border-color: #d1d5db;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.elegant-product-image {
  @apply transition-all duration-300;
}

.elegant-product-card:hover .elegant-product-image {
  @apply scale-105;
}

.elegant-product-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-md;
  background-color: #1a1a1a;
  color: white;
}

.elegant-product-badge-sale {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-md;
  background-color: #dc2626;
  color: white;
}

.elegant-product-badge-new {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-md;
  background-color: #059669;
  color: white;
}

.elegant-product-price {
  @apply text-lg font-bold;
  color: #1a1a1a;
}

.elegant-product-price-sale {
  @apply text-lg font-bold;
  color: #dc2626;
}

.elegant-product-button {
  @apply w-full py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 min-h-[44px];
  background-color: #1a1a1a;
  color: white;
  border: 1px solid #1a1a1a;
}

.elegant-product-button:hover {
  @apply transform -translate-y-0.5;
  background-color: #2d2d2d;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.elegant-product-button-secondary {
  @apply w-full py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 min-h-[44px];
  background-color: transparent;
  border: 1px solid #d1d5db;
  color: #374151;
}

.elegant-product-button-secondary:hover {
  @apply transform -translate-y-0.5;
  background-color: #f9fafb;
  border-color: #9ca3af;
}

/* K-Fashion floating animations */
@keyframes k-product-float {
  0%, 100% {
    transform: translateY(0px) scale(0.5);
    opacity: 0;
  }
  50% {
    transform: translateY(-20px) scale(1);
    opacity: 0.6;
  }
}

.k-product-float {
  animation: k-product-float 3s ease-in-out infinite;
}

/* K-Fashion specific responsive breakpoints */
@media (max-width: 640px) {
  .k-cart-mobile {
    padding: 1rem;
  }

  .k-cart-item-spacing {
    gap: 0.75rem;
  }

  .k-cart-text-mobile {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .k-product-card {
    @apply rounded-2xl;
  }

  .k-product-price {
    @apply text-lg;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .k-cart-tablet {
    padding: 1.5rem;
  }

  .k-cart-item-spacing {
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .k-cart-desktop {
    padding: 2rem;
  }

  .k-cart-item-spacing {
    gap: 1.5rem;
  }
}

/* K-Fashion cart animations */
@keyframes cart-item-enter {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes cart-bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.cart-item-enter {
  animation: cart-item-enter 0.5s ease-out;
}

.cart-bounce {
  animation: cart-bounce 0.3s ease-in-out;
}

/* Elegant quantity controls */
.elegant-quantity-control {
  @apply flex items-center bg-white border border-gray-300 rounded-lg overflow-hidden;
}

.elegant-quantity-button {
  @apply flex items-center justify-center hover:bg-gray-50 transition-colors;
  border: none;
  background: transparent;
}

.elegant-quantity-display {
  @apply text-center font-medium border-x border-gray-300;
  background-color: #f9fafb;
}

/* Mobile-specific quantity controls */
@media (max-width: 768px) {
  .elegant-quantity-control {
    @apply text-sm;
  }

  .elegant-quantity-button {
    @apply w-10 h-10;
  }

  .elegant-quantity-display {
    @apply px-4 py-2 min-w-[60px];
  }
}

/* Desktop-specific quantity controls */
@media (min-width: 769px) {
  .elegant-quantity-control {
    @apply text-base;
  }

  .elegant-quantity-button {
    @apply w-12 h-12;
  }

  .elegant-quantity-display {
    @apply px-6 py-3 min-w-[80px];
  }
}
