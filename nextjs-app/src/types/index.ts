import { Product, Category, User, Order, OrderItem, Review, CartItem, WishlistItem, ProductCategory } from '@prisma/client'

export type ProductWithCategory = Product & {
  category: Category
  reviews: Review[]
  _count: {
    reviews: number
  }
}

// Enhanced types for many-to-many relationships
export type ProductWithCategories = Product & {
  categories: Category[]
  category?: Category // For backward compatibility
  productCategories: (ProductCategory & {
    category: Category
  })[]
  reviews: (Review & {
    user?: {
      name: string | null
      email: string
    } | null
  })[]
  _count: {
    reviews: number
    wishlistItems: number
  }
}

export type CategoryWithProducts = Category & {
  productCategories: (ProductCategory & {
    product: Product
  })[]
  _count: {
    productCategories: number
  }
}

export type ProductWithCategoryAndReviews = Product & {
  category: Category
  variants?: {
    id: string
    name: string
    attributes: any
    price?: number
    stock: number
  }[]
  reviews: (Review & {
    user: {
      name: string | null
      email: string
    }
  })[]
  _count: {
    reviews: number
  }
}

export type CartItemWithProduct = CartItem & {
  product: ProductWithCategory
  variant?: {
    id: string
    name: string
    attributes: any
    price?: number
    stock: number
  } | null
  selectedColor?: string | null
}

export type WishlistItemWithProduct = WishlistItem & {
  product: ProductWithCategory
}

export type OrderWithItems = Order & {
  orderItems: (OrderItem & {
    product: Product
  })[]
  user: User
}

export type ReviewWithUser = Review & {
  user: {
    name: string | null
    email: string
  }
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface ProductFilters {
  category?: string
  minPrice?: number
  maxPrice?: number
  search?: string
  featured?: boolean
  onSale?: boolean
  sortBy?: 'name' | 'price' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

export interface CartContextType {
  items: CartItemWithProduct[]
  addItem: (productId: string, quantity?: number, selectedSize?: string | null, selectedColor?: string | null) => Promise<void>
  removeItem: (itemId: string) => Promise<void>
  removeAllProductVariants: (productId: string) => Promise<void>
  updateQuantity: (itemId: string, quantity: number) => Promise<void>
  updateItemSize: (itemId: string, newSize: string) => Promise<void>
  updateItemColor: (itemId: string, newColor: string) => Promise<void>
  clearCart: () => Promise<void>
  refreshVariantData: () => Promise<void>
  total: number
  itemCount: number
  loading: boolean
}

export interface WishlistContextType {
  items: WishlistItemWithProduct[]
  addItem: (productId: string) => Promise<void>
  removeItem: (productId: string) => Promise<void>
  isInWishlist: (productId: string) => boolean
  loading: boolean
  syncGuestWishlistToUser?: (newUserId: string) => Promise<void>
}

export interface AuthContextType {
  user: User | null
  login: (email: string, password: string) => Promise<void>
  register: (email: string, password: string, name?: string) => Promise<void>
  logout: () => void
  loading: boolean
}

// Admin-specific types
export interface AdminProductFormData {
  name: string
  slug: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  stock: number
  sku?: string
  images: string[]
  categoryIds: string[] // Multiple category IDs
  colors: string[]
  sizes: string[]
  featured: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT'
  tags: string[]
  seoTitle?: string
  seoDescription?: string
  seoKeywords: string[]
}

export interface AdminProductEditData extends AdminProductFormData {
  id: string
  originalId?: string
}

export interface ImportedReview {
  id: string
  rating: number
  comment?: string
  author?: string
  reviewTime?: string
  images: string[]
  isApproved: boolean
  createdAt: Date
}
