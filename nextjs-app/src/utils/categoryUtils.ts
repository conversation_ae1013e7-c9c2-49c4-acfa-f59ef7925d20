interface Category {
  id: string;
  name: string;
  slug: string;
  level: number;
  parentId: string | null;
  isActive?: boolean;
  sortOrder?: number;
}

interface CategoryOption {
  id: string;
  name: string;
  displayName: string;
  level: number;
}

interface FormData {
  name: string;
  slug: string;
  sku: string;
  categoryId: string;
  price: number;
  stock: number;
  status: string;
  description?: string;
}

/**
 * Prepare category options for dropdown with hierarchical display
 * @param categories - Array of categories from database
 * @returns Array of category options with displayName including indentation
 */
export const prepareCategoryOptions = (categories: Category[]): CategoryOption[] => {
  if (!Array.isArray(categories)) {
    return [];
  }

  // Filter only active categories and sort them
  const activeCategories = categories
    .filter(category => category.isActive !== false)
    .sort((a, b) => {
      // Sort by level first, then by sortOrder, then by name
      if (a.level !== b.level) {
        return a.level - b.level;
      }
      if (a.sortOrder && b.sortOrder && a.sortOrder !== b.sortOrder) {
        return a.sortOrder - b.sortOrder;
      }
      return a.name.localeCompare(b.name);
    });

  return activeCategories.map(category => {
    const indentation = '—'.repeat(category.level);
    const displayName = category.level > 0 ? `${indentation} ${category.name}` : category.name;
    
    return {
      id: category.id,
      name: category.name,
      displayName,
      level: category.level
    };
  });
};

/**
 * Generate URL-friendly slug from Vietnamese text
 * @param text - Input text in Vietnamese
 * @returns URL-friendly slug
 */
export const generateSlug = (text: string): string => {
  if (!text) return '';
  
  return text
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .replace(/đ/g, 'd')
    .replace(/Đ/g, 'D')
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

/**
 * Generate unique SKU with timestamp and random component
 * @param prefix - SKU prefix (default: 'TL')
 * @returns Generated SKU
 */
export const generateSKU = (prefix: string = 'TL'): string => {
  const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}${timestamp}${random}`;
};

/**
 * Validate product form data
 * @param formData - Form data to validate
 * @param slugAvailable - Whether slug is available (null = not checked)
 * @param skuAvailable - Whether SKU is available (null = not checked)
 * @returns Object with validation errors
 */
export const validateProductForm = (
  formData: FormData,
  slugAvailable: boolean | null,
  skuAvailable: boolean | null
): { [key: string]: string } => {
  const errors: { [key: string]: string } = {};

  // Required field validations
  if (!formData.name?.trim()) {
    errors.name = 'Tên sản phẩm là bắt buộc';
  }

  if (!formData.slug?.trim()) {
    errors.slug = 'Slug là bắt buộc';
  } else if (slugAvailable === false) {
    errors.slug = 'Slug đã tồn tại, vui lòng chọn slug khác';
  }

  if (!formData.sku?.trim()) {
    errors.sku = 'SKU là bắt buộc';
  } else if (skuAvailable === false) {
    errors.sku = 'SKU đã tồn tại, vui lòng chọn SKU khác';
  }

  if (!formData.categoryId) {
    errors.categoryId = 'Danh mục là bắt buộc';
  }

  if (!formData.price || formData.price <= 0) {
    errors.price = 'Giá bán là bắt buộc và phải lớn hơn 0';
  }

  if (formData.stock < 0) {
    errors.stock = 'Số lượng tồn kho là bắt buộc và không được âm';
  }

  if (!formData.status) {
    errors.status = 'Trạng thái là bắt buộc';
  }

  // Optional description validation
  if (formData.description !== undefined && !formData.description?.trim()) {
    errors.description = 'Mô tả là bắt buộc';
  }

  return errors;
};

/**
 * Validate image file
 * @param file - File to validate
 * @returns Object with validation result
 */
export const validateImageFile = (file: File): { valid: boolean; error?: string } => {
  const maxSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];

  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File ${file.name} quá lớn. Kích thước tối đa là 5MB.`
    };
  }

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `File ${file.name} không đúng định dạng. Chỉ chấp nhận JPG, PNG, WEBP, GIF.`
    };
  }

  return { valid: true };
};

/**
 * Format price to Vietnamese currency
 * @param price - Price number
 * @returns Formatted price string
 */
export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(price);
};

/**
 * Parse price from string (remove non-numeric characters except decimal point)
 * @param priceString - Price as string
 * @returns Parsed price number
 */
export const parsePrice = (priceString: string): number => {
  const numericValue = priceString.replace(/[^0-9.]/g, '');
  return numericValue === '' ? 0 : parseFloat(numericValue) || 0;
};

/**
 * Validate slug format
 * @param slug - Slug to validate
 * @returns Whether slug is valid format
 */
export const isValidSlug = (slug: string): boolean => {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
};

/**
 * Validate SKU format
 * @param sku - SKU to validate
 * @returns Whether SKU is valid format
 */
export const isValidSKU = (sku: string): boolean => {
  const skuRegex = /^[A-Z0-9-_]+$/;
  return skuRegex.test(sku);
};
