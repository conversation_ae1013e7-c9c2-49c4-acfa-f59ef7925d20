import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: "#f97316",
        secondary: "#ffffff",
        accent: "#f8fafc",
        text: {
          primary: "#000000",
          secondary: "#666666",
          light: "#999999",
        },
        border: "#e7e7e7",
      },
      fontFamily: {
        'sans': ['Arial', 'Helvetica', 'sans-serif'],
        'arial': ['Arial', 'Helvetica', 'sans-serif'],
      },
      fontSize: {
        'hero': ['3.75rem', { lineHeight: '1.2em' }],
        'section': ['1.875rem', { lineHeight: '1.3em' }],
        'subsection': ['1.5rem', { lineHeight: '1.3em' }],
      },
      container: {
        center: true,
        padding: '1rem',
        screens: {
          sm: '640px',
          md: '768px',
          lg: '1024px',
          xl: '1240px',
        },
      },
    },
  },
  plugins: [],
};
export default config;
